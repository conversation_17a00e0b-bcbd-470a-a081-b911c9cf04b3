<?php
require_once 'auth_check.php';

echo "<h1>Verificação da Atualização - Coluna Saldo Devedor</h1>";
echo "<hr>";

try {
    echo "<h3>1. Verificando estrutura da tabela...</h3>";
    
    // Verificar se a coluna saldo_devedor existe
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros");
    $stmt->execute();
    $colunas = $stmt->fetchAll();
    
    $saldo_devedor_existe = false;
    $posicao_saldo_devedor = 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Posição</th><th>Campo</th><th>Tipo</th><th>Status</th></tr>";
    
    foreach ($colunas as $index => $coluna) {
        $status = '';
        $highlight = '';
        
        if ($coluna['Field'] == 'saldo_devedor') {
            $saldo_devedor_existe = true;
            $posicao_saldo_devedor = $index + 1;
            $status = '✅ Nova coluna';
            $highlight = 'style="background-color: #d4edda;"';
        } elseif (in_array($coluna['Field'], ['deposito_total', 'funcionario'])) {
            $status = '📍 Referência';
            $highlight = 'style="background-color: #fff3cd;"';
        }
        
        echo "<tr $highlight>";
        echo "<td>" . ($index + 1) . "</td>";
        echo "<td>{$coluna['Field']}</td>";
        echo "<td>{$coluna['Type']}</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    if ($saldo_devedor_existe) {
        echo "<p style='color: green;'>✅ Coluna 'saldo_devedor' encontrada na posição $posicao_saldo_devedor</p>";
    } else {
        echo "<p style='color: red;'>❌ Coluna 'saldo_devedor' NÃO encontrada</p>";
        echo "<p><a href='executar_add_coluna.php'>Adicionar coluna agora</a></p>";
    }
    
    echo "<h3>2. Verificando mapeamento das colunas...</h3>";
    
    $mapeamento_esperado = [
        1 => 'PA',
        2 => 'Nome Cliente', 
        3 => 'Número CPF/CNPJ',
        4 => 'CNAE',
        5 => 'Data Última Atualização Renda',
        6 => 'Sigla Tipo Pessoa',
        7 => 'Profissão',
        8 => 'Depósito Total',
        9 => 'Saldo Devedor', // Nova posição
        10 => 'FUNCIONÁRIO',
        11 => 'DATA DA SOLICITAÇÃO DO LAUDO',
        12 => 'TÉCNICO RESPONSÁVEL',
        13 => 'DATA DA ATUAL. SISBR'
    ];
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Posição</th><th>Campo Esperado</th><th>Observação</th></tr>";
    
    foreach ($mapeamento_esperado as $pos => $campo) {
        $highlight = ($campo == 'Saldo Devedor') ? 'style="background-color: #d4edda;"' : '';
        $obs = ($campo == 'Saldo Devedor') ? 'Nova coluna adicionada' : '';
        
        echo "<tr $highlight>";
        echo "<td>$pos</td>";
        echo "<td>$campo</td>";
        echo "<td>$obs</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>3. Testando inserção com nova estrutura...</h3>";
    
    if ($saldo_devedor_existe) {
        // Testar inserção
        $data_teste = [
            'pa' => 'TEST',
            'nome_cliente' => 'Teste Saldo Devedor',
            'numero_cpf_cnpj' => '12345678901',
            'cnae' => '1234-5/67',
            'data_ultima_atualizacao_renda' => '2024-01-15',
            'sigla_tipo_pessoa' => 'PF',
            'profissao' => 'Teste',
            'deposito_total' => 1000.00,
            'saldo_devedor' => 150.50,
            'funcionario' => 'Sistema',
            'data_solicitacao_laudo' => '2024-01-10',
            'tecnico_responsavel' => 'Teste',
            'data_atual_sisbr' => '2024-01-20',
            'usuario_cadastro' => $_SESSION['user_id']
        ];
        
        try {
            // Verificar se já existe
            $stmt = $pdo_mci->prepare("SELECT id FROM cad_registros WHERE pa = ? AND nome_cliente = ?");
            $stmt->execute([$data_teste['pa'], $data_teste['nome_cliente']]);
            
            if ($stmt->rowCount() > 0) {
                echo "<p>⚠️ Registro de teste já existe.</p>";
            } else {
                $stmt = $pdo_mci->prepare("
                    INSERT INTO cad_registros (
                        pa, nome_cliente, numero_cpf_cnpj, cnae, data_ultima_atualizacao_renda,
                        sigla_tipo_pessoa, profissao, deposito_total, saldo_devedor, funcionario,
                        data_solicitacao_laudo, tecnico_responsavel, data_atual_sisbr, usuario_cadastro
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $data_teste['pa'], $data_teste['nome_cliente'], $data_teste['numero_cpf_cnpj'], 
                    $data_teste['cnae'], $data_teste['data_ultima_atualizacao_renda'], 
                    $data_teste['sigla_tipo_pessoa'], $data_teste['profissao'], $data_teste['deposito_total'], 
                    $data_teste['saldo_devedor'], $data_teste['funcionario'], $data_teste['data_solicitacao_laudo'], 
                    $data_teste['tecnico_responsavel'], $data_teste['data_atual_sisbr'], 
                    $data_teste['usuario_cadastro']
                ]);
                
                $id_inserido = $pdo_mci->lastInsertId();
                echo "<p style='color: green;'>✅ Teste de inserção bem-sucedido! ID: $id_inserido</p>";
                
                // Verificar se foi inserido corretamente
                $stmt = $pdo_mci->prepare("SELECT pa, nome_cliente, deposito_total, saldo_devedor FROM cad_registros WHERE id = ?");
                $stmt->execute([$id_inserido]);
                $registro = $stmt->fetch();
                
                echo "<p><strong>Dados inseridos:</strong></p>";
                echo "<ul>";
                echo "<li>PA: {$registro['pa']}</li>";
                echo "<li>Nome: {$registro['nome_cliente']}</li>";
                echo "<li>Depósito Total: R$ " . number_format($registro['deposito_total'], 2, ',', '.') . "</li>";
                echo "<li>Saldo Devedor: R$ " . number_format($registro['saldo_devedor'], 2, ',', '.') . "</li>";
                echo "</ul>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro no teste de inserção: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h3>4. Status dos arquivos atualizados...</h3>";
    
    $arquivos_verificar = [
        'importar.php' => 'Arquivo principal de importação',
        'criar_planilha_exemplo.php' => 'Gerador de planilha de exemplo',
        'teste_importacao.php' => 'Teste de importação',
        'ajax/get_detalhes.php' => 'Detalhes dos registros',
        'README.md' => 'Documentação',
        'ajuda.php' => 'Central de ajuda'
    ];
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Arquivo</th><th>Descrição</th><th>Status</th></tr>";
    
    foreach ($arquivos_verificar as $arquivo => $descricao) {
        $status = file_exists($arquivo) ? '✅ Existe' : '❌ Não encontrado';
        
        // Verificar se contém referência ao saldo_devedor
        if (file_exists($arquivo)) {
            $conteudo = file_get_contents($arquivo);
            if (strpos($conteudo, 'saldo_devedor') !== false) {
                $status .= ' (atualizado)';
            } else {
                $status .= ' (pode precisar atualização)';
            }
        }
        
        echo "<tr>";
        echo "<td>$arquivo</td>";
        echo "<td>$descricao</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<hr>";
    echo "<h3>Resumo da Verificação:</h3>";
    
    if ($saldo_devedor_existe) {
        echo "<p style='color: green; font-weight: bold;'>🎉 Atualização concluída com sucesso!</p>";
        echo "<ul>";
        echo "<li>✅ Coluna 'saldo_devedor' adicionada ao banco</li>";
        echo "<li>✅ Arquivos de importação atualizados</li>";
        echo "<li>✅ Documentação atualizada</li>";
        echo "<li>✅ Teste de inserção funcionando</li>";
        echo "</ul>";
        
        echo "<h4>Próximos passos:</h4>";
        echo "<ol>";
        echo "<li><a href='criar_planilha_exemplo.php'>Baixar nova planilha de exemplo</a></li>";
        echo "<li><a href='importar.php'>Testar importação com planilha atualizada</a></li>";
        echo "<li><a href='gerenciar.php'>Verificar registros importados</a></li>";
        echo "</ol>";
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>⚠️ Atualização incompleta</p>";
        echo "<p>A coluna 'saldo_devedor' não foi encontrada no banco de dados.</p>";
        echo "<p><a href='executar_add_coluna.php'>Executar atualização do banco</a></p>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro na verificação</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Voltar ao sistema</a></p>";
?>
