<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';
require_once '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

$logger = new MciLogger();
$message = '';
$error = '';

// Função para converter datas do Excel
function convertExcelDate($value) {
    if (empty($value)) {
        return null;
    }

    // Se for um número (data do Excel)
    if (is_numeric($value)) {
        try {
            $date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($value);
            return $date->format('Y-m-d');
        } catch (Exception $e) {
            return null;
        }
    }

    // Se for uma string, tentar converter
    if (is_string($value)) {
        $value = trim($value);
        if (empty($value)) {
            return null;
        }

        // FORMATO BRASILEIRO: dd/mm/yyyy (PRIORIDADE MÁXIMA)
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $value, $matches)) {
            $dia = intval($matches[1]);
            $mes = intval($matches[2]);
            $ano = intval($matches[3]);

            // Validar se a data é válida (dia, mês, ano)
            if ($dia >= 1 && $dia <= 31 && $mes >= 1 && $mes <= 12 && $ano >= 1900 && $ano <= 2100) {
                // Verificar se a data é realmente válida
                if (checkdate($mes, $dia, $ano)) {
                    // Formatar com zeros à esquerda
                    $dia_fmt = str_pad($dia, 2, '0', STR_PAD_LEFT);
                    $mes_fmt = str_pad($mes, 2, '0', STR_PAD_LEFT);

                    return "$ano-$mes_fmt-$dia_fmt";
                }
            }
        }

        // FORMATO BRASILEIRO: dd-mm-yyyy
        if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $value, $matches)) {
            $dia = intval($matches[1]);
            $mes = intval($matches[2]);
            $ano = intval($matches[3]);

            if ($dia >= 1 && $dia <= 31 && $mes >= 1 && $mes <= 12 && $ano >= 1900 && $ano <= 2100) {
                if (checkdate($mes, $dia, $ano)) {
                    $dia_fmt = str_pad($dia, 2, '0', STR_PAD_LEFT);
                    $mes_fmt = str_pad($mes, 2, '0', STR_PAD_LEFT);

                    return "$ano-$mes_fmt-$dia_fmt";
                }
            }
        }

        // FORMATO ISO: yyyy-mm-dd (já no formato correto)
        if (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $value, $matches)) {
            $ano = intval($matches[1]);
            $mes = intval($matches[2]);
            $dia = intval($matches[3]);

            if (checkdate($mes, $dia, $ano)) {
                $dia_fmt = str_pad($dia, 2, '0', STR_PAD_LEFT);
                $mes_fmt = str_pad($mes, 2, '0', STR_PAD_LEFT);

                return "$ano-$mes_fmt-$dia_fmt";
            }
        }

        // FORMATO AMERICANO: yyyy/mm/dd
        if (preg_match('/^(\d{4})\/(\d{1,2})\/(\d{1,2})$/', $value, $matches)) {
            $ano = intval($matches[1]);
            $mes = intval($matches[2]);
            $dia = intval($matches[3]);

            if (checkdate($mes, $dia, $ano)) {
                $dia_fmt = str_pad($dia, 2, '0', STR_PAD_LEFT);
                $mes_fmt = str_pad($mes, 2, '0', STR_PAD_LEFT);

                return "$ano-$mes_fmt-$dia_fmt";
            }
        }
    }

    return null;
}

// Função para limpar valores monetários
function cleanMoneyValue($value) {
    if (empty($value)) {
        return null;
    }

    // Se for numérico, retornar como float
    if (is_numeric($value)) {
        return floatval($value);
    }

    // Converter para string e limpar
    $value = trim((string)$value);

    if (empty($value)) {
        return null;
    }

    // Remover símbolos de moeda e espaços
    $cleaned = preg_replace('/[R$\s]/', '', $value);

    // Verificar se tem formato brasileiro (1.234,56)
    if (preg_match('/^\d{1,3}(\.\d{3})*,\d{2}$/', $cleaned)) {
        // Formato brasileiro: remover pontos e trocar vírgula por ponto
        $cleaned = str_replace('.', '', $cleaned);
        $cleaned = str_replace(',', '.', $cleaned);
    }
    // Verificar se tem formato americano (1,234.56)
    elseif (preg_match('/^\d{1,3}(,\d{3})*\.\d{2}$/', $cleaned)) {
        // Formato americano: remover vírgulas
        $cleaned = str_replace(',', '', $cleaned);
    }
    // Verificar se tem apenas vírgula como separador decimal (123,45)
    elseif (preg_match('/^\d+,\d{1,2}$/', $cleaned)) {
        // Trocar vírgula por ponto
        $cleaned = str_replace(',', '.', $cleaned);
    }
    // Verificar se tem apenas ponto como separador decimal (123.45)
    elseif (preg_match('/^\d+\.\d{1,2}$/', $cleaned)) {
        // Já está no formato correto
    }
    // Se for apenas números, assumir que está em centavos se for muito grande
    elseif (preg_match('/^\d+$/', $cleaned)) {
        $numero = intval($cleaned);
        // Se for maior que 100000, assumir que está em centavos
        if ($numero > 100000) {
            $cleaned = number_format($numero / 100, 2, '.', '');
        }
    }
    else {
        // Limpar caracteres não numéricos exceto vírgula e ponto
        $cleaned = preg_replace('/[^\d,.-]/', '', $cleaned);

        // Se ainda tem vírgula e ponto, assumir formato brasileiro
        if (strpos($cleaned, ',') !== false && strpos($cleaned, '.') !== false) {
            $cleaned = str_replace('.', '', $cleaned);
            $cleaned = str_replace(',', '.', $cleaned);
        }
        // Se tem apenas vírgula, trocar por ponto
        elseif (strpos($cleaned, ',') !== false) {
            $cleaned = str_replace(',', '.', $cleaned);
        }
    }

    return is_numeric($cleaned) ? floatval($cleaned) : null;
}

// Função para tratar CPF/CNPJ
function formatCpfCnpj($value) {
    if (empty($value)) {
        return '';
    }

    // Limpar caracteres não numéricos
    $cleaned = preg_replace('/[^0-9]/', '', $value);

    if (empty($cleaned)) {
        return '';
    }

    $length = strlen($cleaned);

    // Se tem 11 dígitos ou menos, assumir que é CPF e completar com zeros à esquerda até 11 dígitos
    if ($length <= 11) {
        return str_pad($cleaned, 11, '0', STR_PAD_LEFT);
    }
    // Se tem 12-14 dígitos, assumir que é CNPJ e completar com zeros à esquerda até 14 dígitos
    elseif ($length <= 14) {
        return str_pad($cleaned, 14, '0', STR_PAD_LEFT);
    }
    // Se tem mais de 14 dígitos, retornar apenas os primeiros 14
    else {
        return substr($cleaned, 0, 14);
    }
}

// Função para buscar ID do usuário pelo nome
function buscarUsuarioPorNome($nome) {
    global $pdo;

    if (empty($nome)) {
        return null;
    }

    try {
        $stmt = $pdo->prepare("
            SELECT id FROM usuarios
            WHERE nome_completo = ? AND ativo = 1
            LIMIT 1
        ");
        $stmt->execute([trim($nome)]);
        $usuario = $stmt->fetch();

        return $usuario ? $usuario['id'] : null;
    } catch (Exception $e) {
        return null;
    }
}

// Processar upload do arquivo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['excel_file'])) {
    try {
        $file = $_FILES['excel_file'];
        
        // Validações básicas
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Erro no upload do arquivo.');
        }
        
        if ($file['size'] > MCI_MAX_FILE_SIZE) {
            throw new Exception('Arquivo muito grande. Tamanho máximo: ' . (MCI_MAX_FILE_SIZE / 1024 / 1024) . 'MB');
        }
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, MCI_ALLOWED_EXTENSIONS)) {
            throw new Exception('Tipo de arquivo não permitido. Use apenas: ' . implode(', ', MCI_ALLOWED_EXTENSIONS));
        }
        
        // Mover arquivo para diretório de uploads
        $filename = 'mci_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.' . $extension;
        $filepath = MCI_UPLOAD_PATH . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Erro ao salvar arquivo.');
        }
        
        // Registrar importação no banco
        $stmt = $pdo_mci->prepare("
            INSERT INTO cad_importacoes (nome_arquivo, tamanho_arquivo, total_registros, usuario_id, status) 
            VALUES (?, ?, 0, ?, 'processando')
        ");
        $stmt->execute([$file['name'], $file['size'], $_SESSION['user_id']]);
        $importacao_id = $pdo_mci->lastInsertId();
        
        // Processar arquivo Excel
        $spreadsheet = IOFactory::load($filepath);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();
        
        // Remover cabeçalho
        $header = array_shift($rows);
        $total_rows = count($rows);
        
        // Atualizar total de registros
        $stmt = $pdo_mci->prepare("UPDATE cad_importacoes SET total_registros = ? WHERE id = ?");
        $stmt->execute([$total_rows, $importacao_id]);
        
        $imported = 0;
        $errors = 0;
        
        $pdo_mci->beginTransaction();

        foreach ($rows as $index => $row) {
            try {
                // Pular linhas vazias
                if (empty(array_filter($row))) {
                    continue;
                }

                // Mapear colunas da planilha com tratamento melhorado
                // Nova ordem com Saldo Devedor incluído
                $funcionario_valor = trim($row[9] ?? '');
                $tecnico_valor = trim($row[11] ?? '');

                // Verificar se os valores são numéricos (IDs) ou texto (nomes)
                $funcionario_eh_id = is_numeric($funcionario_valor) && !empty($funcionario_valor);
                $tecnico_eh_id = is_numeric($tecnico_valor) && !empty($tecnico_valor);

                $data = [
                    'pa' => isset($row[0]) ? trim((string)$row[0]) : '',
                    'nome_cliente' => trim($row[1] ?? ''),
                    'numero_cpf_cnpj' => formatCpfCnpj($row[2] ?? ''), // Formatar CPF/CNPJ com zeros à esquerda
                    'cnae' => trim($row[3] ?? ''),
                    'data_ultima_atualizacao_renda' => convertExcelDate($row[4] ?? ''),
                    'sigla_tipo_pessoa' => trim($row[5] ?? ''),
                    'profissao' => trim($row[6] ?? ''),
                    'deposito_total' => cleanMoneyValue($row[7] ?? ''),
                    'saldo_devedor' => cleanMoneyValue($row[8] ?? ''), // Nova coluna
                    'funcionario' => $funcionario_valor,
                    'funcionario_id' => $funcionario_eh_id ? (int)$funcionario_valor : buscarUsuarioPorNome($funcionario_valor),
                    'data_solicitacao_laudo' => convertExcelDate($row[10] ?? ''),
                    'tecnico_responsavel' => $tecnico_valor,
                    'tecnico_responsavel_id' => $tecnico_eh_id ? (int)$tecnico_valor : buscarUsuarioPorNome($tecnico_valor),
                    'data_atual_sisbr' => convertExcelDate($row[12] ?? ''),
                    'usuario_cadastro' => $_SESSION['user_id'],
                    'status' => 1 // Status "Pendente" por padrão
                ];

                // Validações obrigatórias
                // Para PA, verificar se é null, string vazia ou apenas espaços, mas aceitar 0
                if ($data['pa'] === '' || $data['pa'] === null || trim($data['pa']) === '') {
                    throw new Exception('PA é obrigatório');
                }
                if (empty($data['nome_cliente'])) {
                    throw new Exception('Nome Cliente é obrigatório');
                }
                if (empty($data['numero_cpf_cnpj'])) {
                    throw new Exception('CPF/CNPJ é obrigatório');
                }

                // Validar tamanho dos campos
                if (strlen($data['pa']) > 10) {
                    throw new Exception('PA muito longo (máximo 10 caracteres)');
                }
                if (strlen($data['nome_cliente']) > 255) {
                    throw new Exception('Nome Cliente muito longo (máximo 255 caracteres)');
                }
                if (strlen($data['numero_cpf_cnpj']) > 20) {
                    throw new Exception('CPF/CNPJ muito longo (máximo 20 caracteres)');
                }

                // Verificar estrutura das colunas funcionario e tecnico_responsavel
                $stmt_check = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field IN ('funcionario', 'tecnico_responsavel')");
                $stmt_check->execute();
                $colunas_info = $stmt_check->fetchAll();

                $funcionario_eh_int = false;
                $tecnico_eh_int = false;

                foreach ($colunas_info as $coluna) {
                    if ($coluna['Field'] == 'funcionario' && strpos($coluna['Type'], 'int') !== false) {
                        $funcionario_eh_int = true;
                    }
                    if ($coluna['Field'] == 'tecnico_responsavel' && strpos($coluna['Type'], 'int') !== false) {
                        $tecnico_eh_int = true;
                    }
                }

                // Preparar valores para inserção baseado na estrutura das colunas
                $funcionario_final = null;
                $tecnico_final = null;

                if ($funcionario_eh_int) {
                    // Coluna é INT, usar o ID (seja da planilha ou buscado por nome)
                    $funcionario_final = $data['funcionario_id'];
                } else {
                    // Coluna é VARCHAR, usar nome original
                    $funcionario_final = $data['funcionario'];
                }

                if ($tecnico_eh_int) {
                    // Coluna é INT, usar o ID (seja da planilha ou buscado por nome)
                    $tecnico_final = $data['tecnico_responsavel_id'];
                } else {
                    // Coluna é VARCHAR, usar nome original
                    $tecnico_final = $data['tecnico_responsavel'];
                }

                // Inserir registro
                $stmt = $pdo_mci->prepare("
                    INSERT INTO cad_registros (
                        pa, nome_cliente, numero_cpf_cnpj, cnae, data_ultima_atualizacao_renda,
                        sigla_tipo_pessoa, profissao, deposito_total, saldo_devedor, funcionario,
                        data_solicitacao_laudo, tecnico_responsavel, data_atual_sisbr, usuario_cadastro, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([
                    $data['pa'], $data['nome_cliente'], $data['numero_cpf_cnpj'], $data['cnae'],
                    $data['data_ultima_atualizacao_renda'], $data['sigla_tipo_pessoa'], $data['profissao'],
                    $data['deposito_total'], $data['saldo_devedor'], $funcionario_final, $data['data_solicitacao_laudo'],
                    $tecnico_final, $data['data_atual_sisbr'], $data['usuario_cadastro'], $data['status']
                ]);

                $imported++;

            } catch (Exception $e) {
                $errors++;

                // Registrar erro com mais detalhes
                $stmt_error = $pdo_mci->prepare("
                    INSERT INTO cad_erros_importacao (importacao_id, linha, campo, valor, erro)
                    VALUES (?, ?, ?, ?, ?)
                ");

                // Tentar identificar o campo problemático
                $campo_erro = '';
                $valor_erro = '';

                if (isset($row)) {
                    $campo_erro = 'linha_completa';
                    $valor_erro = implode(' | ', array_slice($row, 0, 5)); // Primeiros 5 campos
                }

                $stmt_error->execute([
                    $importacao_id,
                    $index + 2,
                    $campo_erro,
                    $valor_erro,
                    $e->getMessage()
                ]);
            }
        }
        
        // Atualizar estatísticas da importação
        $status = $errors > 0 ? ($imported > 0 ? 'concluido' : 'erro') : 'concluido';
        $stmt = $pdo_mci->prepare("
            UPDATE cad_importacoes 
            SET registros_importados = ?, registros_erro = ?, status = ?, data_conclusao = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$imported, $errors, $status, $importacao_id]);
        
        $pdo_mci->commit();
        
        // Log da importação
        $detalhes = "Importação concluída - Arquivo: {$file['name']}, Registros importados: $imported, Erros: $errors";
        $logger->log('Importação de planilha', $detalhes, $importacao_id);
        
        $message = "Importação concluída! $imported registros importados";
        if ($errors > 0) {
            $message .= " com $errors erros.";
        }
        
        // Remover arquivo após processamento
        unlink($filepath);
        
    } catch (Exception $e) {
        if (isset($pdo_mci) && $pdo_mci->inTransaction()) {
            $pdo_mci->rollBack();
        }
        
        $error = $e->getMessage();
        $logger->logFile("Erro na importação: " . $error, 'ERROR');
        
        // Atualizar status da importação para erro
        if (isset($importacao_id)) {
            $stmt = $pdo_mci->prepare("
                UPDATE cad_importacoes 
                SET status = 'erro', detalhes_erro = ?, data_conclusao = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$error, $importacao_id]);
        }
        
        // Remover arquivo em caso de erro
        if (isset($filepath) && file_exists($filepath)) {
            unlink($filepath);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importar Planilha - <?php echo MCI_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
        }

        body { background-color: #f8f9fa; }
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .btn-primary { background-color: var(--sicoob-verde-escuro); border-color: var(--sicoob-verde-escuro); }
        .btn-primary:hover { background-color: var(--sicoob-turquesa); border-color: var(--sicoob-turquesa); }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: var(--sicoob-turquesa);
            background-color: rgba(0, 174, 157, 0.05);
        }
        
        .upload-area.dragover {
            border-color: var(--sicoob-verde-claro);
            background-color: rgba(201, 210, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../assets/images/logo1.png" alt="Sicoob" height="40">
                <span class="ms-2">MCI - Importar Planilha</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Mensagens -->
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Formulário de Upload -->
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-excel"></i> Importar Planilha Excel
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>Arraste o arquivo aqui ou clique para selecionar</h5>
                                <p class="text-muted">Formatos aceitos: .xlsx, .xls (máximo <?php echo MCI_MAX_FILE_SIZE / 1024 / 1024; ?>MB)</p>
                                <input type="file" name="excel_file" id="excel_file" class="d-none" accept=".xlsx,.xls" required>
                            </div>
                            
                            <div id="fileInfo" class="mt-3 d-none">
                                <div class="alert alert-info">
                                    <i class="fas fa-file"></i> <span id="fileName"></span>
                                    <span class="badge bg-secondary ms-2" id="fileSize"></span>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                    <i class="fas fa-upload"></i> Importar Planilha
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancelar
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instruções -->
        <div class="row mt-4">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle"></i> Formato da Planilha
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>A planilha deve conter as seguintes colunas na ordem especificada:</p>
                        <ol>
                            <li><strong>PA</strong> (obrigatório)</li>
                            <li><strong>Nome Cliente</strong> (obrigatório)</li>
                            <li><strong>Número CPF/CNPJ</strong> (obrigatório)</li>
                            <li>CNAE</li>
                            <li>Data Última Atualização Renda</li>
                            <li>Sigla Tipo Pessoa</li>
                            <li>Profissão</li>
                            <li>Depósito Total</li>
                            <li><strong>Saldo Devedor</strong> (nova coluna)</li>
                            <li>FUNCIONÁRIO (ID do usuário ou nome)</li>
                            <li>DATA DA SOLICITAÇÃO DO LAUDO</li>
                            <li>TÉCNICO RESPONSÁVEL (ID do usuário ou nome)</li>
                            <li>DATA DA ATUAL. SISBR</li>
                        </ol>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Importante:</strong> A primeira linha deve conter os cabeçalhos e será ignorada durante a importação.
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Funcionário e Técnico:</strong> Você pode usar:
                            <ul class="mb-0 mt-2">
                                <li><strong>IDs numéricos</strong> (ex: 123) - Referência direta ao usuário</li>
                                <li><strong>Nomes completos</strong> (ex: "João Silva") - Sistema tentará encontrar o usuário</li>
                                <li><strong>Células vazias</strong> - Serão importadas como NULL</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('excel_file');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const submitBtn = document.getElementById('submitBtn');

        // Click para selecionar arquivo
        uploadArea.addEventListener('click', () => fileInput.click());

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        // Mudança no input
        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.classList.remove('d-none');
                submitBtn.disabled = false;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Mostrar loading durante upload
        document.getElementById('uploadForm').addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
