<?php
require_once '../auth_check.php';
require_once '../classes/Logger.php';

$logger = new MciLogger();

echo "<h2>✅ Teste - Filtro de Bloqueio Removido</h2>";

echo "<div style='border: 2px solid #28a745; padding: 15px; background: #d4edda; margin: 10px 0;'>";
echo "<h3>🔧 Alteração Realizada:</h3>";
echo "<p><strong>ANTES:</strong> <code>status == 1 && bloqueado == 0</code></p>";
echo "<p><strong>DEPOIS:</strong> <code>status == 1</code> (filtro de bloqueio removido)</p>";
echo "<p><strong>Objetivo:</strong> Permitir que usuários bloqueados apareçam nos dashboards com suas fotos</p>";
echo "</div>";

// Testar API com novo filtro
echo "<h3>🌐 Teste da API com Filtro Modificado</h3>";

try {
    require_once '../cadastro/config_api.php';
    $intranetAPI = getIntranetAPI($logger);
    
    echo "<p>✅ <strong>API inicializada</strong></p>";
    
    // Buscar usuários com novo filtro
    $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
    echo "<p>✅ <strong>Cache criado com " . count($usuarios_intranet) . " usuários (com novo filtro)</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// Buscar Maycon especificamente
echo "<h3>🎯 Teste Específico - Maycon (ID 58)</h3>";

$email_maycon = '<EMAIL>';
$email_normalizado = strtolower(trim($email_maycon));

echo "<p><strong>Buscando por:</strong> " . htmlspecialchars($email_normalizado) . "</p>";

if (isset($usuarios_intranet[$email_normalizado])) {
    $maycon_api = $usuarios_intranet[$email_normalizado];
    
    echo "<div style='border: 2px solid #28a745; padding: 15px; background: #d4edda;'>";
    echo "<h4>🎉 SUCESSO! Maycon encontrado na API!</h4>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Campo</th><th>Valor</th></tr>";
    
    foreach ($maycon_api as $campo => $valor) {
        $destaque = '';
        if (in_array($campo, ['status', 'bloqueado', 'foto_url', 'nome'])) {
            $destaque = 'style="background-color: #fff3cd;"';
        }
        
        echo "<tr $destaque>";
        echo "<td><strong>" . htmlspecialchars($campo) . "</strong></td>";
        echo "<td>" . htmlspecialchars($valor ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Testar foto
    $foto_url = $maycon_api['foto_url'] ?? null;
    if ($foto_url) {
        echo "<h5>📸 Teste da Foto:</h5>";
        echo "<div style='text-align: center; margin: 15px 0;'>";
        echo "<img src='" . htmlspecialchars($foto_url) . "' alt='Foto do Maycon' style='width: 120px; height: 120px; border-radius: 50%; object-fit: cover; border: 3px solid #28a745;' onerror=\"this.style.border='3px solid #dc3545'; this.alt='❌ Erro ao carregar';\">";
        echo "<p><strong>URL:</strong> " . htmlspecialchars($foto_url) . "</p>";
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠️ <strong>Foto URL não disponível</strong></p>";
    }
    
    echo "</div>";
    
} else {
    echo "<div style='border: 2px solid #dc3545; padding: 15px; background: #f8d7da;'>";
    echo "<h4>❌ Maycon ainda não encontrado</h4>";
    echo "<p>O problema pode não ser apenas o filtro de bloqueio.</p>";
    echo "</div>";
}

// Testar processamento completo como no dashboard
echo "<h3>🔄 Simulação do Dashboard</h3>";

// Buscar técnicos
$stmt = $pdo_mci->prepare("
    SELECT u.id, u.nome_completo as nome, u.email
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL AND u.id = 58
    GROUP BY u.id, u.nome_completo, u.email
");
$stmt->execute();
$maycon_banco = $stmt->fetch();

if ($maycon_banco) {
    echo "<p>✅ <strong>Maycon encontrado no banco MCI</strong></p>";
    
    // Simular processamento
    $maycon_processado = $maycon_banco;
    $maycon_processado['foto_url'] = null;
    $maycon_processado['setor'] = null;
    $maycon_processado['funcao'] = null;
    
    if (!empty($maycon_banco['email'])) {
        $email_key = strtolower(trim($maycon_banco['email']));
        $usuario_intranet = $usuarios_intranet[$email_key] ?? null;
        
        if ($usuario_intranet) {
            $maycon_processado['foto_url'] = $usuario_intranet['foto_url'] ?? null;
            $maycon_processado['setor'] = $usuario_intranet['setor_nome'] ?? null;
            $maycon_processado['funcao'] = $usuario_intranet['funcao_nome'] ?? null;
            
            echo "<div style='border: 2px solid #28a745; padding: 15px; background: #d4edda;'>";
            echo "<h4>🎉 Processamento Bem-Sucedido!</h4>";
            echo "<p><strong>Foto URL:</strong> " . htmlspecialchars($maycon_processado['foto_url'] ?? 'NULL') . "</p>";
            echo "<p><strong>Setor:</strong> " . htmlspecialchars($maycon_processado['setor'] ?? 'NULL') . "</p>";
            echo "<p><strong>Função:</strong> " . htmlspecialchars($maycon_processado['funcao'] ?? 'NULL') . "</p>";
            
            if ($maycon_processado['foto_url']) {
                echo "<h5>🎨 Como aparecerá no dashboard:</h5>";
                echo "<div style='text-align: center; background: white; padding: 20px; border-radius: 15px; max-width: 300px; margin: 15px auto; border: 3px solid #28a745;'>";
                echo "<img src='" . htmlspecialchars($maycon_processado['foto_url']) . "' style='width: 80px; height: 80px; border-radius: 50%; object-fit: cover; margin-bottom: 10px;' onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">";
                echo "<div style='width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #003641, #00AE9D); display: none; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: bold; margin: 0 auto 10px;'>M</div>";
                echo "<h4 style='color: #003641; margin: 10px 0;'>" . htmlspecialchars($maycon_banco['nome']) . "</h4>";
                echo "<p style='color: #6c757d;'>✅ Com foto da intranet!</p>";
                echo "</div>";
            }
            
            echo "</div>";
            
        } else {
            echo "<div style='border: 2px solid #dc3545; padding: 15px; background: #f8d7da;'>";
            echo "<h4>❌ Ainda não encontrado no processamento</h4>";
            echo "<p>Email: " . htmlspecialchars($email_key) . "</p>";
            echo "</div>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ <strong>Maycon não encontrado no banco MCI</strong></p>";
}

// Comparar antes e depois
echo "<h3>📊 Comparação de Resultados</h3>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Aspecto</th><th>Antes (com filtro bloqueio)</th><th>Depois (sem filtro bloqueio)</th></tr>";

$total_usuarios_antes = "~289"; // Valor aproximado baseado nos logs anteriores
$total_usuarios_depois = count($usuarios_intranet);

echo "<tr>";
echo "<td><strong>Total de usuários na API</strong></td>";
echo "<td>$total_usuarios_antes</td>";
echo "<td>$total_usuarios_depois</td>";
echo "</tr>";

$maycon_antes = "❌ Não encontrado";
$maycon_depois = isset($usuarios_intranet[$email_normalizado]) ? "✅ Encontrado" : "❌ Ainda não encontrado";

echo "<tr>";
echo "<td><strong>Maycon na API</strong></td>";
echo "<td>$maycon_antes</td>";
echo "<td>$maycon_depois</td>";
echo "</tr>";

echo "</table>";

echo "<h3>🚀 Próximos Passos</h3>";

if (isset($usuarios_intranet[$email_normalizado])) {
    echo "<div style='border: 2px solid #28a745; padding: 15px; background: #d4edda;'>";
    echo "<h4>✅ Sucesso!</h4>";
    echo "<p>O filtro de bloqueio era o problema. Agora você pode:</p>";
    echo "<ol>";
    echo "<li><a href='dashboard.php' target='_blank'>🚀 Abrir o dashboard</a> e verificar se a foto do Maycon aparece</li>";
    echo "<li>Aguardar o auto-refresh ou forçar refresh (Ctrl+F5)</li>";
    echo "<li>Verificar se outros técnicos também foram beneficiados</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='border: 2px solid #ffc107; padding: 15px; background: #fff3cd;'>";
    echo "<h4>⚠️ Investigação Adicional Necessária</h4>";
    echo "<p>O filtro de bloqueio não era o único problema. Recomendo:</p>";
    echo "<ol>";
    echo "<li><a href='debug_filtros_api.php'>🔍 Executar debug dos filtros</a> para ver dados brutos</li>";
    echo "<li>Verificar se o email está correto na API</li>";
    echo "<li>Verificar se há outros filtros não identificados</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='dashboard.php'>🚀 Dashboard</a> | <a href='debug_filtros_api.php'>🔍 Debug Filtros</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
