<?php
require_once 'config/database.php';

// <PERSON><PERSON>r alias para compatibilidade
$pdo_sicoob = $pdo;

echo "<h1>🚀 Demonstração da Funcionalidade de Transferência em Massa</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .info { color: #17a2b8; }
    .warning { color: #ffc107; }
    .error { color: #dc3545; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
</style>";

try {
    // 1. Mostrar funcionários disponíveis
    echo "<div class='card'>";
    echo "<h2 class='info'>📋 1. Funcionários Disponíveis (Setor 8)</h2>";
    $stmt_funcionarios = $pdo_sicoob->prepare("
        SELECT u.id, u.nome_completo
        FROM usuarios u
        INNER JOIN usuario_setor us ON u.id = us.usuario_id
        WHERE us.setor_id = 8 AND u.ativo = TRUE
        ORDER BY u.nome_completo
    ");
    $stmt_funcionarios->execute();
    $funcionarios = $stmt_funcionarios->fetchAll();
    
    echo "<table>";
    echo "<tr><th>ID</th><th>Nome Completo</th></tr>";
    foreach ($funcionarios as $func) {
        echo "<tr><td>{$func['id']}</td><td>{$func['nome_completo']}</td></tr>";
    }
    echo "</table>";
    echo "<p class='success'>✅ Total de funcionários: " . count($funcionarios) . "</p>";
    echo "</div>";

    // 2. Mostrar distribuição atual de registros
    echo "<div class='card'>";
    echo "<h2 class='info'>📊 2. Distribuição Atual de Registros</h2>";
    $stmt_distribuicao = $pdo_mci->prepare("
        SELECT
            cr.funcionario,
            u.nome_completo,
            COUNT(*) as total_registros,
            SUM(CASE WHEN cs.nome = 'Pendente' THEN 1 ELSE 0 END) as registros_pendentes,
            SUM(CASE WHEN cs.nome != 'Removido' AND cs.nome != 'Pendente' THEN 1 ELSE 0 END) as registros_processados,
            SUM(CASE WHEN cs.nome = 'Removido' THEN 1 ELSE 0 END) as registros_removidos
        FROM cad_registros cr
        LEFT JOIN sicoob_access_control.usuarios u ON cr.funcionario = u.id
        INNER JOIN cad_status cs ON cr.status = cs.id
        WHERE cr.funcionario IS NOT NULL
        GROUP BY cr.funcionario, u.nome_completo
        ORDER BY registros_pendentes DESC
        LIMIT 10
    ");
    $stmt_distribuicao->execute();
    $distribuicao = $stmt_distribuicao->fetchAll();
    
    echo "<table>";
    echo "<tr><th>ID</th><th>Nome</th><th>Total</th><th>Pendentes</th><th>Processados</th><th>Removidos</th><th>% Pendente</th></tr>";
    foreach ($distribuicao as $func) {
        $percentual = $func['total_registros'] > 0 ? round(($func['registros_pendentes'] / $func['total_registros']) * 100, 1) : 0;
        echo "<tr>";
        echo "<td>{$func['funcionario']}</td>";
        echo "<td>{$func['nome_completo']}</td>";
        echo "<td>{$func['total_registros']}</td>";
        echo "<td><strong>{$func['registros_pendentes']}</strong></td>";
        echo "<td>{$func['registros_processados']}</td>";
        echo "<td>{$func['registros_removidos']}</td>";
        echo "<td>{$percentual}%</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";

    // 3. Simular uma transferência
    if (count($distribuicao) >= 2) {
        echo "<div class='card'>";
        echo "<h2 class='info'>🔄 3. Simulação de Transferência</h2>";
        
        $funcionario_origem = $distribuicao[0];
        $funcionario_destino = $distribuicao[1];
        $quantidade_simular = min(10, $funcionario_origem['registros_pendentes']);

        echo "<p><strong>Cenário Simulado:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Origem:</strong> {$funcionario_origem['nome_completo']} (ID: {$funcionario_origem['funcionario']})</li>";
        echo "<li><strong>Registros Pendentes:</strong> {$funcionario_origem['registros_pendentes']}</li>";
        echo "<li><strong>Destino:</strong> {$funcionario_destino['nome_completo']} (ID: {$funcionario_destino['funcionario']})</li>";
        echo "<li><strong>Quantidade a Transferir:</strong> $quantidade_simular registros</li>";
        echo "</ul>";

        // Buscar registros que seriam transferidos (apenas pendentes)
        $stmt_simulacao = $pdo_mci->prepare("
            SELECT cr.id, cr.nome_cliente, cr.numero_cpf_cnpj, cr.data_cadastro
            FROM cad_registros cr
            INNER JOIN cad_status cs ON cr.status = cs.id
            WHERE cr.funcionario = ?
            AND cs.nome = 'Pendente'
            ORDER BY cr.data_cadastro ASC
            LIMIT ?
        ");
        $stmt_simulacao->execute([$funcionario_origem['funcionario'], $quantidade_simular]);
        $registros_simulacao = $stmt_simulacao->fetchAll();
        
        echo "<h4>Registros que seriam transferidos (mais antigos primeiro):</h4>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Cliente</th><th>CPF/CNPJ</th><th>Data Cadastro</th></tr>";
        foreach ($registros_simulacao as $registro) {
            $cpf_formatado = strlen($registro['numero_cpf_cnpj']) == 11 ?
                preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $registro['numero_cpf_cnpj']) :
                preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $registro['numero_cpf_cnpj']);
            
            echo "<tr>";
            echo "<td>{$registro['id']}</td>";
            echo "<td>{$registro['nome_cliente']}</td>";
            echo "<td>$cpf_formatado</td>";
            echo "<td>" . date('d/m/Y', strtotime($registro['data_cadastro'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div class='warning'>";
        echo "<h4>⚠️ Resultado da Transferência:</h4>";
        echo "<ul>";
        echo "<li><strong>{$funcionario_origem['nome_completo']}</strong> ficaria com: " . ($funcionario_origem['registros_pendentes'] - $quantidade_simular) . " registros pendentes</li>";
        echo "<li><strong>{$funcionario_destino['nome_completo']}</strong> ficaria com: " . ($funcionario_destino['registros_pendentes'] + $quantidade_simular) . " registros pendentes</li>";
        echo "</ul>";
        echo "<p><small><strong>Nota:</strong> Apenas registros com status 'Pendente' podem ser transferidos.</small></p>";
        echo "</div>";
        echo "</div>";
    }

    // 4. Instruções de uso
    echo "<div class='card'>";
    echo "<h2 class='success'>🎯 4. Como Usar a Transferência em Massa</h2>";
    echo "<ol>";
    echo "<li><strong>Acesse:</strong> <a href='transferencia_massa.php' target='_blank'>transferencia_massa.php</a></li>";
    echo "<li><strong>Selecione:</strong> O funcionário de origem que possui os registros</li>";
    echo "<li><strong>Configure:</strong> As transferências clicando em 'Adicionar Transferência'</li>";
    echo "<li><strong>Defina:</strong> Para cada transferência:";
    echo "<ul>";
    echo "<li>Funcionário de destino</li>";
    echo "<li>Quantidade de registros</li>";
    echo "</ul></li>";
    echo "<li><strong>Valide:</strong> O sistema verifica automaticamente se a configuração está correta</li>";
    echo "<li><strong>Execute:</strong> Clique em 'Executar Transferência' e confirme</li>";
    echo "</ol>";
    echo "</div>";

    // 5. Recursos disponíveis
    echo "<div class='card'>";
    echo "<h2 class='info'>🛠️ 5. Recursos Disponíveis</h2>";
    echo "<ul>";
    echo "<li>✅ <strong>Validação em Tempo Real:</strong> Verifica se as quantidades são válidas</li>";
    echo "<li>✅ <strong>Sugestão Automática:</strong> Botão 'Auto' para distribuir registros automaticamente</li>";
    echo "<li>✅ <strong>Transações Seguras:</strong> Em caso de erro, nenhuma alteração é aplicada</li>";
    echo "<li>✅ <strong>Log Completo:</strong> Todas as transferências são registradas</li>";
    echo "<li>✅ <strong>Interface Intuitiva:</strong> Design responsivo e fácil de usar</li>";
    echo "<li>✅ <strong>Permissões:</strong> Apenas gestores e administradores podem usar</li>";
    echo "</ul>";
    echo "</div>";

    // 6. Links úteis
    echo "<div class='card'>";
    echo "<h2 class='success'>🔗 6. Links Úteis</h2>";
    echo "<ul>";
    echo "<li>🚀 <a href='transferencia_massa.php' target='_blank'><strong>Acessar Transferência em Massa</strong></a></li>";
    echo "<li>📋 <a href='gerenciar.php' target='_blank'>Gerenciar Registros</a></li>";
    echo "<li>📊 <a href='metas.php' target='_blank'>Visualizar Metas</a></li>";
    echo "<li>📖 <a href='TRANSFERENCIA_MASSA.md' target='_blank'>Documentação Completa</a></li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='success'>";
    echo "<h2>🎉 Sistema Pronto para Uso!</h2>";
    echo "<p>A funcionalidade de Transferência em Massa foi implementada com sucesso e está pronta para ser utilizada.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ Erro na Demonstração</h2>";
    echo "<p>Erro: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
