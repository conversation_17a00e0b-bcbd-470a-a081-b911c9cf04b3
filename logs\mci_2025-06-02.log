[2025-06-02 14:36:48] [INFO] Acesso ao módulo MCI: Usuário acessou a página principal do módulo MCI
[2025-06-02 14:36:48] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'usuario_id' in 'field list'
[2025-06-02 14:36:48] [ERROR] Erro ao buscar estatísticas: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list'
[2025-06-02 14:37:45] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 0, Erros: 3954
[2025-06-02 14:37:45] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'usuario_id' in 'field list'
[2025-06-02 14:48:40] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 0, Erros: 3939
[2025-06-02 14:48:40] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'usuario_id' in 'field list'
[2025-06-02 14:51:00] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3692, Erros: 247
[2025-06-02 14:56:31] [INFO] Reprocessamento PA 0: Usuário iniciou reprocessamento de registros com PA 0
[2025-06-02 14:56:38] [INFO] Reprocessamento PA 0 concluído: Reprocessados: 244, Falhas: 0
[2025-06-02 15:12:27] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 15:12:28] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 15:16:03] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 15:16:04] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 15:24:07] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 15:24:09] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 15:32:42] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 15:32:42] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 15:32:43] [INFO] Teste de inserção: Registro de teste inserido com sucesso
[2025-06-02 15:35:03] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 15:35:03] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 15:38:28] [INFO] Teste de inserção: Registro de teste inserido com sucesso
[2025-06-02 15:40:27] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 15:40:28] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 16:48:50] [INFO] Acesso ao módulo MCI: Usuário acessou a página principal do módulo MCI
[2025-06-02 16:51:52] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 553, Erros: 3386
[2025-06-02 16:51:53] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 17:03:46] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 17:03:46] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 17:10:29] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 17:10:29] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 17:16:50] [INFO] Importação de planilha: Importação concluída - Arquivo: PLANILHA MCI - 1º TRIMESTRE - MCI.xlsx, Registros importados: 3939, Erros: 0
[2025-06-02 17:16:51] [ERROR] Erro ao registrar log no banco MCI: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`mci`.`cad_logs`, CONSTRAINT `cad_logs_ibfk_1` FOREIGN KEY (`registro_id`) REFERENCES `cad_registros` (`id`) ON DELETE SET NULL)
[2025-06-02 17:18:21] [INFO] Acesso ao módulo MCI: Usuário acessou a página principal do módulo MCI
