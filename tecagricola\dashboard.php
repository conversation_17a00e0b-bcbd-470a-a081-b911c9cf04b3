<?php
// Dashboard público - sem autenticação necessária
// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

require_once '../config/config.php';
require_once '../classes/Logger.php';

$logger = new MciLogger();

// Buscar dados dos técnicos agrícolas
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

// Buscar dados da API Intranet para fotos
require_once '../cadastro/config_api.php';
$intranetAPI = getIntranetAPI($logger);

// Cache dos usuários da intranet
$usuarios_intranet = [];
try {
    $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
    $logger->logFile("Cache de usuários da intranet criado com " . count($usuarios_intranet) . " usuários", 'INFO');
} catch (Exception $e) {
    $logger->logFile("Erro ao buscar usuários da intranet: " . $e->getMessage(), 'ERROR');
}

// Adicionar dados da intranet aos técnicos (ISOLADO PARA EVITAR INTERFERÊNCIA)
$tecnicos_dados_temp = $tecnicos_dados; // Cópia para processamento
foreach ($tecnicos_dados_temp as $index => &$tecnico) {
    $tecnico['foto_url'] = null;
    $tecnico['setor'] = null;
    $tecnico['funcao'] = null;

    if (!empty($tecnico['email'])) {
        $email_key = strtolower(trim($tecnico['email']));
        $usuario_intranet = $usuarios_intranet[$email_key] ?? null;

        if ($usuario_intranet) {
            $tecnico['foto_url'] = $usuario_intranet['foto_url'] ?? null;
            $tecnico['setor'] = $usuario_intranet['setor_nome'] ?? null;
            $tecnico['funcao'] = $usuario_intranet['funcao_nome'] ?? null;

            // Usar nome da API Intranet quando disponível
            if (!empty($usuario_intranet['nome'])) {
                $tecnico['nome'] = $usuario_intranet['nome'];
            }
        }
    }

    // Atualizar o array original apenas com os dados processados
    $tecnicos_dados[$index] = $tecnico;
}
unset($tecnicos_dados_temp); // Limpar variável temporária

// Calcular métricas gerais
$total_meta = array_sum(array_column($tecnicos_dados, 'meta_total'));
$total_atualizados = array_sum(array_column($tecnicos_dados, 'atualizados_total'));
$progresso_geral = $total_meta > 0 ? round(($total_atualizados / $total_meta) * 100, 1) : 0;

// Calcular segmentos da barra de progresso por técnico
$cores_tecnicos = ['#00AE9D', '#003641', '#C9D200', '#70B86C', '#494790', '#6c757d'];
$segmentos_progresso = [];

// USAR CÓPIA DO ARRAY PARA EVITAR INTERFERÊNCIA
$tecnicos_copia = $tecnicos_dados;
$tecnico_index_calc = 0;

foreach ($tecnicos_copia as $tecnico) {
    if ($total_meta > 0) {
        $porcentagem_contribuicao = ($tecnico['atualizados_total'] / $total_meta) * 100;
        $segmentos_progresso[] = [
            'porcentagem' => $porcentagem_contribuicao,
            'cor' => $cores_tecnicos[$tecnico_index_calc % count($cores_tecnicos)],
            'nome' => $tecnico['nome'],
            'atualizados' => $tecnico['atualizados_total']
        ];
    }
    $tecnico_index_calc++;
}

// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard MCI - Técnicos Agrícolas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-roxo: #494790;
            --sicoob-cinza: #6c757d;

            /* Cores da identidade visual Sicoob para técnicos agrícolas */
            --cor-tecnico-1: #00AE9D; /* Turquesa */
            --cor-tecnico-2: #003641; /* Verde escuro */
            --cor-tecnico-3: #C9D200; /* Verde claro */
            --cor-tecnico-4: #70B86C; /* Verde médio */
            --cor-tecnico-5: #494790; /* Roxo */
            --cor-tecnico-6: #6c757d; /* Cinza */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #003641 0%, #00AE9D 100%);
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .dashboard-container {
            padding: 0.5rem;
            height: 100vh;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: white;
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .dashboard-header h1 {
            font-size: clamp(1.5rem, 3vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .dashboard-header .subtitle {
            font-size: clamp(0.8rem, 1.5vw, 1.1rem);
            opacity: 0.9;
        }

        .overview-section {
            margin-bottom: 0.5rem;
            flex-shrink: 0;
        }

        .overview-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .overview-card:hover {
            transform: translateY(-2px);
        }

        .overview-card h3 {
            color: var(--sicoob-verde-escuro);
            font-size: clamp(1rem, 2vw, 1.4rem);
            margin-bottom: 0.5rem;
        }

        .overview-value {
            font-size: clamp(1.5rem, 3vw, 2rem);
            font-weight: 700;
            color: var(--sicoob-turquesa);
            margin-bottom: 0.5rem;
        }

        .overview-progress {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            color: #6c757d;
            margin-bottom: 1rem;
        }

        .progress-bar-overview {
            height: 12px;
            background-color: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            display: flex;
        }

        .progress-segment {
            height: 100%;
            transition: width 0.8s ease;
            position: relative;
        }

        .progress-segment:first-child {
            border-radius: 6px 0 0 6px;
        }

        .progress-segment:last-child {
            border-radius: 0 6px 6px 0;
        }

        .progress-segment:only-child {
            border-radius: 6px;
        }

        .tecnicos-grid {
            display: flex;
            flex-direction: row;
            gap: 0.8rem;
            flex: 1;
            overflow: visible;
            justify-content: space-evenly;
            align-items: stretch;
            padding-top: clamp(15px, 3vw, 25px);
            padding-bottom: clamp(10px, 2vw, 20px);
            height: 100%;
        }

        .tecnico-card {
            background: white;
            border-radius: 16px;
            padding: clamp(5rem, 7vh, 7rem) clamp(0.8rem, 1.5vw, 1.2rem) clamp(1rem, 2vh, 1.5rem);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            border-top: 3px solid var(--cor-tecnico, var(--sicoob-turquesa));
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            text-align: center;
            flex: 1;
            min-width: 0;
            max-width: calc(100% / 6 - 0.8rem);
            overflow: visible;
            gap: clamp(0.5rem, 1.5vh, 1.5rem);
            margin-top: clamp(20px, 4vw, 40px);
        }

        .tecnico-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            padding: 2px;
            background: linear-gradient(135deg,
                var(--cor-tecnico, var(--sicoob-turquesa)) 0%,
                transparent 50%,
                var(--cor-tecnico, var(--sicoob-turquesa)) 100%);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            opacity: 0.3;
            z-index: -1;
        }

        .tecnico-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-top-color: var(--cor-tecnico, var(--sicoob-turquesa));
        }

        .tecnico-card:hover::before {
            opacity: 0.5;
        }

        .tecnico-avatar-container {
            position: absolute;
            top: clamp(-25px, -4vw, -40px);
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            flex-shrink: 0;
            margin: 0;
        }

        .tecnico-avatar {
            width: clamp(60px, 8vw, 100px);
            height: clamp(60px, 8vw, 100px);
            border-radius: 50%;
            object-fit: cover;
            position: relative;
            z-index: 2;
            border: 3px solid white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .tecnico-avatar.placeholder {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: clamp(1rem, 3.5vw, 2.2rem);
            font-weight: 700;
            border: 3px solid white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .progress-ring {
            position: absolute;
            top: -6px;
            left: -6px;
            width: calc(clamp(60px, 8vw, 100px) + 12px);
            height: calc(clamp(60px, 8vw, 100px) + 12px);
            border-radius: 50%;
            background: conic-gradient(
                var(--cor-tecnico, var(--sicoob-turquesa)) 0deg,
                var(--cor-tecnico, var(--sicoob-turquesa)) var(--progress-angle, 0deg),
                #e9ecef var(--progress-angle, 0deg),
                #e9ecef 360deg
            );
            z-index: 1;
        }

        .progress-ring::before {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            width: clamp(60px, 8vw, 100px);
            height: clamp(60px, 8vw, 100px);
            border-radius: 50%;
            background: white;
        }

        .tecnico-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;
            width: 100%;
            text-align: center;
            padding: 0;
            height: 100%;
        }

        .tecnico-info {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .tecnico-info h4 {
            color: var(--sicoob-verde-escuro);
            font-size: clamp(0.9rem, 1.8vw, 1.3rem);
            margin: 0;
            font-weight: 600;
            line-height: 1.2;
            word-wrap: break-word;
            hyphens: auto;
            text-align: center;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            width: 100%;
        }

        .metric-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;
            width: 100%;
            height: 100%;
            gap: clamp(0.5rem, 1vh, 1rem);
        }

        .metric-section {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(0.2rem, 0.5vh, 0.4rem);
        }

        .metric-label {
            font-size: clamp(0.7rem, 1.5vw, 0.9rem);
            color: var(--sicoob-verde-escuro);
            font-weight: 500;
            margin: 0;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 0.8;
        }

        .metric-value-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(0.1rem, 0.3vh, 0.2rem);
        }

        .metric-value {
            font-size: clamp(1.4rem, 3.5vw, 2.5rem);
            font-weight: 700;
            color: var(--cor-tecnico, var(--sicoob-turquesa));
            margin: 0;
            line-height: 1;
            text-align: center;
            width: 100%;
        }

        .metric-progress-bar {
            width: 100%;
            height: clamp(4px, 0.8vh, 8px);
            background-color: #e9ecef;
            border-radius: clamp(2px, 0.4vh, 4px);
            overflow: hidden;
            position: relative;
        }

        .metric-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--cor-tecnico, var(--sicoob-turquesa)), var(--sicoob-verde-escuro));
            border-radius: clamp(2px, 0.4vh, 4px);
            transition: width 0.8s ease-in-out;
            position: relative;
        }

        .metric-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .metric-percentage {
            font-size: clamp(1rem, 2.5vw, 1.6rem);
            font-weight: 600;
            color: var(--sicoob-verde-escuro);
            margin: 0;
            line-height: 1;
            text-align: center;
            width: 100%;
        }

        .metric-stats {
            display: flex;
            justify-content: space-between;
            width: 100%;
            font-size: clamp(0.6rem, 1.2vw, 0.8rem);
            color: var(--sicoob-verde-escuro);
            opacity: 0.7;
            font-weight: 500;
        }

        /* Classes específicas para cada técnico */
        .tecnico-1 { --cor-tecnico: var(--cor-tecnico-1); }
        .tecnico-2 { --cor-tecnico: var(--cor-tecnico-2); }
        .tecnico-3 { --cor-tecnico: var(--cor-tecnico-3); }
        .tecnico-4 { --cor-tecnico: var(--cor-tecnico-4); }
        .tecnico-5 { --cor-tecnico: var(--cor-tecnico-5); }
        .tecnico-6 { --cor-tecnico: var(--cor-tecnico-6); }

        /* Responsividade para TV - Layout sempre 3x2 */
        @media (min-width: 1920px) {
            .dashboard-container {
                padding: 1.2rem;
            }
        }

        @media (min-width: 3840px) {
            .dashboard-container {
                padding: 1.5rem;
            }
        }

        /* Ajustes para telas pequenas */
        @media (max-width: 1200px) {
            .tecnicos-grid {
                gap: 0.6rem;
                padding-top: clamp(12px, 2.5vh, 20px);
                padding-bottom: clamp(10px, 1.5vh, 20px);
            }

            .tecnico-card {
                max-width: calc(100% / 6 - 0.6rem);
                margin-top: clamp(18px, 3vh, 30px);
                padding: clamp(4rem, 6vh, 6rem) clamp(0.8rem, 1.5vw, 1.2rem) clamp(1rem, 1.8vh, 1.4rem);
            }

            .metric-value {
                font-size: clamp(1.2rem, 3vw, 2rem);
            }

            .metric-percentage {
                font-size: clamp(0.9rem, 2.2vw, 1.4rem);
            }
        }

        /* Ajustes para tablets */
        @media (max-width: 768px) {
            .tecnicos-grid {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
                padding-top: clamp(10px, 2vh, 20px);
                padding-bottom: clamp(10px, 1.5vh, 20px);
            }

            .tecnico-card {
                max-width: calc(50% - 0.5rem);
                padding: clamp(4rem, 5vh, 5rem) clamp(0.6rem, 1.2vw, 1rem) clamp(0.8rem, 1.5vh, 1.2rem);
                margin-top: clamp(15px, 2.5vh, 25px);
                flex: 1 1 calc(50% - 0.5rem);
            }

            .tecnico-avatar-container {
                top: clamp(-20px, -3vh, -30px);
            }

            .metric-value {
                font-size: clamp(1rem, 2.5vw, 1.6rem);
            }

            .metric-percentage {
                font-size: clamp(0.8rem, 2vw, 1.2rem);
            }

            .metric-stats {
                font-size: clamp(0.5rem, 1vw, 0.7rem);
            }
        }

        /* Ajustes para telas muito pequenas */
        @media (max-width: 480px) {
            .dashboard-container {
                height: 100vh;
                overflow: hidden;
            }

            .tecnicos-grid {
                flex-direction: column;
                align-items: center;
                padding-top: clamp(8px, 1.5vh, 15px);
                padding-bottom: clamp(10px, 1vh, 15px);
                gap: clamp(0.3rem, 1vh, 0.8rem);
                overflow: hidden;
            }

            .tecnico-card {
                max-width: 100%;
                padding: clamp(3rem, 4vh, 4rem) clamp(0.8rem, 1.5vw, 1.2rem) clamp(0.8rem, 1.5vh, 1.2rem);
                margin-top: clamp(8px, 1vh, 15px);
                flex: 1;
                min-height: 0;
            }

            .tecnico-avatar-container {
                top: clamp(-18px, -2vh, -25px);
            }

            .metric-value {
                font-size: clamp(0.9rem, 2vw, 1.3rem);
            }

            .metric-percentage {
                font-size: clamp(0.7rem, 1.8vw, 1rem);
            }

            .metric-stats {
                font-size: clamp(0.4rem, 0.8vw, 0.6rem);
            }

            .metric-label {
                font-size: clamp(0.6rem, 1.2vw, 0.8rem);
            }
        }

        /* Auto-refresh animation */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .refreshing {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="dashboard-header">
            <h1><i class="fas fa-seedling"></i> MCI - TÉCNICOS AGRÍCOLAS</h1>
            <div class="subtitle">Antecipação de Laudos - Monitoramento em Tempo Real</div>
        </div>

        <!-- Overview Geral -->
        <div class="overview-section">
            <div class="overview-card">
                <h3><i class="fas fa-chart-line"></i> Meta Total da Equipe</h3>
                <div class="overview-value"><?php echo number_format($total_meta); ?></div>
                <div class="overview-progress"><?php echo $total_atualizados; ?> concluídos (<?php echo $progresso_geral; ?>%)</div>
                <div class="progress-bar-overview">
                    <?php foreach ($segmentos_progresso as $segmento): ?>
                        <div class="progress-segment" 
                             style="width: <?php echo $segmento['porcentagem']; ?>%; background-color: <?php echo $segmento['cor']; ?>;"
                             title="<?php echo htmlspecialchars($segmento['nome']); ?>: <?php echo $segmento['atualizados']; ?> atualizações">
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Grid de Técnicos -->
        <!-- Timestamp: <?php echo date('Y-m-d H:i:s'); ?> | Total: <?php echo count($tecnicos_dados); ?> técnicos -->
        <div class="tecnicos-grid">
            <?php
            // USAR EXATAMENTE A MESMA ESTRUTURA DO DASHBOARD SIMPLIFICADO QUE FUNCIONA
            $contador = 1;
            foreach ($tecnicos_dados as $tecnico):
            ?>
            <div class="tecnico-card tecnico-<?php echo $contador; ?>">
                <!-- Lado Esquerdo: Avatar -->
                <div class="tecnico-avatar-container">
                    <div class="progress-ring" style="--progress-angle: <?php echo ($tecnico['progresso_total'] * 3.6); ?>deg;"></div>
                    <?php if ($tecnico['foto_url']): ?>
                        <img src="<?php echo htmlspecialchars($tecnico['foto_url']); ?>"
                             alt="<?php echo htmlspecialchars($tecnico['nome']); ?>"
                             class="tecnico-avatar">
                    <?php else: ?>
                        <div class="tecnico-avatar placeholder">
                            <?php echo strtoupper(substr($tecnico['nome'], 0, 1)); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Lado Direito: Informações -->
                <div class="tecnico-content">
                    <div class="tecnico-info">
                        <h4><?php echo htmlspecialchars($tecnico['nome']); ?></h4>
                    </div>

                    <div class="metric-info">
                        <!-- Seção de Meta -->
                        <div class="metric-section">
                            <div class="metric-value-container">
                                <div class="metric-value"><?php echo $tecnico['atualizados_total']; ?>/<?php echo $tecnico['meta_total']; ?></div>
                                <div class="metric-progress-bar">
                                    <div class="metric-progress-fill" style="width: <?php echo $tecnico['progresso_total']; ?>%;"></div>
                                </div>
                                <div class="metric-stats">
                                    <span>Concluídos: <?php echo $tecnico['atualizados_total']; ?> | </span>
                                    <span>| Restantes: <?php echo ($tecnico['meta_total'] - $tecnico['atualizados_total']); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Seção de Performance -->
                        <div class="metric-section">
                            <div class="metric-label">Performance</div>
                            <div class="metric-percentage"><?php echo $tecnico['progresso_total']; ?>%</div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            $contador++;
            endforeach;
            ?>
        </div>
    </div>

    <script>
        // Auto-refresh da página a cada 30 segundos
        let refreshTimer = setTimeout(() => {
            document.body.classList.add('refreshing');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }, 30000); // 30 segundos

        // Pausar refresh quando a página não está visível
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                clearTimeout(refreshTimer);
            } else {
                refreshTimer = setTimeout(() => {
                    document.body.classList.add('refreshing');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }, 30000); // 30 segundos
            }
        });

        // Animação de entrada para os cards
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.tecnico-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
