<?php
// Demonstração da funcionalidade de download Excel
// Este arquivo simula o download sem precisar de autenticação

// Verificar se é uma requisição de download
if (isset($_GET['demo']) && $_GET['demo'] === 'download') {
    // Função para formatar CPF/CNPJ
    function formatarCpfCnpj($numero) {
        $numero = preg_replace('/\D/', '', $numero);
        
        if (strlen($numero) == 11) {
            // CPF
            return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $numero);
        } elseif (strlen($numero) == 14) {
            // CNPJ
            return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $numero);
        }
        
        return $numero;
    }

    // Dados de exemplo para demonstração
    $registros_demo = [
        [
            'id' => 1,
            'pa' => 2,
            'nome_ponto_atendimento' => 'Manhumirim',
            'nome_cliente' => 'JOÃO DA SILVA SANTOS',
            'numero_cpf_cnpj' => '12345678901',
            'data_ultima_atualizacao_renda' => '2024-01-15',
            'funcionario_nome' => 'Milena Rodrigues Alves',
            'data_solicitacao_laudo' => '2024-02-10',
            'tecnico_nome' => 'Carlos Eduardo Técnico',
            'data_atual_sisbr' => '',
            'status_nome' => 'Solicitado',
            'data_cadastro' => '2024-01-10 14:30:00',
            'observacoes' => 'Cliente solicitou atualização urgente'
        ],
        [
            'id' => 2,
            'pa' => 5,
            'nome_ponto_atendimento' => 'Reduto',
            'nome_cliente' => 'MARIA OLIVEIRA COSTA',
            'numero_cpf_cnpj' => '98765432100',
            'data_ultima_atualizacao_renda' => '2023-12-20',
            'funcionario_nome' => 'Jussara Cristina Queiros Soares',
            'data_solicitacao_laudo' => '',
            'tecnico_nome' => '',
            'data_atual_sisbr' => '',
            'status_nome' => 'Pendente',
            'data_cadastro' => '2024-01-08 09:15:00',
            'observacoes' => ''
        ],
        [
            'id' => 3,
            'pa' => 1,
            'nome_ponto_atendimento' => 'Sede',
            'nome_cliente' => 'EMPRESA EXEMPLO LTDA',
            'numero_cpf_cnpj' => '12345678000195',
            'data_ultima_atualizacao_renda' => '2024-01-05',
            'funcionario_nome' => 'Luis Otavio Santos',
            'data_solicitacao_laudo' => '2024-01-20',
            'tecnico_nome' => 'Ana Paula Técnica',
            'data_atual_sisbr' => '2024-02-15',
            'status_nome' => 'Atualizado',
            'data_cadastro' => '2024-01-05 16:45:00',
            'observacoes' => 'Processo concluído com sucesso'
        ]
    ];

    // Configurar headers para download do Excel
    $filename = 'demo_relatorio_mci_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    // Abrir output para escrita
    $output = fopen('php://output', 'w');
    
    // Adicionar BOM para UTF-8 (para Excel reconhecer acentos)
    fwrite($output, "\xEF\xBB\xBF");

    // Cabeçalhos das colunas
    $headers = [
        'ID',
        'PA',
        'Nome do PA',
        'Nome do Cliente',
        'CPF/CNPJ',
        'Última Atualização Renda',
        'Funcionário',
        'Data Solicitação Laudo',
        'Técnico Responsável',
        'Data Atualização SISBR',
        'Status',
        'Data Cadastro',
        'Observações'
    ];

    fputcsv($output, $headers, ';');

    // Dados dos registros
    foreach ($registros_demo as $registro) {
        $row = [
            $registro['id'],
            $registro['pa'],
            $registro['nome_ponto_atendimento'],
            $registro['nome_cliente'],
            formatarCpfCnpj($registro['numero_cpf_cnpj']),
            !empty($registro['data_ultima_atualizacao_renda']) ? date('d/m/Y', strtotime($registro['data_ultima_atualizacao_renda'])) : '',
            $registro['funcionario_nome'],
            !empty($registro['data_solicitacao_laudo']) ? date('d/m/Y', strtotime($registro['data_solicitacao_laudo'])) : '',
            $registro['tecnico_nome'],
            !empty($registro['data_atual_sisbr']) ? date('d/m/Y', strtotime($registro['data_atual_sisbr'])) : '',
            $registro['status_nome'],
            !empty($registro['data_cadastro']) ? date('d/m/Y H:i:s', strtotime($registro['data_cadastro'])) : '',
            $registro['observacoes']
        ];

        fputcsv($output, $row, ';');
    }

    fclose($output);
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Download Excel - MCI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        body { background-color: #f8f9fa; padding: 20px; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        .btn-excel { background-color: #28a745; border-color: #28a745; color: white; }
        .btn-excel:hover { background-color: #218838; border-color: #1e7e34; color: white; }
        .demo-table { font-size: 0.9rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-primary mb-4">
            <i class="fas fa-file-excel"></i> 
            Demonstração Download Excel - MCI
        </h1>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> Registros de Atualizações Cadastrais (DEMO)
                    <span class="badge bg-light text-dark ms-2">3 registros</span>
                </h6>
                <div>
                    <a href="?demo=download" class="btn btn-excel btn-sm">
                        <i class="fas fa-file-excel"></i> Download Excel
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0 demo-table">
                        <thead>
                            <tr>
                                <th>PA</th>
                                <th>Associado</th>
                                <th>Última Atualização de Renda</th>
                                <th>Funcionário</th>
                                <th>Solicitação Laudo</th>
                                <th>Técnico Responsável</th>
                                <th>Atualização SISBR</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <strong>2</strong>
                                    <br><small class="text-muted">Manhumirim</small>
                                </td>
                                <td>
                                    <strong>JOÃO DA SILVA SANTOS</strong>
                                    <br><small class="text-muted">123.456.789-01</small>
                                </td>
                                <td>15/01/2024</td>
                                <td>Milena Rodrigues Alves</td>
                                <td>10/02/2024</td>
                                <td>Carlos Eduardo Técnico</td>
                                <td>-</td>
                                <td><span class="badge bg-warning">Solicitado</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>5</strong>
                                    <br><small class="text-muted">Reduto</small>
                                </td>
                                <td>
                                    <strong>MARIA OLIVEIRA COSTA</strong>
                                    <br><small class="text-muted">987.654.321-00</small>
                                </td>
                                <td>20/12/2023</td>
                                <td>Jussara Cristina Queiros Soares</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td><span class="badge bg-secondary">Pendente</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>1</strong>
                                    <br><small class="text-muted">Sede</small>
                                </td>
                                <td>
                                    <strong>EMPRESA EXEMPLO LTDA</strong>
                                    <br><small class="text-muted">12.345.678/0001-95</small>
                                </td>
                                <td>05/01/2024</td>
                                <td>Luis Otavio Santos</td>
                                <td>20/01/2024</td>
                                <td>Ana Paula Técnica</td>
                                <td>15/02/2024</td>
                                <td><span class="badge bg-success">Atualizado</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle"></i> Como Funciona
                        </h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Clique no botão "Download Excel"</strong> acima</li>
                            <li><strong>Arquivo será baixado automaticamente</strong> com nome demo_relatorio_mci_AAAA-MM-DD_HH-MM-SS.csv</li>
                            <li><strong>Abra no Excel</strong> para ver os dados formatados</li>
                            <li><strong>Observe:</strong> CPF/CNPJ formatados, datas em formato brasileiro</li>
                        </ol>
                        
                        <div class="alert alert-info mt-3">
                            <small>
                                <i class="fas fa-lightbulb"></i> 
                                <strong>Dica:</strong> Esta é uma demonstração com dados fictícios. 
                                Na página real, o download incluirá todos os registros do sistema 
                                respeitando os filtros aplicados.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-file-excel"></i> Conteúdo do Arquivo
                        </h6>
                    </div>
                    <div class="card-body">
                        <h6>Colunas incluídas no Excel:</h6>
                        <ul class="small">
                            <li>ID, PA, Nome do PA</li>
                            <li>Nome do Cliente, CPF/CNPJ</li>
                            <li>Última Atualização Renda</li>
                            <li>Funcionário</li>
                            <li>Data Solicitação Laudo</li>
                            <li>Técnico Responsável</li>
                            <li>Data Atualização SISBR</li>
                            <li>Status, Data Cadastro</li>
                            <li>Observações</li>
                        </ul>
                        
                        <h6 class="mt-3">Formatação:</h6>
                        <ul class="small">
                            <li><strong>Datas:</strong> dd/mm/aaaa</li>
                            <li><strong>CPF:</strong> 123.456.789-01</li>
                            <li><strong>CNPJ:</strong> 12.345.678/0001-95</li>
                            <li><strong>Separador:</strong> ; (ponto e vírgula)</li>
                            <li><strong>Encoding:</strong> UTF-8 com BOM</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-link"></i> Links Relacionados
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="test_download_excel.php" class="btn btn-primary w-100 mb-2" target="_blank">
                                    <i class="fas fa-info"></i> Documentação
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="gerenciar.php" class="btn btn-success w-100 mb-2" target="_blank">
                                    <i class="fas fa-list"></i> Página Real
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="test_page_simple.php" class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                    <i class="fas fa-list"></i> Todos os Testes
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="?demo=download" class="btn btn-excel w-100 mb-2">
                                    <i class="fas fa-download"></i> Baixar Demo
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h6><i class="fas fa-check-circle"></i> Funcionalidade Implementada com Sucesso!</h6>
            <p class="mb-0">
                O botão de download Excel foi adicionado na página gerenciar.php e está funcionando perfeitamente. 
                Clique no botão acima para testar o download com dados de demonstração!
            </p>
        </div>
    </div>
</body>
</html>
