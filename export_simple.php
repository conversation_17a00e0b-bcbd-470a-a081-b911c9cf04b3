<?php
// Export simples sem autenticação para teste
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Verificar parâmetro
if (!isset($_GET['download']) || $_GET['download'] !== 'excel') {
    echo "Erro: Parâmetro 'download=excel' não encontrado.";
    exit;
}

// Dados de exemplo para teste
$registros_exemplo = [
    [
        'id' => 1,
        'pa' => 2,
        'nome_ponto_atendimento' => 'Manhumirim',
        'nome_cliente' => 'JOÃO DA SILVA SANTOS',
        'numero_cpf_cnpj' => '12345678901',
        'data_ultima_atualizacao_renda' => '2024-01-15',
        'funcionario_nome' => 'Milena <PERSON>',
        'data_solicitacao_laudo' => '2024-02-10',
        'tecnico_nome' => '<PERSON>',
        'data_atual_sisbr' => '',
        'status_nome' => 'Solicitado',
        'data_cadastro' => '2024-01-10 14:30:00',
        'observacoes' => 'Cliente solicitou atualização urgente'
    ],
    [
        'id' => 2,
        'pa' => 5,
        'nome_ponto_atendimento' => 'Reduto',
        'nome_cliente' => 'MARIA OLIVEIRA COSTA',
        'numero_cpf_cnpj' => '98765432100',
        'data_ultima_atualizacao_renda' => '2023-12-20',
        'funcionario_nome' => 'Jussara Cristina Queiros Soares',
        'data_solicitacao_laudo' => '',
        'tecnico_nome' => '',
        'data_atual_sisbr' => '',
        'status_nome' => 'Pendente',
        'data_cadastro' => '2024-01-08 09:15:00',
        'observacoes' => ''
    ],
    [
        'id' => 3,
        'pa' => 1,
        'nome_ponto_atendimento' => 'Sede',
        'nome_cliente' => 'EMPRESA EXEMPLO LTDA',
        'numero_cpf_cnpj' => '12345678000195',
        'data_ultima_atualizacao_renda' => '2024-01-05',
        'funcionario_nome' => 'Luis Otavio Santos',
        'data_solicitacao_laudo' => '2024-01-20',
        'tecnico_nome' => 'Ana Paula Técnica',
        'data_atual_sisbr' => '2024-02-15',
        'status_nome' => 'Atualizado',
        'data_cadastro' => '2024-01-05 16:45:00',
        'observacoes' => 'Processo concluído com sucesso'
    ]
];

// Função para formatar CPF/CNPJ
function formatarCpfCnpj($numero) {
    $numero = preg_replace('/\D/', '', $numero);
    
    if (strlen($numero) == 11) {
        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $numero);
    } elseif (strlen($numero) == 14) {
        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $numero);
    }
    
    return $numero;
}

try {
    // Configurar headers para download
    $filename = 'teste_simples_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    // Abrir output
    $output = fopen('php://output', 'w');
    
    // BOM para UTF-8
    fwrite($output, "\xEF\xBB\xBF");

    // Cabeçalhos
    $headers = [
        'ID',
        'PA',
        'Nome do PA',
        'Nome do Cliente',
        'CPF/CNPJ',
        'Última Atualização Renda',
        'Funcionário',
        'Data Solicitação Laudo',
        'Técnico Responsável',
        'Data Atualização SISBR',
        'Status',
        'Data Cadastro',
        'Observações'
    ];

    fputcsv($output, $headers, ';');

    // Dados
    foreach ($registros_exemplo as $registro) {
        $row = [
            $registro['id'],
            $registro['pa'],
            $registro['nome_ponto_atendimento'],
            $registro['nome_cliente'],
            formatarCpfCnpj($registro['numero_cpf_cnpj']),
            !empty($registro['data_ultima_atualizacao_renda']) ? date('d/m/Y', strtotime($registro['data_ultima_atualizacao_renda'])) : '',
            $registro['funcionario_nome'],
            !empty($registro['data_solicitacao_laudo']) ? date('d/m/Y', strtotime($registro['data_solicitacao_laudo'])) : '',
            $registro['tecnico_nome'],
            !empty($registro['data_atual_sisbr']) ? date('d/m/Y', strtotime($registro['data_atual_sisbr'])) : '',
            $registro['status_nome'],
            !empty($registro['data_cadastro']) ? date('d/m/Y H:i:s', strtotime($registro['data_cadastro'])) : '',
            $registro['observacoes']
        ];

        fputcsv($output, $row, ';');
    }

    fclose($output);
    exit;

} catch (Exception $e) {
    echo "Erro: " . $e->getMessage();
    exit;
}
?>
