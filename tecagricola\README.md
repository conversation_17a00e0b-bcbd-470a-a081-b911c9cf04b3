# 🌱 Dashboard MCI - Técnicos Agrícolas

## 📋 Visão Geral

Dashboard específico para monitoramento das métricas dos técnicos agrícolas responsáveis pelas atualizações cadastrais no sistema MCI.

## 🎯 Funcionalidades

### **Métricas dos Técnicos**
- **Meta Individual**: Quantidade total de registros atribuídos ao técnico
- **Atualizações Concluídas**: Registros com status 'Atualizado'
- **Progresso Percentual**: Cálculo automático do desempenho
- **Barra de Progresso Visual**: Anel colorido ao redor da foto

### **Visão Geral da Equipe**
- **Meta Total**: Soma de todos os registros atribuídos
- **Progresso Geral**: Percentual de conclusão da equipe
- **Barra Segmentada**: Contribuição individual de cada técnico

## 🎨 Identidade Visual

### **Paleta de Cores - Identidade Visual Sicoob**
| Técnico | Cor | Código Hex | Descrição |
|---------|-----|------------|-----------|
| **Técnico 1** | Turquesa | `#00AE9D` | Cor principal Sicoob |
| **Técnico 2** | Verde Escuro | `#003641` | Verde institucional |
| **Técnico 3** | Verde Claro | `#C9D200` | Verde vibrante |
| **Técnico 4** | Verde Médio | `#70B86C` | Verde intermediário |
| **Técnico 5** | Roxo | `#494790` | Roxo diferenciação |
| **Técnico 6** | Cinza | `#6c757d` | Cinza neutro |

### **Diferenças do Dashboard de Cadastro**
- **Fundo**: Gradiente verde escuro → turquesa (#003641 → #00AE9D)
- **Cores**: Identidade visual Sicoob com foco agrícola
- **Ícones**: Temática agrícola (🌱 seedling)
- **Título**: "MCI - TÉCNICOS AGRÍCOLAS"
- **Layout**: Mesmo tamanho e responsividade do dashboard de cadastro

## 🔧 Implementação Técnica

### **Consulta SQL Principal**
```sql
SELECT 
    u.id,
    u.nome_completo as nome,
    u.email,
    COUNT(r.id) as meta_total,
    COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
    ROUND(
        (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
    ) as progresso_total
FROM sicoob_access_control.usuarios u
INNER JOIN mci.cad_registros r ON u.id = r.tecnico_responsavel
LEFT JOIN mci.cad_status s ON r.status = s.id
WHERE u.ativo = 1
GROUP BY u.id, u.nome_completo, u.email
HAVING meta_total > 0
ORDER BY u.nome_completo
```

### **Regras de Negócio**
1. **Meta do Técnico**: Total de registros na coluna `tecnico_responsavel`
2. **Registros Concluídos**: Apenas status 'Atualizado'
3. **Progresso**: (Atualizados / Meta Total) × 100
4. **Filtro**: Apenas usuários ativos com registros atribuídos

### **Integração com API Intranet**
- **Fotos dos Técnicos**: Busca automática via email
- **Fallback**: Avatar com inicial do nome
- **Timeout**: 5 segundos para evitar travamentos

## 📊 Layout e Responsividade

### **Estrutura do Dashboard**
```
┌─────────────────────────────────────────────────────────┐
│                    🌱 HEADER AGRÍCOLA                   │
├─────────────────────────────────────────────────────────┤
│                  MÉTRICA GERAL TOTAL                    │
│  [████████████████████████████████████████████████]    │
│  [Lar1][Lar2][Amar][Verde][Azul][AzEsc]               │
├─────────────────────────────────────────────────────────┤
│  [🟠 João]     │  [🟠 Maria]    │  [🟡 Pedro]   │
│  Lar. Vibrante │  Lar. Médio    │  Amarelo      │
├────────────────┼────────────────┼───────────────┤
│  [🟢 Ana]      │  [🔵 Carlos]   │  [🔷 Fernanda]│
│  Verde Água    │  Azul          │  Azul Escuro  │
└─────────────────────────────────────────────────────────┘
```

### **Grid Layout**
- **Desktop/TV**: 3×2 fixo (6 cards máximo)
- **Responsivo**: `clamp()` para todos os tamanhos
- **Flexbox**: Distribuição automática do conteúdo
- **Overflow**: Oculto para manter proporções

## ⏱️ Auto-Refresh

### **Configuração**
- **Intervalo**: 30 segundos
- **Comportamento**: Pausa quando aba não visível
- **Animação**: Efeito pulse antes do reload
- **Otimização**: Evita refresh em background

## 🚀 Como Usar

### **Acesso**
1. Navegar para `/mci/tecagricola/dashboard.php`
2. Login necessário com permissões MCI
3. Visualização automática dos técnicos com registros

### **Teste Visual**
1. Abrir `/mci/tecagricola/test_layout.html`
2. Verificar responsividade em diferentes resoluções
3. Testar efeitos hover e animações

## 📁 Estrutura de Arquivos

```
mci/tecagricola/
├── dashboard.php          # Dashboard principal
├── test_layout.html       # Teste visual
└── README.md             # Esta documentação
```

## 🔄 Manutenção

### **Para Adicionar Novos Técnicos**
1. Sistema detecta automaticamente novos registros
2. Cores são atribuídas ciclicamente (1-6)
3. Fotos buscadas automaticamente via API

### **Para Modificar Cores**
1. Alterar variáveis CSS `--cor-tecnico-X`
2. Atualizar array `$cores_tecnicos` no PHP
3. Testar em diferentes resoluções

### **Para Ajustar Métricas**
1. Modificar consulta SQL principal
2. Ajustar cálculos de progresso
3. Verificar regras de negócio

## 🎯 Diferenças do Dashboard de Cadastro

| Aspecto | Cadastro | Técnicos Agrícolas |
|---------|----------|-------------------|
| **Fundo** | Verde/Turquesa | Laranja/Amarelo |
| **Tema** | Funcionários | Agrícola |
| **Coluna** | `funcionario` | `tecnico_responsavel` |
| **Ícone** | 👥 | 🌱 |
| **Cores** | Frias | Quentes |

## 📱 Compatibilidade

### **Navegadores Suportados**
- ✅ Chrome 88+ 
- ✅ Firefox 85+
- ✅ Safari 14+
- ✅ Edge 88+

### **Resoluções Testadas**
- ✅ 1920×1080 (Full HD)
- ✅ 2560×1440 (2K)
- ✅ 3840×2160 (4K)
- ✅ Mobile/Tablet

---

**Dashboard MCI - Técnicos Agrícolas**  
*Monitoramento especializado para equipe técnica agrícola* 🌱
