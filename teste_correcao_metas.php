<?php
/**
 * <PERSON>ript de Teste - Correção das Metas dos Técnicos Agrícolas
 * Verifica se o filtro de ano foi aplicado corretamente
 */

require_once 'config/config.php';
require_once 'classes/Logger.php';

$logger = new MciLogger();
$logger->log('Teste de correção', 'Verificação do filtro de ano nas metas dos técnicos');

echo "<h1>🧪 Teste de Correção - Metas dos Técnicos Agrícolas</h1>";
echo "<p><strong>Objetivo:</strong> Verificar se o filtro de ano foi aplicado corretamente</p>";
echo "<hr>";

try {
    // 1. TESTE ANTES DA CORREÇÃO (simulação)
    echo "<h2>📊 1. Simulação do Sistema Antes da Correção</h2>";
    
    $query_antiga = "
        SELECT
            u.id,
            u.nome_completo as nome,
            COUNT(r.id) as meta_total_antiga,
            COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total_antiga
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
        GROUP BY u.id, u.nome_completo
        HAVING meta_total_antiga > 0
        ORDER BY meta_total_antiga DESC
        LIMIT 5
    ";
    
    $stmt = $pdo_mci->prepare($query_antiga);
    $stmt->execute();
    $tecnicos_antiga = $stmt->fetchAll();
    
    $total_antiga = 0;
    foreach ($tecnicos_antiga as $tecnico) {
        echo "<p><strong>{$tecnico['nome']} (ID: {$tecnico['id']}):</strong> ";
        echo "Meta Total: " . number_format($tecnico['meta_total_antiga']) . " | ";
        echo "Atualizados: " . number_format($tecnico['atualizados_total_antiga']) . "</p>";
        $total_antiga += $tecnico['meta_total_antiga'];
    }
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📈 Total Antes da Correção:</h3>";
    echo "<p><strong>Meta Total:</strong> " . number_format($total_antiga) . " registros</p>";
    echo "<p><em>Inclui registros de todos os anos (sem filtro)</em></p>";
    echo "</div>";
    
    // 2. TESTE APÓS A CORREÇÃO (sistema atual)
    echo "<h2>✅ 2. Sistema Após a Correção</h2>";
    
    $query_nova = "
        SELECT
            u.id,
            u.nome_completo as nome,
            COUNT(CASE 
                WHEN YEAR(r.data_solicitacao_laudo) = YEAR(NOW()) 
                THEN 1 END) as meta_total_nova,
            COUNT(CASE 
                WHEN s.nome = 'Atualizado' 
                AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                THEN 1 END) as atualizados_total_nova
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 
        AND r.tecnico_responsavel IS NOT NULL
        AND r.data_solicitacao_laudo IS NOT NULL
        GROUP BY u.id, u.nome_completo
        HAVING meta_total_nova > 0
        ORDER BY meta_total_nova DESC
        LIMIT 5
    ";
    
    $stmt = $pdo_mci->prepare($query_nova);
    $stmt->execute();
    $tecnicos_nova = $stmt->fetchAll();
    
    $total_nova = 0;
    foreach ($tecnicos_nova as $tecnico) {
        echo "<p><strong>{$tecnico['nome']} (ID: {$tecnico['id']}):</strong> ";
        echo "Meta Total: " . number_format($tecnico['meta_total_nova']) . " | ";
        echo "Atualizados: " . number_format($tecnico['atualizados_total_nova']) . "</p>";
        $total_nova += $tecnico['meta_total_nova'];
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📈 Total Após a Correção:</h3>";
    echo "<p><strong>Meta Total:</strong> " . number_format($total_nova) . " registros</p>";
    echo "<p><em>Apenas registros do ano atual (com filtro)</em></p>";
    echo "</div>";
    
    // 3. COMPARAÇÃO
    echo "<h2>🔍 3. Comparação e Análise</h2>";
    
    $diferenca = $total_antiga - $total_nova;
    $percentual_reducao = $total_antiga > 0 ? round(($diferenca / $total_antiga) * 100, 1) : 0;
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📊 Resumo da Correção:</h3>";
    echo "<p><strong>Antes da Correção:</strong> " . number_format($total_antiga) . " registros</p>";
    echo "<p><strong>Após a Correção:</strong> " . number_format($total_nova) . " registros</p>";
    echo "<p><strong>Diferença:</strong> " . number_format($diferenca) . " registros</p>";
    echo "<p><strong>Redução:</strong> {$percentual_reducao}%</p>";
    echo "</div>";
    
    // 4. VERIFICAÇÃO DO ALINHAMENTO COM CADASTRO
    echo "<h2>🎯 4. Verificação do Alinhamento com Cadastro</h2>";
    
    $query_cadastro = "
        SELECT
            COUNT(CASE
                WHEN r.data_solicitacao_laudo IS NOT NULL
                AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                THEN 1 ELSE 0
            END) as total_cadastro
        FROM cad_registros r
        WHERE r.funcionario IN (17, 18, 19, 20, 21, 22)
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_cadastro);
    $stmt->execute();
    $cadastro = $stmt->fetch();
    
    $alinhamento = $total_nova == $cadastro['total_cadastro'];
    
    echo "<div style='background: " . ($alinhamento ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🎯 Status do Alinhamento:</h3>";
    echo "<p><strong>Meta dos Técnicos (corrigida):</strong> " . number_format($total_nova) . " registros</p>";
    echo "<p><strong>Meta do Cadastro:</strong> " . number_format($cadastro['total_cadastro']) . " registros</p>";
    
    if ($alinhamento) {
        echo "<p><strong>✅ ALINHAMENTO PERFEITO:</strong> As metas estão idênticas!</p>";
    } else {
        echo "<p><strong>⚠️ AINDA HÁ DIFERENÇA:</strong> " . number_format(abs($total_nova - $cadastro['total_cadastro'])) . " registros</p>";
    }
    echo "</div>";
    
    // 5. DETALHAMENTO DOS REGISTROS EXCLUÍDOS
    if ($diferenca > 0) {
        echo "<h2>📋 5. Detalhamento dos Registros Excluídos</h2>";
        
        $query_excluidos = "
            SELECT 
                COUNT(*) as total_excluidos,
                COUNT(CASE WHEN YEAR(r.data_solicitacao_laudo) = YEAR(NOW()) THEN 1 END) as ano_atual,
                COUNT(CASE WHEN YEAR(r.data_solicitacao_laudo) < YEAR(NOW()) THEN 1 END) as anos_anteriores,
                COUNT(CASE WHEN r.data_solicitacao_laudo IS NULL THEN 1 END) as sem_data
            FROM cad_registros r
            INNER JOIN sicoob_access_control.usuarios u ON r.tecnico_responsavel = u.id
            WHERE u.ativo = 1 
            AND r.tecnico_responsavel IS NOT NULL
            AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        ";
        
        $stmt = $pdo_mci->prepare($query_excluidos);
        $stmt->execute();
        $excluidos = $stmt->fetch();
        
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📊 Registros Excluídos pela Correção:</h3>";
        echo "<ul>";
        echo "<li><strong>Total de registros com técnico:</strong> " . number_format($excluidos['total_excluidos']) . "</li>";
        echo "<li><strong>Registros do ano atual:</strong> " . number_format($excluidos['ano_atual']) . " (incluídos na meta)</li>";
        echo "<li><strong>Registros de anos anteriores:</strong> " . number_format($excluidos['anos_anteriores']) . " (excluídos da meta)</li>";
        echo "<li><strong>Registros sem data:</strong> " . number_format($excluidos['sem_data']) . " (excluídos da meta)</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // 6. CONCLUSÃO
    echo "<h2>💡 6. Conclusão</h2>";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🎯 Resultado da Correção:</h3>";
    
    if ($alinhamento) {
        echo "<p><strong>✅ SUCESSO TOTAL:</strong></p>";
        echo "<ul>";
        echo "<li>As metas dos técnicos e cadastro estão perfeitamente alinhadas</li>";
        echo "<li>O filtro de ano foi aplicado corretamente</li>";
        echo "<li>A divergência foi eliminada</li>";
        echo "</ul>";
    } else {
        echo "<p><strong>⚠️ CORREÇÃO PARCIAL:</strong></p>";
        echo "<ul>";
        echo "<li>O filtro de ano foi aplicado, mas ainda há diferenças</li>";
        echo "<li>Possíveis causas: registros sem data ou outros critérios</li>";
        echo "<li>Investigação adicional necessária</li>";
        echo "</ul>";
    }
    
    echo "<p><strong>🔧 Próximos Passos:</strong></p>";
    echo "<ol>";
    echo "<li>Verificar se há registros sem <code>data_solicitacao_laudo</code></li>";
    echo "<li>Implementar validação para garantir que todos os registros tenham data</li>";
    echo "<li>Monitorar continuamente o alinhamento das metas</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ Erro durante o teste:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    $logger->logFile("Erro no teste de correção: " . $e->getMessage(), 'ERROR');
}

echo "<hr>";
echo "<p><strong>Data do teste:</strong> " . date('d/m/Y H:i:s') . "</p>";
echo "<p><a href='metas.php'>← Voltar para Metas</a></p>";
?>
