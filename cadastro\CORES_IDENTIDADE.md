# 🎨 Dashboard MCI - Cores da Identidade Visual

## 📋 Implementação das Cores

O dashboard foi atualizado para usar as cores da identidade visual Sicoob, com cada funcionário tendo uma cor específica conforme a paleta fornecida.

## 🎯 Cores Implementadas

### **Paleta de Cores dos Funcionários**

| Funcionário | Cor | Código Hex | Descrição |
|-------------|-----|------------|-----------|
| **Funcionário 1** | Turquesa | `#00AE9D` | Cor principal Si<PERSON>ob |
| **Funcionário 2** | Verde Escuro | `#003641` | Verde institucional |
| **Funcionário 3** | Cinza | `#6c757d` | Substitui o branco original |
| **Funcionário 4** | Verde Claro | `#C9D200` | Verde vibrante |
| **Funcionário 5** | Verde Médio | `#70B86C` | Verde intermediário |
| **Funcionário 6** | Roxo | `#494790` | Roxo institucional |

## 🔧 Implementação Técnica

### **Variáveis CSS**
```css
:root {
    /* Cores da identidade visual para funcionários */
    --cor-funcionario-1: #00AE9D; /* Turquesa */
    --cor-funcionario-2: #003641; /* Verde escuro */
    --cor-funcionario-3: #6c757d; /* Cinza (substitui branco) */
    --cor-funcionario-4: #C9D200; /* Verde claro */
    --cor-funcionario-5: #70B86C; /* Verde médio */
    --cor-funcionario-6: #494790; /* Roxo */
}
```

### **Classes Específicas**
```css
.funcionario-1 { --cor-funcionario: var(--cor-funcionario-1); }
.funcionario-2 { --cor-funcionario: var(--cor-funcionario-2); }
.funcionario-3 { --cor-funcionario: var(--cor-funcionario-3); }
.funcionario-4 { --cor-funcionario: var(--cor-funcionario-4); }
.funcionario-5 { --cor-funcionario: var(--cor-funcionario-5); }
.funcionario-6 { --cor-funcionario: var(--cor-funcionario-6); }
```

## 🎨 Aplicação das Cores

### **1. Borda de Progresso da Foto**
- Cada funcionário tem sua borda circular preenchida com sua cor específica
- Progresso calculado em graus (0-360°)
- Fundo cinza claro para área não preenchida

### **2. Valores das Métricas**
- Números das métricas (ex: "68/75") usam a cor do funcionário
- Percentuais mantêm cor padrão (verde escuro)
- Labels mantêm cor padrão

### **3. Barra de Progresso Geral**
- Segmentada por funcionário
- Cada segmento tem a cor correspondente ao funcionário
- Largura proporcional à contribuição individual
- Tooltip com nome e quantidade de atualizações

## 📊 Barra de Progresso Segmentada

### **Cálculo dos Segmentos**
```php
foreach ($funcionarios_dados as $funcionario) {
    $porcentagem_contribuicao = ($funcionario['atualizados_ano'] / $total_meta_anual) * 100;
    $segmentos_progresso[] = [
        'porcentagem' => $porcentagem_contribuicao,
        'cor' => $cores_funcionarios[$funcionario_index],
        'nome' => $funcionario['nome'],
        'atualizados' => $funcionario['atualizados_ano']
    ];
}
```

### **Renderização HTML**
```html
<div class="progress-bar-overview">
    <?php foreach ($segmentos_progresso as $segmento): ?>
        <div class="progress-segment" 
             style="width: <?php echo $segmento['porcentagem']; ?>%; 
                    background-color: <?php echo $segmento['cor']; ?>;"
             title="<?php echo $segmento['nome']; ?>: <?php echo $segmento['atualizados']; ?> atualizações">
        </div>
    <?php endforeach; ?>
</div>
```

## 🎯 Elementos Visuais Atualizados

### **Removidos:**
- ✅ Badge de "Última atualização"
- ✅ Cor única turquesa para todos os funcionários
- ✅ Media queries específicas (substituídas por clamp())

### **Adicionados:**
- ✅ Cores individuais por funcionário
- ✅ Barra de progresso segmentada
- ✅ Tooltips informativos nos segmentos
- ✅ Sistema de classes CSS dinâmicas
- ✅ **Bordas modernas e discretas** com cores específicas
- ✅ **Efeito gradiente sutil** nas bordas dos cards
- ✅ **Responsividade avançada** com clamp() CSS
- ✅ **Melhor aproveitamento do espaço** interno dos cards
- ✅ **Efeitos hover** aprimorados

### **Mantidos:**
- ✅ Layout 3x2 fixo
- ✅ Responsividade para TV
- ✅ Auto-refresh a cada 30 segundos (atualizado)
- ✅ Animações suaves

## 🎨 Melhorias Visuais dos Cards

### **Bordas Modernas e Discretas**
Cada card possui uma borda superior colorida e um efeito gradiente sutil:

```css
.funcionario-card {
    border-top: 3px solid var(--cor-funcionario);
    border-radius: 16px;
    position: relative;
}

.funcionario-card::before {
    background: linear-gradient(135deg,
        var(--cor-funcionario) 0%,
        transparent 50%,
        var(--cor-funcionario) 100%);
    opacity: 0.3;
}
```

### **Responsividade Avançada**
Uso extensivo de `clamp()` para adaptação automática:

```css
/* Tamanhos responsivos */
padding: clamp(0.8rem, 2vw, 1.5rem);
font-size: clamp(0.9rem, 2.2vw, 1.4rem);
width: clamp(60px, 8vw, 90px);
margin-bottom: clamp(0.3rem, 1vw, 0.8rem);
```

### **Melhor Aproveitamento do Espaço**
- **Flexbox otimizado**: `justify-content: space-between`
- **Elementos flexíveis**: Métricas expandem para preencher espaço
- **Quebra de linha inteligente**: `word-wrap: break-word`
- **Hifenização automática**: `hyphens: auto`

### **Efeitos Hover Aprimorados**
```css
.funcionario-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.funcionario-card:hover::before {
    opacity: 0.5; /* Intensifica gradiente */
}
```

## 🔍 Mapeamento Funcionário → Cor

O sistema mapeia automaticamente cada funcionário para sua cor:

```php
$funcionario_index = 1; // Inicia em 1
foreach ($funcionarios_dados as $funcionario) {
    // Card recebe classe "funcionario-{$funcionario_index}"
    // CSS aplica a cor correspondente via variável --cor-funcionario
    $funcionario_index++;
}
```

## 📱 Compatibilidade

### **Navegadores Suportados:**
- ✅ Chrome 88+ (CSS Custom Properties)
- ✅ Firefox 85+ (CSS Custom Properties)
- ✅ Safari 14+ (CSS Custom Properties)
- ✅ Edge 88+ (CSS Custom Properties)

### **Recursos Utilizados:**
- CSS Custom Properties (variáveis)
- Conic Gradient (bordas circulares)
- Flexbox (barra segmentada)
- CSS Grid (layout 3x2)

## 🎨 Exemplo Visual

```
┌─────────────────────────────────────────────────────────┐
│                    HEADER DASHBOARD                     │
├─────────────────────────────────────────────────────────┤
│                  MÉTRICA GERAL ANUAL                    │
│  [████████████████████████████████████████████████]    │
│  [Turq][Verde][Cinza][V.Claro][V.Médio][Roxo]          │
├─────────────────────────────────────────────────────────┤
│  [🔵 Ana]     │  [🟢 Bruno]    │  [⚫ Carlos]   │
│  Turquesa     │  Verde Escuro  │  Cinza        │
├───────────────┼────────────────┼───────────────┤
│  [🟡 Diana]   │  [🟢 Eduardo]  │  [🟣 Fernanda]│
│  Verde Claro  │  Verde Médio   │  Roxo         │
└─────────────────────────────────────────────────────────┘
```

## ⏱️ Sistema de Auto-Refresh

### **Configuração Atual:**
- **Intervalo**: 30 segundos
- **Comportamento**: Pausa quando a aba não está visível
- **Animação**: Efeito de pulse antes do reload
- **Otimização**: Evita refresh desnecessário em background

### **Implementação JavaScript:**
```javascript
// Auto-refresh da página a cada 30 segundos
let refreshTimer = setTimeout(() => {
    document.body.classList.add('refreshing');
    setTimeout(() => {
        location.reload();
    }, 1000);
}, 30000); // 30 segundos

// Pausar refresh quando a página não está visível
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        clearTimeout(refreshTimer);
    } else {
        refreshTimer = setTimeout(() => {
            // Reinicia timer quando volta a ser visível
        }, 30000);
    }
});
```

### **Para Alterar o Intervalo:**
1. Modificar valor `30000` (em millisegundos)
2. Atualizar comentários correspondentes
3. Considerar impacto no servidor e experiência do usuário

## 🔄 Manutenção

### **Para Adicionar Novas Cores:**
1. Adicionar nova variável CSS `--cor-funcionario-X`
2. Adicionar nova classe `.funcionario-X`
3. Incluir cor no array `$cores_funcionarios` no PHP
4. Testar em diferentes resoluções

### **Para Modificar Cores Existentes:**
1. Alterar valor da variável CSS correspondente
2. Atualizar array PHP se necessário
3. Verificar contraste e legibilidade

---

**Dashboard MCI - Identidade Visual Sicoob**  
*Cores personalizadas por funcionário com barra de progresso segmentada*
