-- Script para criar tabela cad_status e ajustar cad_registros
-- Execute este script para implementar o sistema de status

USE mci;

-- 1. Criar tabela cad_status
CREATE TABLE IF NOT EXISTS cad_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(50) NOT NULL UNIQUE COMMENT 'Nome do status',
    descricao VARCHAR(255) COMMENT 'Descrição do status',
    cor VARCHAR(20) DEFAULT '#6c757d' COMMENT 'Cor para exibição (hex)',
    ativo BOOLEAN DEFAULT TRUE COMMENT 'Status ativo/inativo',
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de criação',
    INDEX idx_nome (nome),
    INDEX idx_ativo (ativo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela de status dos registros';

-- 2. Inserir status padrão
INSERT INTO cad_status (nome, descricao, cor) VALUES 
('Pendente', 'Registro pendente de atualização', '#ffc107'),
('Atualizado', 'Registro atualizado e processado', '#28a745')
ON DUPLICATE KEY UPDATE 
    descricao = VALUES(descricao),
    cor = VALUES(cor);

-- 3. Verificar se a coluna status já existe na tabela cad_registros
-- Se não existir, criar como VARCHAR temporariamente
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'mci' 
    AND TABLE_NAME = 'cad_registros' 
    AND COLUMN_NAME = 'status'
);

-- Adicionar coluna status se não existir
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE cad_registros ADD COLUMN status VARCHAR(50) DEFAULT ''Pendente'' COMMENT ''Status do registro'' AFTER usuario_cadastro',
    'SELECT ''Coluna status já existe'' as info'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Atualizar registros existentes para usar 'Pendente' como padrão
UPDATE cad_registros 
SET status = 'Pendente' 
WHERE status IS NULL OR status = '' OR status NOT IN ('Pendente', 'Atualizado');

-- 5. Alterar coluna status para usar chave estrangeira
-- Primeiro, vamos alterar o tipo da coluna para INT
ALTER TABLE cad_registros 
MODIFY COLUMN status INT DEFAULT 1 COMMENT 'ID do status (referência para cad_status)';

-- 6. Atualizar os valores existentes para usar IDs
UPDATE cad_registros r
JOIN cad_status s ON s.nome = 'Pendente'
SET r.status = s.id
WHERE r.status IS NULL OR r.status = 0;

-- 7. Adicionar chave estrangeira
ALTER TABLE cad_registros 
ADD CONSTRAINT fk_registros_status 
FOREIGN KEY (status) REFERENCES cad_status(id) 
ON UPDATE CASCADE ON DELETE RESTRICT;

-- 8. Adicionar índice para melhor performance
ALTER TABLE cad_registros 
ADD INDEX idx_status (status);

-- 9. Verificar estrutura final
DESCRIBE cad_registros;
SELECT * FROM cad_status;

-- 10. Mostrar contagem por status
SELECT 
    s.nome as status_nome,
    s.descricao,
    s.cor,
    COUNT(r.id) as total_registros
FROM cad_status s
LEFT JOIN cad_registros r ON r.status = s.id
WHERE s.ativo = TRUE
GROUP BY s.id, s.nome, s.descricao, s.cor
ORDER BY s.id;

SELECT 'Tabela cad_status criada e relacionamento configurado com sucesso!' as resultado;
