[2025-06-30 00:00:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:00:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:00:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:00:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:03:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:03:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:04:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:04:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:04:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:04:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:04:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:04:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:05:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:05:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:05:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:05:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:08:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:08:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:08:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:08:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:09:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:09:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:09:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:09:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:09:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:09:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:10:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:10:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:13:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:13:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:13:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:13:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:14:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:14:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:14:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:14:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:14:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:14:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:15:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:15:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:17:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:17:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:18:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:18:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:18:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:18:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:19:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:19:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:19:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:19:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:20:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:20:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:22:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:22:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:23:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:23:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:23:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:23:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:23:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:23:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:24:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:24:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:24:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:24:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:27:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:27:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:28:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:28:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:28:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:28:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:28:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:28:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:29:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:29:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:29:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:29:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:32:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:32:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:32:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:32:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:33:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:33:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:33:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:33:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:34:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:34:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:34:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:34:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:37:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:37:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:37:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:37:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:38:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:38:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:38:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:38:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:38:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:38:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:39:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:39:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:41:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:41:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:42:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:42:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:42:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:42:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:43:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:43:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:43:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:43:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:44:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:44:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:46:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:46:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:47:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:47:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:47:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:47:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:47:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:47:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:48:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:48:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:48:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:48:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:51:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:51:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:52:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:52:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:52:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:52:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:52:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:52:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:53:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:53:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:53:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:53:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:56:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:56:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:56:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:56:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:57:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:57:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:57:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:57:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:58:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:58:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 00:58:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 00:58:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:01:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:01:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:01:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:01:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:02:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:02:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:02:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:02:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:02:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:02:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:03:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:03:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:05:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:05:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:06:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:06:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:06:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:06:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:07:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:07:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:07:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:07:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:08:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:08:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:10:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:10:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:11:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:11:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:11:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:11:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:11:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:11:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:12:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:12:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:12:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:12:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:15:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:15:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:16:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:16:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:16:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:16:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:16:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:16:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:17:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:17:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:17:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:17:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:20:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:20:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:20:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:20:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:21:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:21:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:21:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:21:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:22:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:22:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:22:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:22:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:25:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:25:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:25:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:25:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:26:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:26:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:26:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:26:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:26:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:26:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:27:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:27:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:29:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:29:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:30:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:30:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:30:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:30:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:31:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:31:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:31:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:31:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:32:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:32:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:34:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:34:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:35:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:35:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:35:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:35:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:35:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:35:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:36:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:36:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:36:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:36:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:39:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:39:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:40:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:40:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:40:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:40:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:40:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:40:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:41:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:41:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:41:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:41:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:44:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:44:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:44:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:44:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:45:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:45:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:45:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:45:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:46:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:46:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:46:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:46:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:49:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:49:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:49:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:49:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:50:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:50:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:50:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:50:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:50:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:50:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:51:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:51:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:53:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:53:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:54:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:54:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:55:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:55:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:55:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:55:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:55:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:55:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:56:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:56:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:58:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:58:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:59:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:59:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:59:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:59:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 01:59:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 01:59:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:00:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:00:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:01:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:01:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:03:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:03:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:04:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:04:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:04:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:04:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:04:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:04:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:05:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:05:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:05:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:05:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:08:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:08:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:08:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:08:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:09:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:09:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:09:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:09:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:10:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:10:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:10:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:10:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:13:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:13:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:13:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:13:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:14:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:14:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:14:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:14:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:14:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:14:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:15:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:15:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:17:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:17:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:18:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:18:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:19:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:19:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:19:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:19:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:19:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:19:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:20:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:20:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:22:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:22:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:23:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:23:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:23:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:23:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:23:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:23:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:24:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:24:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:24:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:24:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:27:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:27:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:28:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:28:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:28:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:28:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:28:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:28:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:29:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:29:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:29:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:29:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:32:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:32:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:32:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:32:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:33:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:33:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:33:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:33:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:34:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:34:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:34:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:34:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:37:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:37:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:37:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:37:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:38:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:38:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:38:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:38:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:38:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:38:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:39:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:39:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:41:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:41:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:42:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:42:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:43:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:43:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:43:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:43:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:43:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:43:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:44:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:44:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:46:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:46:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:47:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:47:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:47:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:47:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:47:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:47:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:48:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:48:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:49:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:49:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:51:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:51:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:52:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:52:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:52:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:52:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:52:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:52:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:53:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:53:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:53:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:53:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:56:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:56:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:56:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:56:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:57:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:57:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:57:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:57:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:58:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:58:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 02:58:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 02:58:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:01:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:01:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:01:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:01:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:02:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:02:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:02:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:02:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:02:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:02:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:03:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:03:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:06:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:06:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:06:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:06:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:07:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:07:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:07:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:07:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:07:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:07:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:08:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:08:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:10:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:10:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:11:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:11:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:11:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:11:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:12:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:12:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:12:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:12:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:13:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:13:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:15:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:15:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:16:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:16:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:16:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:16:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:16:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:16:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:17:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:17:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:17:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:17:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:20:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:20:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:20:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:20:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:21:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:21:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:21:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:21:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:22:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:22:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:22:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:22:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:25:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:25:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:25:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:25:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:26:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:26:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:26:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:26:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:26:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:26:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:27:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:27:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:30:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:30:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:30:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:30:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:31:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:31:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:31:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:31:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:31:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:31:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:32:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:32:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:34:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:34:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:35:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:35:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:35:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:35:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:36:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:36:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:36:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:36:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:37:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:37:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:39:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:39:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:40:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:40:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:40:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:40:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:40:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:40:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:41:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:41:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:41:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:41:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:44:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:44:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:44:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:44:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:45:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:45:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:45:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:45:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:46:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:46:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:46:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:46:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:49:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:49:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:49:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:49:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:50:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:50:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:50:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:50:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:50:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:50:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:51:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:51:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:54:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:54:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:54:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:54:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:55:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:55:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:55:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:55:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:55:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:55:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:56:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:56:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:58:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:58:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:59:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:59:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 03:59:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 03:59:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:00:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:00:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:00:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:00:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:01:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:01:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:03:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:03:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:04:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:04:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:04:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:04:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:04:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:04:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:05:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:05:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:05:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:05:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:08:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:08:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:08:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:08:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:09:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:09:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:09:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:09:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:10:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:10:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:10:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:10:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:13:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:13:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:13:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:13:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:14:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:14:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:14:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:14:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:14:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:14:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:15:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:15:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:18:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:18:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:18:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:18:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:19:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:19:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:19:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:19:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:19:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:19:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:20:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:20:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:22:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:22:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:23:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:23:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:23:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:23:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:24:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:24:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:24:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:24:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:25:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:25:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:27:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:27:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:28:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:28:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:28:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:28:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:28:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:28:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:29:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:29:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:29:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:29:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:32:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:32:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:33:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:33:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:33:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:33:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:33:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:33:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:34:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:34:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:34:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:34:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:37:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:37:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:37:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:37:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:38:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:38:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:38:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:38:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:38:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:38:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:39:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:39:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:42:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:42:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:42:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:42:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:43:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:43:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:43:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:43:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:43:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:43:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:44:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:44:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:46:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:46:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:47:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:47:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:47:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:47:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:48:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:48:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:48:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:48:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:49:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:49:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:51:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:51:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:52:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:52:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:52:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:52:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:52:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:52:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:53:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:53:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:53:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:53:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:56:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:56:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:56:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:56:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:57:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:57:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:57:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:57:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:58:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:58:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 04:58:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 04:58:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:01:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:01:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:01:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:01:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:02:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:02:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:02:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:02:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:02:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:02:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:03:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:03:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:06:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:06:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:06:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:06:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:07:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:07:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:07:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:07:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:07:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:07:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:08:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:08:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:10:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:10:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:11:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:11:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:11:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:11:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:12:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:12:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:12:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:12:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:13:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:13:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:15:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:15:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:16:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:16:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:16:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:16:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:16:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:16:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:17:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:17:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:17:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:17:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:20:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:20:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:20:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:20:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:21:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:21:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:21:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:21:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:22:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:22:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:22:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:22:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:25:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:25:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:25:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:25:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:26:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:26:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:26:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:26:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:26:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:26:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:27:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:27:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:30:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:30:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:30:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:30:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:31:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:31:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:31:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:31:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:31:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:31:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:32:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:32:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:34:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:34:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:35:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:35:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:35:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:35:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:36:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:36:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:36:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:36:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:37:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:37:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:39:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:39:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:40:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:40:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:40:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:40:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:40:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:40:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:41:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:41:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:41:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:41:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:44:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:44:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:44:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:44:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:45:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:45:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:45:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:45:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:46:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:46:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:46:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:46:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:49:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:49:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:49:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:49:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:50:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:50:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:50:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:50:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:50:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:50:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:51:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:51:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:54:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:54:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:54:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:54:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:55:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:55:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:55:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:55:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:55:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:55:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:56:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:56:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:58:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:58:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:59:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:59:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 05:59:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 05:59:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:00:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:00:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:00:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:00:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:01:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:01:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:03:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:03:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:04:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:04:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:04:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:04:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:04:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:04:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:05:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:05:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:05:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:05:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:08:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:08:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:09:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:09:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:09:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:09:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:09:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:09:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:10:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:10:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:10:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:10:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:13:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:13:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:13:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:13:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:14:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:14:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:14:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:14:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:14:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:14:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:15:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:15:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:18:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:18:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:18:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:18:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:19:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:19:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:19:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:19:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:19:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:19:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:20:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:20:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:22:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:22:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:23:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:23:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:23:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:23:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:24:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:24:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:24:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:24:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:25:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:25:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:27:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:27:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:28:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:28:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:28:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:28:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:28:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:28:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:29:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:29:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:29:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:29:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:32:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:32:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:32:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:32:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:33:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:33:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:33:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:33:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:34:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:34:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:34:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:34:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:37:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:37:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:37:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:37:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:38:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:38:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:38:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:38:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:39:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:39:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:39:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:39:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:42:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:42:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:42:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:42:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:43:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:43:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:43:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:43:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:43:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:43:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:44:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:44:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:46:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:46:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:47:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:47:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:47:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:47:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:48:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:48:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:48:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:48:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:49:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:49:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:51:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:51:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:52:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:52:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:52:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:52:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:52:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:52:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:53:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:53:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:53:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:53:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:56:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:56:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:57:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:57:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:57:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:57:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:57:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:57:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:58:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:58:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 06:58:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 06:58:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:01:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:01:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:01:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:01:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:02:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:02:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:02:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:02:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:03:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:03:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:03:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:03:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:06:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:06:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:06:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:06:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:07:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:07:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:07:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:07:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:07:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:07:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:08:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:08:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:10:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:10:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:11:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:11:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:11:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:11:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:12:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:12:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:12:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:12:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:13:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:13:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:15:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:15:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:16:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:16:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:16:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:16:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:16:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:16:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:17:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:17:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:17:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:17:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:20:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:20:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:21:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:21:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:21:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:21:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:21:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:21:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:22:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:22:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:22:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:22:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:25:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:25:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:25:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:25:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:26:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:26:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:26:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:26:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:27:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:27:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:27:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:27:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:30:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:30:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:30:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:30:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:31:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:31:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:31:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:31:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:31:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:31:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:32:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:32:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:34:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:34:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:35:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:35:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:35:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:35:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:36:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:36:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:36:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:36:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:37:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:37:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:39:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:39:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:40:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:40:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:40:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:40:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:40:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:40:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:41:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:41:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:41:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:41:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:44:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:44:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:45:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:45:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:45:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:45:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:45:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:45:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:46:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:46:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:46:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:46:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:49:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:49:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:49:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:49:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:50:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:50:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:50:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:50:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:51:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:51:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:51:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:51:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:54:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:54:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:54:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:54:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:55:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:55:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:55:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:55:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:55:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:55:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:56:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:56:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:58:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:58:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:59:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:59:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 07:59:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 07:59:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:00:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:00:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:00:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:00:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:01:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:01:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:03:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:03:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:04:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:04:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:04:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:04:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:04:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:04:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:05:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:05:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:05:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:05:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:08:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:08:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:09:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:09:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:09:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:09:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:09:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:09:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:10:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:10:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:10:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:10:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:13:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:13:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:13:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:13:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:14:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:14:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:14:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:14:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:15:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:15:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:15:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:15:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:18:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:18:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:18:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:18:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:19:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:19:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:19:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:19:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:19:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:19:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:20:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:20:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:22:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:22:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:23:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:23:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:23:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:23:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:24:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:24:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:24:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:24:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:25:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:25:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:27:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:27:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:28:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:28:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:28:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:28:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:28:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:28:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:29:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:29:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:29:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:29:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:32:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:32:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:33:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:33:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:33:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:33:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:33:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:33:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:34:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:34:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:34:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:34:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:37:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:37:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:37:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:37:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:38:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:38:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:38:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:38:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:39:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:39:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:39:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:39:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:42:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:42:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:42:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:42:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:43:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:43:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:43:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:43:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:43:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:43:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:44:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:44:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:46:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:46:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:47:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:47:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:48:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:48:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:48:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:48:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:48:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:48:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:49:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:49:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:51:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:51:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:52:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:52:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:52:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:52:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:52:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:52:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:53:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:53:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:54:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:54:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:56:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:56:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:57:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:57:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:57:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:57:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:57:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:57:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:58:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:58:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 08:58:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 08:58:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:01:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:01:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:01:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:01:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:02:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:02:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:02:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:02:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:03:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:03:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:03:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:03:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:06:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:06:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:06:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:06:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:07:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:07:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:07:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:07:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:07:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:07:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:08:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:08:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:11:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:11:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:11:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:11:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:12:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:12:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:12:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:12:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:12:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:12:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:13:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:13:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:15:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:15:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:16:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:16:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:16:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:16:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:17:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:17:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:17:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:17:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:18:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:18:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:20:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:20:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:21:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:21:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:21:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:21:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:21:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:21:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:22:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:22:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:22:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:22:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:25:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:25:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:25:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:25:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:26:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:26:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:26:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:26:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:27:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:27:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:27:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:27:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:30:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:30:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:30:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:30:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:30:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:30:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:30:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:30:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:31:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:31:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:31:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:31:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:31:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:31:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:32:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:32:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:32:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:32:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:32:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:32:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:32:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:32:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:34:29] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: ANANIAS MARQUES FILHO (CPF/CNPJ: 80362850704) | Justificativa: Cadastro em atualização no PA.
[2025-06-30 09:35:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:35:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:35:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:35:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:36:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:36:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:36:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:36:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:36:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:36:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:37:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:37:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:39:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:39:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:40:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:40:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:40:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:40:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:41:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:41:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:41:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:41:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:42:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:42:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:44:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:44:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:45:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:45:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:45:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:45:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:45:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:45:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:46:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:46:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:46:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:46:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:49:07] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40818 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 13/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 09:49:22] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40311 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 09:49:26] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40828 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 16/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 09:49:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:49:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:50:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:50:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:50:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:50:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:50:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:50:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:51:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:51:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:51:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:51:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:54:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:54:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:54:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:54:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:55:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:55:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:55:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:55:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:55:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:55:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:55:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:55:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:56:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:56:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:56:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:56:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:56:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:56:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:59:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:59:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 09:59:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 09:59:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:00:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:00:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:00:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:00:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:00:30] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39903 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 14/03/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-30 10:00:44] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40792 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 16/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 10:00:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:00:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:01:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:01:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:03:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:03:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:04:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:04:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:04:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:04:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:05:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:05:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:05:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:05:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:05:46] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40310 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 10:06:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:06:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:08:24] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40775 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 16/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 10:08:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:08:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:09:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:09:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:09:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:09:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:09:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39908 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 14/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 10:09:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:09:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:10:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:10:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:10:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:10:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:13:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:13:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:14:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:14:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:14:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:14:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:14:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:14:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:15:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:15:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:15:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:15:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:18:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:18:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:18:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:18:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:19:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:19:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:19:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:19:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:20:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:20:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:20:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:20:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:22:35] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40302 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 10:23:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:23:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:23:12] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39909 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 21/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 10:23:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:23:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:24:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:24:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:24:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:24:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:24:28] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40772 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 16/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 10:24:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:24:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:25:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:25:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:26:55] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40339 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 10:27:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:27:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:28:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:28:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:28:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:28:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:29:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:29:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:29:17] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39912 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 21/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 10:29:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:29:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:30:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:30:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:32:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:32:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:33:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:33:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:33:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:33:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:33:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:33:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:34:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:34:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:34:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:34:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:35:53] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40322 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 10:37:18] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39913 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 21/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 10:37:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:37:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:38:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:38:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:38:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:38:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:38:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:38:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:39:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:39:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:39:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:39:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:42:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:42:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:42:37] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39947 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/03/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 10:42:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:42:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:43:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:43:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:43:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:43:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:44:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:44:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:44:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:44:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:47:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:47:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:47:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:47:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:48:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:48:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:48:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:48:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:48:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:48:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:49:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:49:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:49:28] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40131 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Sidnei Gama | SISBR: 11/04/2025
[2025-06-30 10:50:50] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40130 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 24/04/2025
[2025-06-30 10:51:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:51:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:51:57] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40159 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Sidnei Gama | SISBR: 02/06/2025
[2025-06-30 10:52:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:52:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:52:49] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40167 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 27/06/2025
[2025-06-30 10:52:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:52:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:53:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:53:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:53:33] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40398 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 10:53:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:53:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:53:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40175 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 12/06/2025
[2025-06-30 10:54:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:54:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:54:56] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40194 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 15/05/2025
[2025-06-30 10:55:17] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40173 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 15/05/2025
[2025-06-30 10:56:03] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40195 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 11/06/2025
[2025-06-30 10:56:29] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40196 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 15/05/2025
[2025-06-30 10:56:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:56:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:56:57] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40192 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 02/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 24/04/2025
[2025-06-30 10:57:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:57:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:57:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:57:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:57:54] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40205 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 19/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 06/06/2025
[2025-06-30 10:57:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:57:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:58:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:58:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 10:58:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 10:58:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:00:57] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40206 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 19/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 06/06/2025
[2025-06-30 11:01:20] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40207 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 19/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 17/06/2025
[2025-06-30 11:01:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:01:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:01:59] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40295 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 19/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 09/06/2025
[2025-06-30 11:02:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:02:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:02:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:02:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:02:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:02:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:03:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:03:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:03:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:03:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:03:52] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: JOSE ROBERTO MENDES (CPF/CNPJ: 40792374649) | Justificativa: Cadastro em atualização no PA.
[2025-06-30 11:03:59] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40770 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 16/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 11:06:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:06:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:06:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:06:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:07:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:07:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:07:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:07:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:08:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:08:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:08:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:08:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:11:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:11:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:11:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:11:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:12:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:12:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:12:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:12:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:12:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:12:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:12:58] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40771 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 16/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 11:13:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:13:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:15:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:15:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:16:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:16:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:17:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:17:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:17:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:17:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:17:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:17:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:17:52] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: LUIZ AUGUSTO GARCIA DE PAULA (CPF/CNPJ: 13761622619) | Justificativa: LAUDO EXCLUÍDO PELO PA.
[2025-06-30 11:18:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:18:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:20:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:20:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:21:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:21:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:21:20] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40152 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/04/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 11:21:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:21:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:21:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:21:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:22:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:22:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:23:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:23:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:25:08] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40080 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Sidnei Gama
[2025-06-30 11:25:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:25:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:25:59] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40721 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 11:26:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:26:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:26:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:26:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:26:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:26:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:27:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:27:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:27:43] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40357 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 29/05/2025
[2025-06-30 11:27:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:27:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:28:07] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40364 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 29/05/2025
[2025-06-30 11:28:42] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40366 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 25/05/2025
[2025-06-30 11:29:38] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40330 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/05/2025
[2025-06-30 11:30:02] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40347 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 29/05/2025
[2025-06-30 11:30:22] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40292 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 30/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 05/06/2025
[2025-06-30 11:30:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:30:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:30:46] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40293 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 29/05/2025
[2025-06-30 11:30:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:30:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:31:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:31:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:31:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:31:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:32:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:32:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:32:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:32:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:35:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:35:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:35:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:35:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:36:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:36:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:36:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:36:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:36:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:36:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:37:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:37:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:40:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:40:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:40:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:40:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:40:55] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: ANTONIO DANIEL FILHO (CPF/CNPJ: 71035761653) | Justificativa: Cadastro em atualização no PA.
[2025-06-30 11:41:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:41:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:41:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:41:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:41:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:41:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:42:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:42:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:43:01] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40385 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 30/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-30 11:43:25] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40722 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 11:44:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:44:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:45:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:45:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:45:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:45:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:46:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:46:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:46:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:46:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:47:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:47:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:48:40] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40388 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 21/05/2025
[2025-06-30 11:49:33] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40394 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/05/2025
[2025-06-30 11:49:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:49:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:50:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:50:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:50:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:50:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:50:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:50:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:51:09] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40331 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 08/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 18/06/2025
[2025-06-30 11:51:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:51:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:51:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:51:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:53:52] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40315 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 20/06/2025
[2025-06-30 11:54:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:54:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:54:30] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40316 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 21/05/2025
[2025-06-30 11:55:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:55:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:55:14] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40294 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 21/05/2025
[2025-06-30 11:55:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:55:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:55:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:55:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:55:40] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40237 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 02/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 12/05/2025
[2025-06-30 11:56:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:56:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:56:35] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40723 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 04/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 11:56:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:56:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:57:43] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40238 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 09/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 21/05/2025
[2025-06-30 11:59:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:59:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 11:59:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 11:59:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:00:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:00:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:00:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:00:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:01:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:01:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:01:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:01:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:02:18] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40684 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 16/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 12:04:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:04:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:04:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:04:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:05:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:05:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:05:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:05:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:05:28] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40817 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 20/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 12:05:31] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40698 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 18/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-30 12:05:37] [INFO] MCI - Reativação de registro: MCI - Reativação de registro: ANTONIO DANIEL FILHO (CPF/CNPJ: 71035761653)
[2025-06-30 12:05:40] [INFO] MCI - Reativação de registro: MCI - Reativação de registro: JOSE ROBERTO MENDES (CPF/CNPJ: 40792374649)
[2025-06-30 12:05:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:05:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:06:01] [INFO] MCI - Reativação de registro: MCI - Reativação de registro: CLEOMAR ANDRE PRATA BERTOLASSE (CPF/CNPJ: 12307014608)
[2025-06-30 12:06:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:06:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:06:25] [INFO] MCI - Reativação de registro: MCI - Reativação de registro: JOSE GERALDO MENDES CAMPOS (CPF/CNPJ: 57019940668)
[2025-06-30 12:08:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:08:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:09:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:09:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:09:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:09:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:10:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:10:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:10:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:10:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:11:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:11:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:13:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:13:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:14:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:14:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:14:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:14:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:14:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:14:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:15:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:15:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:15:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:15:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:18:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:18:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:19:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:19:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:19:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:19:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:19:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:19:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:20:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:20:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:20:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:20:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:23:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:23:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:23:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:23:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:24:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:24:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:24:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:24:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:25:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:25:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:25:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:25:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:28:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:28:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:28:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:28:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:29:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:29:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:29:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:29:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:29:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:29:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:30:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:30:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:32:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:32:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:33:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:33:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:34:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:34:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:34:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:34:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:34:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:34:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:35:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:35:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:37:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:37:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:38:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:38:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:38:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:38:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:38:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:38:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:39:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:39:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:40:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:40:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:42:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:42:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:43:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:43:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:43:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:43:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:43:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:43:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:44:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:44:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:44:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:44:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:47:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:47:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:47:28] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40666 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 18/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 12:47:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:47:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:48:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:48:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:48:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:48:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:49:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:49:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:49:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:49:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:52:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:52:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:52:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:52:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:53:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:53:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:53:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:53:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:53:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:53:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:54:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:54:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:56:09] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40483 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 04/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 12:57:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:57:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:57:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:57:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:58:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:58:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:58:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:58:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:58:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:58:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 12:59:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 12:59:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:01:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:01:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:02:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:02:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:02:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:02:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:03:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:03:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:03:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:03:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:04:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:04:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:06:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:06:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:07:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:07:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:07:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:07:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:07:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:07:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:08:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:08:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:08:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:08:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:11:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:11:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:11:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:11:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:12:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:12:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:12:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:12:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:13:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:13:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:13:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:13:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:16:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:16:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:16:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:16:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:17:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:17:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:17:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:17:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:17:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:17:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:18:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:18:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:21:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:21:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:21:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:21:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:22:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:22:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:22:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:22:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:22:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:22:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:23:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:23:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:25:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:25:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:26:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:26:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:26:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:26:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:27:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:27:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:27:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:27:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:28:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:28:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:30:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:30:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:31:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:31:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:31:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:31:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:31:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:31:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:32:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:32:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:32:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:32:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:35:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:35:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:36:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:36:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:36:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:36:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:36:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:36:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:37:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:37:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:37:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:37:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:40:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:40:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:40:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:40:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:41:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:41:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:41:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:41:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:42:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:42:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:42:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:42:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:45:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:45:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:45:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:45:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:46:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:46:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:46:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:46:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:46:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:46:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:47:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:47:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:49:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:49:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:50:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:50:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:51:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:51:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:51:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:51:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:51:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:51:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:52:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:52:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:54:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:54:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:55:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:55:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:55:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:55:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:55:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:55:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:56:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:56:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:57:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:57:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 13:59:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 13:59:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:00:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:00:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:00:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:00:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:00:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:00:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:01:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:01:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:01:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:01:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:04:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:04:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:04:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:04:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:05:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:05:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:05:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:05:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:06:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:06:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:06:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:06:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:09:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:09:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:09:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:09:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:10:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:10:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:10:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:10:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:10:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:10:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:11:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:11:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:14:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:14:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:14:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:14:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:15:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:15:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:15:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:15:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:15:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:15:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:16:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:16:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:18:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:18:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:19:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:19:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:19:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:19:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:20:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:20:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:20:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:20:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:21:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:21:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:23:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:23:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:24:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:24:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:24:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:24:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:24:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:24:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:25:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:25:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:25:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:25:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:28:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:28:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:28:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:28:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:29:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:29:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:29:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:29:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:30:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:30:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:30:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:30:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:33:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:33:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:33:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:33:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:34:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:34:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:34:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:34:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:34:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:34:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:35:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:35:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:38:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:38:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:38:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:38:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:39:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:39:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:39:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:39:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:39:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:39:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:40:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:40:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:41:29] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40397 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 14:42:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:42:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:43:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:43:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:43:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:43:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:44:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:44:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:44:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:44:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:45:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:45:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:47:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:47:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:48:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:48:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:48:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:48:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:48:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:48:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:49:02] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40390 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 14:49:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:49:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:49:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:49:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:52:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:52:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:52:35] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: JOSE CLAUDIO VALENTIM FRANKLIN (CPF/CNPJ: 09559860607) | Justificativa: LAUDO EXCLUÍDO PELO PA.
[2025-06-30 14:53:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:53:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:53:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:53:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:53:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:53:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:54:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:54:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:54:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:54:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:55:27] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39666 | Funcionário: Luana Viana Ramos | Solicitação Laudo: 03/02/2025 | Técnico: Daniel Carlos Muniz | SISBR: 20/02/2025
[2025-06-30 14:55:40] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40417 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 14:57:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:57:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:57:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:57:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:58:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:58:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:58:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:58:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:59:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:59:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 14:59:31] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40018 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 13/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 14:59:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 14:59:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:02:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:02:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:02:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:02:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:03:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:03:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:03:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:03:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:03:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:03:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:04:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:04:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:05:54] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40416 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 15:06:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:06:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:07:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:07:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:07:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:07:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:08:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:08:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:08:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:08:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:09:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:09:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:11:35] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40448 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 15:11:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:11:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:12:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:12:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:12:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:12:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:12:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:12:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:13:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:13:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:13:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:13:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:16:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:16:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:17:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:17:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:17:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:17:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:17:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:17:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:18:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:18:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:18:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:18:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:21:15] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39946 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 14/03/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 15:21:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:21:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:21:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:21:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:22:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:22:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:22:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:22:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:23:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:23:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:23:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:23:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:26:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:26:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:26:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:26:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:27:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:27:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:27:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:27:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:27:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:27:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:28:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:28:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:28:43] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40499 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 15:31:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:31:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:31:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:31:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:32:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:32:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:32:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:32:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:32:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:32:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:33:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:33:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:35:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:35:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:35:52] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40500 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 18/06/2025
[2025-06-30 15:36:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:36:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:36:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:36:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:37:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:37:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:37:29] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39953 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 21/03/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 15:37:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:37:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:38:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:38:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:40:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:40:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:41:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:41:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:41:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:41:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:41:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:41:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:42:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:42:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:42:28] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40516 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 15:42:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:42:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:45:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:45:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:45:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:45:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:46:23] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39965 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 21/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 15:46:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:46:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:46:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:46:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:47:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:47:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:47:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:47:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:50:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:50:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:50:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:50:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:51:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:51:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:51:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:51:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:51:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:51:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:52:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:52:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:54:57] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39977 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/03/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 15:55:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:55:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:55:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:55:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:56:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:56:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:56:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:56:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:56:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:56:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:57:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:57:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 15:59:26] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40550 | Funcionário: Cristiano Prazer | Solicitação Laudo: 10/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 15:59:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 15:59:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:00:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:00:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:00:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:00:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:01:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:01:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:01:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:01:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:02:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:02:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:02:39] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40592 | Funcionário: Cristiano Prazer | Solicitação Laudo: 03/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 16:04:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:04:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:05:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:05:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:05:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:05:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:05:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:05:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:06:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:06:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:06:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:06:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:07:32] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40575 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 16:08:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40621 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-30 16:09:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:09:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:10:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:10:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:10:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:10:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:10:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:10:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:11:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:11:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:11:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:11:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:12:23] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40689 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 16:14:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:14:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:14:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:14:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:15:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:15:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:15:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:15:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:15:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:15:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:16:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:16:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:18:43] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40620 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 16:19:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:19:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:19:36] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40608 | Funcionário: Cristiano Prazer | Solicitação Laudo: 03/06/2025 | Técnico: Sidnei Gama | SISBR: 26/03/2025
[2025-06-30 16:19:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:19:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:20:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:20:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:20:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:20:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:20:34] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40643 | Funcionário: Cristiano Prazer | Solicitação Laudo: 10/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 17/06/2025
[2025-06-30 16:20:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:20:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:21:19] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40642 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 29/05/2025
[2025-06-30 16:21:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:21:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:22:00] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40641 | Funcionário: Cristiano Prazer | Solicitação Laudo: 10/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 24/06/2025
[2025-06-30 16:22:56] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40671 | Funcionário: Cristiano Prazer | Solicitação Laudo: 03/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 16:23:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:23:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:24:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:24:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:24:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:24:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:24:59] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40670 | Funcionário: Cristiano Prazer | Solicitação Laudo: 03/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 11/06/2025
[2025-06-30 16:25:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:25:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:25:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:25:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:25:41] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40688 | Funcionário: Cristiano Prazer | Solicitação Laudo: 03/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 12/06/2025
[2025-06-30 16:26:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:26:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:28:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:28:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:29:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:29:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:29:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:29:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:29:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:29:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:30:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:30:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:30:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:30:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:33:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:33:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:34:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:34:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:34:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:34:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:34:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:34:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:35:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:35:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:35:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:35:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:38:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:38:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:38:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:38:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:39:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:39:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:39:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:39:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:40:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:40:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:40:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:40:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:43:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:43:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:43:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:43:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:43:53] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40650 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 16:44:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:44:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:44:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:44:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:44:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:44:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:45:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:45:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:47:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:47:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:48:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:48:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:49:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:49:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:49:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:49:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:49:10] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40676 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 16:49:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:49:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:50:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:50:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:52:15] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39949 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/03/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 16:52:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:52:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:53:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:53:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:53:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:53:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:53:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:53:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:54:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:54:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:55:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:55:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:55:39] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40674 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Sidnei Gama
[2025-06-30 16:55:50] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40674 | Funcionário: Cristiano Prazer | Solicitação Laudo: 27/05/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 16:57:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:57:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:58:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:58:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:58:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:58:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:58:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:58:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:59:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:59:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 16:59:42] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40675 | Funcionário: Cristiano Prazer | Solicitação Laudo: 03/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 16:59:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 16:59:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:02:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:02:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:02:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:02:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:03:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:03:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:03:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:03:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:04:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:04:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:04:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:04:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:05:32] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39966 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 17:07:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:07:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:07:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:07:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:08:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:08:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:08:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:08:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:08:43] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40706 | Funcionário: Cristiano Prazer | Solicitação Laudo: 10/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 17:08:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:08:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:09:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:09:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:12:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:12:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:12:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:12:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:13:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:13:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:13:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:13:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:13:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:13:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:14:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:14:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:14:47] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40707 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 17:16:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:16:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:17:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:17:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:17:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:17:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:18:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:18:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:18:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:18:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:19:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:19:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:21:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:21:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:22:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:22:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:22:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:22:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:22:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:22:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:22:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40801 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 17:23:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:23:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:23:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:23:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:23:58] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40848 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 24/06/2025
[2025-06-30 17:26:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:26:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:26:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:26:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:27:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:27:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:27:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:27:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:27:50] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40832 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 30/06/2025
[2025-06-30 17:28:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:28:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:28:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:28:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:28:50] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40992 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 24/06/2025
[2025-06-30 17:30:03] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40882 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 17:30:22] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40012 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 17:31:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:31:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:31:22] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 41419 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 17:31:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:31:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:32:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:32:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:32:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:32:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:32:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:32:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:33:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:33:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:36:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:36:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:36:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:36:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:37:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:37:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:37:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:37:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:37:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:37:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:38:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:38:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:40:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:40:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:41:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:41:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:41:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:41:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:42:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:42:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:42:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:42:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:42:41] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40944 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 17:43:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:43:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:45:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:45:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:46:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:46:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:46:16] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40013 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 17:46:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:46:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:46:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:46:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:47:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:47:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:47:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:47:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:50:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:50:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:50:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:50:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:50:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:50:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:50:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:50:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:50:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:50:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:50:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:50:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:50:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:50:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:50:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:50:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:51:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:51:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:51:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:51:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:51:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:51:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:51:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:51:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:51:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:51:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:51:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:51:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:51:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:51:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:51:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:51:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:52:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:52:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:52:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:52:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:52:47] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40847 | Funcionário: Cristiano Prazer | Solicitação Laudo: 17/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 17:55:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:55:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:55:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:55:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:56:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:56:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:56:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:56:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:56:53] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 41160 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 17:56:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:56:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:57:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 17:57:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 17:57:35] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39629 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 31/01/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 17:57:35] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39629 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 31/01/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 17:59:29] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39700 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 05/02/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 18:00:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:00:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:00:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:00:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:01:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:01:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:01:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:01:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:01:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:01:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:02:03] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 41130 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 30/06/2025
[2025-06-30 18:02:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:02:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:04:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:04:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:05:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:05:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:05:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:05:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:06:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:06:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:06:14] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40019 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 18:06:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:06:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:07:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:07:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:07:18] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40080 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 18:09:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:09:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:10:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:10:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:10:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:10:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:10:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:10:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:11:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:11:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:11:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:11:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:14:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:14:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:15:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:15:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:15:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:15:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:15:39] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40702 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 30/06/2025
[2025-06-30 18:15:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:15:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:16:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:16:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:16:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:16:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:17:19] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40036 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Daniel Carlos Muniz | SISBR: 30/06/2025
[2025-06-30 18:19:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:19:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:19:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:19:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:20:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:20:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:20:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:20:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:21:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:21:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:21:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:21:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:24:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:24:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:24:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:24:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:25:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:25:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:25:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:25:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:25:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:25:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:26:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:26:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:28:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:28:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:29:09] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40050 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 16/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 30/06/2025
[2025-06-30 18:29:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:29:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:30:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:30:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:30:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:30:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:30:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:30:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:31:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:31:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:33:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:33:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:34:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:34:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:34:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:34:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:34:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:34:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:35:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:35:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:36:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:36:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:38:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:38:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:39:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:39:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:39:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:39:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:39:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:39:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:40:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:40:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:40:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:40:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:43:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:43:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:43:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:43:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:44:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:44:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:44:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:44:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:45:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:45:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:45:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:45:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:48:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:48:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:48:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:48:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:49:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:49:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:49:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:49:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:49:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:49:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:50:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:50:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:53:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:53:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:53:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:53:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:54:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:54:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:54:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:54:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:54:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:54:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:55:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:55:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:57:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:57:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:58:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:58:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:58:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:58:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:59:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:59:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 18:59:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 18:59:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:00:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:00:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:02:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:02:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:03:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:03:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:03:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:03:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:03:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:03:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:04:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:04:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:04:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:04:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:07:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:07:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:07:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:07:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:08:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:08:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:08:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:08:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:09:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:09:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:09:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:09:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:12:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:12:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:12:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:12:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:13:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:13:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:13:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:13:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:13:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:13:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:14:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:14:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:17:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:17:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:17:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:17:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:18:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:18:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:18:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:18:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:18:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:18:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:19:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:19:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:21:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:21:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:22:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:22:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:22:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:22:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:23:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:23:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:23:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:23:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:24:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:24:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:26:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:26:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:27:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:27:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:27:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:27:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:27:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:27:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:28:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:28:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:28:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:28:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:31:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:31:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:32:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:32:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:32:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:32:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:32:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:32:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:33:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:33:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:33:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:33:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:36:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:36:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:36:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:36:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:37:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:37:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:37:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:37:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:38:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:38:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:38:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:38:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:41:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:41:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:41:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:41:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:42:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:42:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:42:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:42:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:42:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:42:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:43:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:43:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:45:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:45:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:46:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:46:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:46:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:46:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:47:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:47:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:47:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:47:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:48:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:48:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:50:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:50:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:51:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:51:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:51:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:51:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:51:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:51:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:52:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:52:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:52:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:52:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:55:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:55:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:56:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:56:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:56:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:56:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:56:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:56:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:57:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:57:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 19:57:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 19:57:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:00:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:00:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:00:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:00:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:01:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:01:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:01:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:01:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:02:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:02:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:02:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:02:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:05:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:05:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:05:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:05:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:06:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:06:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:06:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:06:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:06:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:06:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:07:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:07:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:09:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:09:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:10:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:10:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:10:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:10:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:11:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:11:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:11:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:11:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:12:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:12:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:14:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:14:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:15:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:15:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:15:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:15:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:15:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:15:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:16:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:16:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:16:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:16:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:19:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:19:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:20:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:20:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:20:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:20:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:20:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:20:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:21:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:21:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:21:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:21:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:24:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:24:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:24:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:24:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:25:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:25:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:25:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:25:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:26:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:26:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:26:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:26:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:29:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:29:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:29:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:29:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:30:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:30:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:30:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:30:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:30:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:30:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:31:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:31:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:33:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:33:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:34:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:34:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:35:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:35:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:35:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:35:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:35:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:35:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:36:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:36:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:38:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:38:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:39:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:39:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:39:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:39:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:39:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:39:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:40:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:40:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:41:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:41:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:43:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:43:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:44:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:44:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:44:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:44:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:44:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:44:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:45:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:45:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:45:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:45:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:48:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:48:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:48:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:48:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:49:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:49:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:49:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:49:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:50:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:50:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:50:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:50:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:53:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:53:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:53:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:53:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:54:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:54:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:54:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:54:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:54:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:54:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:55:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:55:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:57:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:57:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:58:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:58:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:59:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:59:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:59:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:59:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 20:59:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 20:59:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:00:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:00:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:02:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:02:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:03:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:03:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:03:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:03:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:03:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:03:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:04:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:04:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:05:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:05:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:07:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:07:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:08:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:08:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:08:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:08:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:08:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:08:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:09:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:09:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:09:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:09:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:12:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:12:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:12:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:12:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:13:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:13:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:13:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:13:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:14:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:14:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:14:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:14:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:17:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:17:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:17:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:17:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:18:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:18:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:18:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:18:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:18:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:18:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:19:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:19:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:22:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:22:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:22:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:22:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:23:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:23:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:23:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:23:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:23:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:23:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:24:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:24:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:26:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:26:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:27:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:27:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:27:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:27:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:28:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:28:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:28:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:28:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:29:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:29:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:31:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:31:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:32:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:32:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:32:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:32:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:32:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:32:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:33:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:33:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:33:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:33:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:36:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:36:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:36:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:36:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:37:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:37:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:37:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:37:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:38:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:38:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:38:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:38:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:41:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:41:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:41:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:41:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:42:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:42:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:42:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:42:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:42:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:42:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:43:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:43:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:46:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:46:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:46:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:46:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:47:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:47:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:47:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:47:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:47:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:47:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:48:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:48:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:50:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:50:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:51:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:51:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:51:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:51:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:52:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:52:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:52:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:52:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:53:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:53:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:55:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:55:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:56:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:56:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:56:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:56:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:56:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:56:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:57:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:57:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 21:57:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 21:57:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:00:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:00:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:01:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:01:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:01:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:01:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:01:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:01:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:02:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:02:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:02:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:02:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:05:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:05:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:05:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:05:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:06:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:06:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:06:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:06:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:06:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:06:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:07:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:07:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:10:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:10:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:10:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:10:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:11:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:11:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:11:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:11:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:11:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:11:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:12:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:12:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:14:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:14:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:15:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:15:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:15:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:15:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:16:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:16:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:16:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:16:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:17:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:17:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:19:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:19:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:20:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:20:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:20:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:20:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:20:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:20:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:21:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:21:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:21:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:21:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:24:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:24:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:25:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:25:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:25:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:25:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:25:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:25:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:26:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:26:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:26:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:26:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:29:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:29:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:29:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:29:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:30:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:30:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:30:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:30:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:31:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:31:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:31:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:31:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:34:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:34:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:34:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:34:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:35:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:35:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:35:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:35:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:35:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:35:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:36:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:36:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:38:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:38:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:39:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:39:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:39:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:39:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:40:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:40:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:40:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:40:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:41:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:41:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:43:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:43:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:44:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:44:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:44:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:44:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:44:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:44:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:45:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:45:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:45:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:45:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:48:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:48:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:49:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:49:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:49:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:49:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:49:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:49:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:50:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:50:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:50:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:50:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:53:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:53:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:53:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:53:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:54:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:54:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:54:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:54:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:55:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:55:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:55:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:55:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:58:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:58:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:58:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:58:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:59:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:59:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:59:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:59:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 22:59:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 22:59:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:00:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:00:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:02:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:02:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:03:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:03:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:03:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:03:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:04:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:04:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:04:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:04:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:05:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:05:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:07:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:07:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:08:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:08:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:08:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:08:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:08:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:08:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:09:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:09:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:09:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:09:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:12:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:12:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:13:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:13:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:13:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:13:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:13:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:13:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:14:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:14:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:14:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:14:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:17:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:17:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:17:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:17:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:18:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:18:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:18:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:18:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:19:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:19:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:19:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:19:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:22:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:22:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:22:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:22:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:23:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:23:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:23:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:23:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:23:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:23:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:24:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:24:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:26:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:26:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:27:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:27:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:27:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:27:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:28:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:28:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:28:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:28:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:29:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:29:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:31:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:31:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:32:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:32:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:32:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:32:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:32:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:32:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:33:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:33:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:33:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:33:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:36:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:36:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:37:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:37:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:37:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:37:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:37:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:37:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:38:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:38:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:38:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:38:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:41:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:41:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:41:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:41:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:42:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:42:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:42:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:42:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:43:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:43:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:43:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:43:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:46:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:46:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:46:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:46:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:47:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:47:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:47:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:47:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:47:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:47:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:48:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:48:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:50:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:50:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:51:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:51:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:52:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:52:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:52:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:52:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:52:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:52:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:53:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:53:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:55:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:55:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:56:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:56:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:56:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:56:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:56:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:56:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:57:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:57:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-30 23:58:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-30 23:58:01] [INFO] Cache de usuários da intranet criado com 290 usuários
