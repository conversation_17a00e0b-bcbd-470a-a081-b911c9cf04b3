<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Filtros Download - MCI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        body { background-color: #f8f9fa; padding: 20px; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        .test-section { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0; }
        .btn-excel { background-color: #28a745; border-color: #28a745; color: white; }
        .btn-excel:hover { background-color: #218838; border-color: #1e7e34; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-primary mb-4">
            <i class="fas fa-filter"></i> 
            Teste de Filtros no Download Excel
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Problema dos Filtros</h5>
            <p class="mb-0">
                O download estava funcionando, mas não respeitava os filtros aplicados na interface. 
                Agora vamos testar se a correção funcionou.
            </p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bug"></i> Problema Anterior
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="test-section">
                            <h6><i class="fas fa-code"></i> JavaScript Incorreto</h6>
                            <p><strong>Problema:</strong></p>
                            <code class="small">const status = '&lt;?php echo $filtro_status; ?&gt;';</code>
                            
                            <p class="mt-2"><strong>Causa:</strong></p>
                            <ul class="small">
                                <li>Capturava valores PHP do carregamento da página</li>
                                <li>Não capturava mudanças feitas pelo usuário</li>
                                <li>Filtros aplicados não eram enviados para o export</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tools"></i> Correção Aplicada
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="test-section">
                            <h6><i class="fas fa-code"></i> JavaScript Corrigido</h6>
                            <p><strong>Solução:</strong></p>
                            <code class="small">const status = document.querySelector('select[name="status"]')?.value || '';</code>
                            
                            <p class="mt-2"><strong>Benefícios:</strong></p>
                            <ul class="small">
                                <li>Captura valores atuais dos campos</li>
                                <li>Respeita mudanças feitas pelo usuário</li>
                                <li>Filtros aplicados são enviados para o export</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-play"></i> Simulador de Filtros
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>Use os filtros abaixo para testar se estão sendo capturados corretamente:</p>
                        
                        <form class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label small">Status</label>
                                <select name="status" class="form-select form-select-sm">
                                    <option value="">Todos</option>
                                    <option value="Pendente">Pendente</option>
                                    <option value="Solicitado">Solicitado</option>
                                    <option value="Atualizado">Atualizado</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label small">PA</label>
                                <select name="pa" class="form-select form-select-sm">
                                    <option value="">Todos</option>
                                    <option value="1">1 - Sede</option>
                                    <option value="2">2 - Manhumirim</option>
                                    <option value="5">5 - Reduto</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label small">Associado</label>
                                <input type="text" name="associado" class="form-control form-control-sm" placeholder="Nome ou CPF...">
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label small">Funcionário</label>
                                <select name="funcionario" class="form-select form-select-sm">
                                    <option value="">Todos</option>
                                    <option value="17">Jussara</option>
                                    <option value="20">Milena</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label small">Técnico</label>
                                <select name="tecnico" class="form-select form-select-sm">
                                    <option value="">Todos</option>
                                    <option value="1">Técnico 1</option>
                                    <option value="2">Técnico 2</option>
                                </select>
                            </div>
                            
                            <div class="col-md-1">
                                <label class="form-label small">&nbsp;</label>
                                <button type="button" class="btn btn-excel btn-sm w-100" onclick="testarFiltros()">
                                    <i class="fas fa-download"></i> Testar
                                </button>
                            </div>
                        </form>
                        
                        <div class="mt-3">
                            <h6>Filtros Capturados:</h6>
                            <div id="filtros-capturados" class="alert alert-light">
                                <em>Clique em "Testar" para ver os filtros capturados</em>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6>URL Gerada:</h6>
                            <div id="url-gerada" class="alert alert-light">
                                <em>A URL do download será exibida aqui</em>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-list-check"></i> Testes Recomendados
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-filter"></i> Teste 1: Status</h6>
                                <ol class="small">
                                    <li>Selecione "Pendente" no filtro Status</li>
                                    <li>Vá para gerenciar.php</li>
                                    <li>Aplique o mesmo filtro</li>
                                    <li>Clique em "Download Excel"</li>
                                    <li>Verifique se baixou apenas registros pendentes</li>
                                </ol>
                            </div>
                            
                            <div class="col-md-4">
                                <h6><i class="fas fa-building"></i> Teste 2: PA</h6>
                                <ol class="small">
                                    <li>Selecione um PA específico</li>
                                    <li>Vá para gerenciar.php</li>
                                    <li>Aplique o mesmo filtro</li>
                                    <li>Clique em "Download Excel"</li>
                                    <li>Verifique se baixou apenas registros do PA</li>
                                </ol>
                            </div>
                            
                            <div class="col-md-4">
                                <h6><i class="fas fa-user"></i> Teste 3: Funcionário</h6>
                                <ol class="small">
                                    <li>Selecione um funcionário específico</li>
                                    <li>Vá para gerenciar.php</li>
                                    <li>Aplique o mesmo filtro</li>
                                    <li>Clique em "Download Excel"</li>
                                    <li>Verifique se baixou apenas registros do funcionário</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-link"></i> Links para Teste
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="gerenciar.php" class="btn btn-primary w-100 mb-2" target="_blank">
                                    <i class="fas fa-list"></i> Gerenciar (Corrigido)
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="export_excel_real.php?download=excel&status=Pendente" class="btn btn-warning w-100 mb-2" target="_blank">
                                    <i class="fas fa-download"></i> Apenas Pendentes
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="export_excel_real.php?download=excel&pa=2" class="btn btn-info w-100 mb-2" target="_blank">
                                    <i class="fas fa-download"></i> Apenas PA 2
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="export_excel_real.php?download=excel&funcionario=20" class="btn btn-success w-100 mb-2" target="_blank">
                                    <i class="fas fa-download"></i> Funcionário 20
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h6><i class="fas fa-check-circle"></i> Correção Aplicada!</h6>
            <p class="mb-0">
                O JavaScript foi corrigido para capturar os valores atuais dos campos de filtro. 
                Agora o download deve respeitar todos os filtros aplicados na interface.
                <strong>Teste usando a página gerenciar.php!</strong>
            </p>
        </div>
    </div>

    <script>
        function testarFiltros() {
            // Capturar filtros da mesma forma que o gerenciar.php
            const status = document.querySelector('select[name="status"]')?.value || '';
            const pa = document.querySelector('select[name="pa"]')?.value || '';
            const associado = document.querySelector('input[name="associado"]')?.value || '';
            const funcionario = document.querySelector('select[name="funcionario"]')?.value || '';
            const tecnico = document.querySelector('select[name="tecnico"]')?.value || '';
            const mesRenda = document.querySelector('select[name="mes_renda"]')?.value || '';
            
            // Mostrar filtros capturados
            const filtrosDiv = document.getElementById('filtros-capturados');
            filtrosDiv.innerHTML = `
                <strong>Filtros Capturados:</strong><br>
                • Status: "${status}"<br>
                • PA: "${pa}"<br>
                • Associado: "${associado}"<br>
                • Funcionário: "${funcionario}"<br>
                • Técnico: "${tecnico}"<br>
                • Mês Renda: "${mesRenda}"
            `;
            
            // Construir URL
            const params = new URLSearchParams();
            if (status) params.append('status', status);
            if (pa) params.append('pa', pa);
            if (associado) params.append('associado', associado);
            if (funcionario) params.append('funcionario', funcionario);
            if (tecnico) params.append('tecnico', tecnico);
            if (mesRenda) params.append('mes_renda', mesRenda);
            params.append('download', 'excel');
            
            const finalUrl = 'export_excel_real.php?' + params.toString();
            
            // Mostrar URL
            const urlDiv = document.getElementById('url-gerada');
            urlDiv.innerHTML = `
                <strong>URL Gerada:</strong><br>
                <code>${finalUrl}</code><br>
                <a href="${finalUrl}" class="btn btn-sm btn-success mt-2" target="_blank">
                    <i class="fas fa-download"></i> Testar Download
                </a>
            `;
        }
    </script>
</body>
</html>
