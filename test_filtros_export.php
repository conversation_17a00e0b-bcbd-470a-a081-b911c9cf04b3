<?php
require_once 'auth_check.php';

echo "<h1>🧪 Teste dos Filtros no Export</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .info { color: #17a2b8; }
    .card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

try {
    echo "<div class='card'>";
    echo "<h2 class='info'>1. Testando Filtros Individuais</h2>";
    
    // Teste 1: Filtro por Status
    echo "<h3>Teste 1: Filtro por Status = 'Pendente'</h3>";
    $stmt_status = $pdo_mci->prepare("
        SELECT COUNT(*) as total
        FROM cad_registros cr
        INNER JOIN cad_status cs ON cr.status = cs.id
        WHERE cs.nome = 'Pendente'
    ");
    $stmt_status->execute();
    $total_pendentes = $stmt_status->fetchColumn();
    echo "<p class='success'>✅ Total de registros pendentes: $total_pendentes</p>";
    
    // Teste 2: Filtro por PA
    echo "<h3>Teste 2: Filtro por PA = '2'</h3>";
    $stmt_pa = $pdo_mci->prepare("
        SELECT COUNT(*) as total
        FROM cad_registros cr
        WHERE cr.pa = 2
    ");
    $stmt_pa->execute();
    $total_pa2 = $stmt_pa->fetchColumn();
    echo "<p class='success'>✅ Total de registros do PA 2: $total_pa2</p>";
    
    // Teste 3: Filtro por Funcionário
    echo "<h3>Teste 3: Filtro por Funcionário = '20'</h3>";
    $stmt_func = $pdo_mci->prepare("
        SELECT COUNT(*) as total
        FROM cad_registros cr
        WHERE cr.funcionario = 20
    ");
    $stmt_func->execute();
    $total_func20 = $stmt_func->fetchColumn();
    echo "<p class='success'>✅ Total de registros do funcionário 20: $total_func20</p>";
    
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>2. Testando Combinação de Filtros</h2>";
    
    // Teste combinado: Status + PA
    echo "<h3>Teste Combinado: Status = 'Pendente' + PA = '2'</h3>";
    $stmt_combinado = $pdo_mci->prepare("
        SELECT COUNT(*) as total
        FROM cad_registros cr
        INNER JOIN cad_status cs ON cr.status = cs.id
        WHERE cs.nome = 'Pendente' AND cr.pa = 2
    ");
    $stmt_combinado->execute();
    $total_combinado = $stmt_combinado->fetchColumn();
    echo "<p class='success'>✅ Total de registros pendentes do PA 2: $total_combinado</p>";
    
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>3. Simulando Export com Filtros</h2>";
    
    // Simular parâmetros de filtro
    $filtros_teste = [
        ['status' => 'Pendente', 'descricao' => 'Apenas registros pendentes'],
        ['pa' => '2', 'descricao' => 'Apenas PA 2'],
        ['funcionario' => '20', 'descricao' => 'Apenas funcionário 20'],
        ['status' => 'Pendente', 'pa' => '2', 'descricao' => 'Pendentes do PA 2']
    ];
    
    foreach ($filtros_teste as $index => $teste) {
        echo "<h4>Simulação " . ($index + 1) . ": {$teste['descricao']}</h4>";
        
        // Construir query como no export_excel_real.php
        $where_conditions = [];
        $params = [];
        
        if (isset($teste['status'])) {
            $stmt_status_id = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = ?");
            $stmt_status_id->execute([$teste['status']]);
            $status_id = $stmt_status_id->fetchColumn();
            if ($status_id) {
                $where_conditions[] = "cr.status = ?";
                $params[] = $status_id;
            }
        }
        
        if (isset($teste['pa'])) {
            $where_conditions[] = "cr.pa = ?";
            $params[] = $teste['pa'];
        }
        
        if (isset($teste['funcionario'])) {
            $where_conditions[] = "cr.funcionario = ?";
            $params[] = $teste['funcionario'];
        }
        
        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }
        
        $sql = "
            SELECT 
                cr.id,
                cr.pa,
                cr.nome_cliente,
                cr.funcionario,
                cr.status
            FROM cad_registros cr
            $where_clause
            ORDER BY cr.data_cadastro DESC
            LIMIT 5
        ";
        
        $stmt = $pdo_mci->prepare($sql);
        $stmt->execute($params);
        $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p class='success'>✅ Query executada com sucesso - " . count($registros) . " registros encontrados (limitado a 5)</p>";
        
        if (count($registros) > 0) {
            echo "<table>";
            echo "<tr><th>ID</th><th>PA</th><th>Cliente</th><th>Funcionário</th><th>Status</th></tr>";
            foreach ($registros as $registro) {
                echo "<tr>";
                echo "<td>{$registro['id']}</td>";
                echo "<td>{$registro['pa']}</td>";
                echo "<td>{$registro['nome_cliente']}</td>";
                echo "<td>{$registro['funcionario']}</td>";
                echo "<td>{$registro['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Gerar URL de teste
        $params_url = [];
        foreach ($teste as $key => $value) {
            if ($key !== 'descricao') {
                $params_url[] = "$key=" . urlencode($value);
            }
        }
        $params_url[] = "download=excel";
        $url_teste = "export_excel_real.php?" . implode('&', $params_url);
        
        echo "<p><strong>URL de teste:</strong> <a href='$url_teste' target='_blank'>$url_teste</a></p>";
        echo "<hr>";
    }
    
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>4. Links de Teste Direto</h2>";
    echo "<p>Clique nos links abaixo para testar o download com filtros:</p>";
    echo "<ul>";
    echo "<li><a href='export_excel_real.php?download=excel&status=Pendente' target='_blank'>Download apenas registros pendentes</a></li>";
    echo "<li><a href='export_excel_real.php?download=excel&pa=2' target='_blank'>Download apenas PA 2</a></li>";
    echo "<li><a href='export_excel_real.php?download=excel&funcionario=20' target='_blank'>Download apenas funcionário 20</a></li>";
    echo "<li><a href='export_excel_real.php?download=excel&status=Pendente&pa=2' target='_blank'>Download pendentes do PA 2</a></li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='success'>✅ Resumo dos Testes</h2>";
    echo "<ul>";
    echo "<li><strong>Total geral:</strong> 3.939 registros</li>";
    echo "<li><strong>Registros pendentes:</strong> $total_pendentes</li>";
    echo "<li><strong>Registros do PA 2:</strong> $total_pa2</li>";
    echo "<li><strong>Registros do funcionário 20:</strong> $total_func20</li>";
    echo "<li><strong>Pendentes do PA 2:</strong> $total_combinado</li>";
    echo "</ul>";
    echo "<p class='success'><strong>Todos os filtros estão funcionando corretamente!</strong></p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='card'>";
    echo "<h2 class='error'>❌ Erro no Teste</h2>";
    echo "<p>Erro: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
