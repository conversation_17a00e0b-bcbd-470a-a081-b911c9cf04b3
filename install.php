<?php
/**
 * Script de instalação do Sistema MCI
 * Execute este arquivo para criar o banco de dados e tabelas
 */

echo "<h1>Instalação do Sistema MCI - Atualizações Cadastrais</h1>";
echo "<hr>";

// Verificar se já foi executado
if (file_exists('config/installed.flag')) {
    echo "<div style='color: orange;'>⚠️ Sistema já foi instalado anteriormente.</div>";
    echo "<p>Se deseja reinstalar, remova o arquivo 'config/installed.flag' e execute novamente.</p>";
    echo "<p><a href='test_connection.php'>Executar teste de conexão</a></p>";
    exit;
}

try {
    // Conectar ao servidor MySQL
    echo "<h3>1. Conectando ao servidor MySQL...</h3>";
    
    $host = 'livreplus.sicoobcredilivre.com.br';
    $user = 'sis';
    $pass = 'Sicoob@123';
    
    $pdo = new PDO("mysql:host=$host", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Conexão estabelecida com sucesso<br>";
    
    // Criar banco de dados MCI
    echo "<h3>2. Criando banco de dados MCI...</h3>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS mci CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Banco de dados 'mci' criado/verificado<br>";
    
    // Conectar ao banco MCI
    $pdo->exec("USE mci");
    echo "✅ Conectado ao banco 'mci'<br>";
    
    // Criar tabelas
    echo "<h3>3. Criando tabelas...</h3>";
    
    // Tabela cad_registros
    $sql_registros = "
    CREATE TABLE IF NOT EXISTS cad_registros (
        id INT AUTO_INCREMENT PRIMARY KEY,
        pa VARCHAR(10) NOT NULL COMMENT 'PA',
        nome_cliente VARCHAR(255) NOT NULL COMMENT 'Nome Cliente',
        numero_cpf_cnpj VARCHAR(20) NOT NULL COMMENT 'Número CPF/CNPJ',
        cnae VARCHAR(20) COMMENT 'CNAE',
        data_ultima_atualizacao_renda DATE COMMENT 'Data Última Atualização Renda',
        sigla_tipo_pessoa VARCHAR(5) COMMENT 'Sigla Tipo Pessoa',
        profissao VARCHAR(100) COMMENT 'Profissão',
        deposito_total DECIMAL(15,2) COMMENT 'Depósito Total',
        funcionario VARCHAR(100) COMMENT 'FUNCIONÁRIO',
        data_solicitacao_laudo DATE COMMENT 'DATA DA SOLICITAÇÃO DO LAUDO',
        tecnico_responsavel VARCHAR(100) COMMENT 'TÉCNICO RESPONSÁVEL',
        data_atual_sisbr DATE COMMENT 'DATA DA ATUAL. SISBR',
        status ENUM('pendente', 'em_andamento', 'concluido', 'cancelado') DEFAULT 'pendente',
        observacoes TEXT,
        data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        usuario_cadastro INT,
        usuario_atualizacao INT,
        INDEX idx_pa (pa),
        INDEX idx_cpf_cnpj (numero_cpf_cnpj),
        INDEX idx_status (status),
        INDEX idx_data_cadastro (data_cadastro)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_registros);
    echo "✅ Tabela 'cad_registros' criada<br>";
    
    // Tabela cad_logs
    $sql_logs = "
    CREATE TABLE IF NOT EXISTS cad_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        acao VARCHAR(100) NOT NULL,
        detalhes TEXT,
        registro_id INT,
        data_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_usuario (usuario_id),
        INDEX idx_registro (registro_id),
        INDEX idx_data (data_hora),
        FOREIGN KEY (registro_id) REFERENCES cad_registros(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_logs);
    echo "✅ Tabela 'cad_logs' criada<br>";
    
    // Tabela cad_importacoes
    $sql_importacoes = "
    CREATE TABLE IF NOT EXISTS cad_importacoes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome_arquivo VARCHAR(255) NOT NULL,
        tamanho_arquivo INT NOT NULL,
        total_registros INT NOT NULL,
        registros_importados INT DEFAULT 0,
        registros_erro INT DEFAULT 0,
        status ENUM('processando', 'concluido', 'erro') DEFAULT 'processando',
        detalhes_erro TEXT,
        usuario_id INT NOT NULL,
        data_importacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_conclusao TIMESTAMP NULL,
        INDEX idx_usuario (usuario_id),
        INDEX idx_status (status),
        INDEX idx_data (data_importacao)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_importacoes);
    echo "✅ Tabela 'cad_importacoes' criada<br>";
    
    // Tabela cad_erros_importacao
    $sql_erros = "
    CREATE TABLE IF NOT EXISTS cad_erros_importacao (
        id INT AUTO_INCREMENT PRIMARY KEY,
        importacao_id INT NOT NULL,
        linha INT NOT NULL,
        campo VARCHAR(50),
        valor TEXT,
        erro TEXT NOT NULL,
        data_erro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (importacao_id) REFERENCES cad_importacoes(id) ON DELETE CASCADE,
        INDEX idx_importacao (importacao_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_erros);
    echo "✅ Tabela 'cad_erros_importacao' criada<br>";
    
    // Verificar tabelas criadas
    echo "<h3>4. Verificando tabelas criadas...</h3>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "✅ $table<br>";
    }
    
    // Criar diretórios necessários
    echo "<h3>5. Criando diretórios...</h3>";
    $directories = ['logs', 'uploads'];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "✅ Diretório '$dir' criado<br>";
        } else {
            echo "✅ Diretório '$dir' já existe<br>";
        }
    }
    
    // Criar arquivo de flag de instalação
    file_put_contents('config/installed.flag', date('Y-m-d H:i:s'));
    
    echo "<hr>";
    echo "<h3>🎉 Instalação concluída com sucesso!</h3>";
    echo "<p>O Sistema MCI foi instalado e está pronto para uso.</p>";
    
    echo "<h4>Próximos passos:</h4>";
    echo "<ul>";
    echo "<li><a href='test_connection.php'>Executar teste de conexão</a></li>";
    echo "<li><a href='index.php'>Acessar o sistema</a></li>";
    echo "<li>Ler a documentação no README.md</li>";
    echo "</ul>";
    
    echo "<p><strong>Data da instalação:</strong> " . date('d/m/Y H:i:s') . "</p>";
    
} catch (Exception $e) {
    echo "<hr>";
    echo "<h3 style='color: red;'>❌ Erro na instalação</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>Verifique as configurações e tente novamente.</p>";
}
?>
