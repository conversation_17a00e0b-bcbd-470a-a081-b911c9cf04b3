<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Adicionar <PERSON><PERSON> Saldo Devedor</h1>";
echo "<hr>";

try {
    // Verificar se a coluna já existe
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros LIKE 'saldo_devedor'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: orange;'>⚠️ A coluna 'saldo_devedor' já existe na tabela.</p>";
    } else {
        echo "<h3>Adicionando coluna 'saldo_devedor'...</h3>";
        
        // Adicionar a coluna
        $sql = "ALTER TABLE cad_registros 
                ADD COLUMN saldo_devedor DECIMAL(15,2) COMMENT 'Saldo Devedor' 
                AFTER deposito_total";
        
        $pdo_mci->exec($sql);
        
        echo "<p style='color: green;'>✅ Coluna 'saldo_devedor' adicionada com sucesso!</p>";
    }
    
    // Mostrar estrutura atualizada da tabela
    echo "<h3>Estrutura Atualizada da Tabela:</h3>";
    $stmt = $pdo_mci->prepare("DESCRIBE cad_registros");
    $stmt->execute();
    $colunas = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
    
    foreach ($colunas as $coluna) {
        $highlight = ($coluna['Field'] == 'saldo_devedor') ? 'style="background-color: #d4edda;"' : '';
        echo "<tr $highlight>";
        echo "<td>{$coluna['Field']}</td>";
        echo "<td>{$coluna['Type']}</td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>{$coluna['Key']}</td>";
        echo "<td>" . ($coluna['Default'] ?? 'NULL') . "</td>";
        echo "<td>{$coluna['Extra']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<hr>";
    echo "<h3>Próximos passos:</h3>";
    echo "<ul>";
    echo "<li>A estrutura do banco foi atualizada</li>";
    echo "<li>Agora você pode fazer novas importações com a coluna Saldo Devedor</li>";
    echo "<li>Os arquivos de importação serão atualizados automaticamente</li>";
    echo "</ul>";
    
    echo "<p><a href='importar.php'>Testar nova importação</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro ao adicionar coluna</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
