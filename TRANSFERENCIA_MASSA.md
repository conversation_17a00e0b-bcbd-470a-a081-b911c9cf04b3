# Transferência em Massa de Registros - MCI

## 📋 Visão Geral

A funcionalidade de **Transferência em Massa** permite transferir registros entre funcionários de forma rápida, organizada e segura. Esta ferramenta é especialmente útil para redistribuir cargas de trabalho e reorganizar responsabilidades.

## 🔐 Permissões

- **Acesso**: Apenas usuários com nível **Gestor** ou **Administrador**
- **Localização**: Menu de navegação em `gerenciar.php` → "Transferência em Massa"
- **URL Direta**: `/mci/transferencia_massa.php`

## 🚀 Como Usar

### 1. Seleção do Funcionário de Origem
- Escolha o funcionário que possui os registros a serem transferidos
- O sistema exibirá automaticamente:
  - Total de registros do funcionário
  - Registros disponíveis (excluindo removidos)
  - Informações detalhadas

### 2. Configuração das Transferências
- Clique em **"Adicionar Transferência"** para criar uma nova transferência
- Para cada transferência, configure:
  - **Funcionário de Destino**: Para quem os registros serão transferidos
  - **Quantidade**: Número de registros a transferir
- Use o botão **"Auto"** para sugerir quantidades automaticamente

### 3. Validações Automáticas
O sistema valida automaticamente:
- ✅ Quantidade não excede registros disponíveis
- ✅ Funcionário de destino diferente do origem
- ✅ Não há funcionários duplicados nas transferências
- ✅ Todas as transferências têm dados válidos

### 4. Resumo e Execução
- Visualize o resumo completo da operação
- Confirme os detalhes antes de executar
- Acompanhe o progresso com indicador visual

## 📊 Exemplo Prático

**Cenário**: João tem 500 registros e precisa redistribuir:

1. **Selecionar**: João como funcionário de origem
2. **Configurar transferências**:
   - 200 registros → Maria
   - 100 registros → Pedro
   - 50 registros → Ana
3. **Resultado**: 
   - João ficará com 150 registros
   - Total transferido: 350 registros

## 🔧 Funcionalidades Avançadas

### Sugestão Automática de Quantidades
- Clique no botão **"Auto"** em qualquer transferência
- O sistema calcula automaticamente uma distribuição equilibrada
- Considera registros já alocados em outras transferências

### Validação em Tempo Real
- **Status Verde (✓)**: Configuração válida, pronto para executar
- **Status Amarelo (⚠)**: Problemas detectados, revisar configuração
- Contador dinâmico de registros restantes

### Confirmação Detalhada
Antes da execução, o sistema exibe:
- Resumo completo da operação
- Detalhes de cada transferência
- Registros que permanecerão com o funcionário origem

## 🛡️ Segurança e Auditoria

### Transações Seguras
- Todas as transferências usam transações de banco
- Em caso de erro, nenhuma alteração é aplicada
- Rollback automático em falhas

### Log Completo
Cada transferência é registrada em:
- **Arquivo de Log**: `logs/mci_YYYY-MM-DD.log`
- **Banco Principal**: `sicoob_access_control.logs`
- **Banco MCI**: `mci.cad_logs`

### Rastreabilidade
- Usuário que executou a transferência
- Data e hora da operação
- Detalhes completos da transferência
- Registros específicos transferidos

## 📋 Critérios de Transferência

### Registros Elegíveis
- ✅ Status diferente de "Removido"
- ✅ Vinculados ao funcionário de origem
- ✅ Ordenados por data de cadastro (mais antigos primeiro)

### Registros Não Elegíveis
- ❌ Status "Removido"
- ❌ Sem funcionário vinculado
- ❌ Já em processo de transferência

## 🔍 Monitoramento

### Indicadores Visuais
- **Total a Transferir**: Soma de todas as transferências
- **Nº de Transferências**: Quantidade de operações configuradas
- **Permanecerão com Origem**: Registros que não serão transferidos
- **Status**: Validação geral da configuração

### Feedback do Sistema
- Mensagens de sucesso com detalhes
- Alertas de erro com explicações claras
- Loading screen durante processamento

## 🚨 Limitações e Cuidados

### Limitações Técnicas
- Máximo de registros por transferência: Limitado aos disponíveis
- Não é possível transferir para o mesmo funcionário múltiplas vezes
- Funcionário de destino deve ser diferente do origem

### Boas Práticas
1. **Planeje antes**: Defina a distribuição desejada
2. **Verifique dados**: Confirme funcionários e quantidades
3. **Teste pequeno**: Comece com transferências menores
4. **Monitore logs**: Acompanhe as operações nos logs

### Recuperação
- Em caso de erro, consulte os logs para detalhes
- Transferências podem ser revertidas manualmente se necessário
- Contate o administrador em caso de problemas

## 📞 Suporte

### Arquivos de Teste
- `test_transferencia.php`: Diagnóstico completo do sistema
- Verifica conexões, dados e estruturas

### Logs de Debug
- Localização: `logs/mci_YYYY-MM-DD.log`
- Nível de detalhe: Completo para transferências
- Formato: Timestamp, usuário, ação, detalhes

### Contato
- **Administrador do Sistema**: Consulte permissões
- **Suporte Técnico**: Logs disponíveis para análise
- **Documentação**: Este arquivo e README.md principal

---

## 🎯 Resumo Rápido

1. **Acesso**: Menu → Transferência em Massa
2. **Selecione**: Funcionário de origem
3. **Configure**: Transferências desejadas
4. **Valide**: Verifique resumo
5. **Execute**: Confirme e processe
6. **Monitore**: Acompanhe logs

**Pronto!** Seus registros foram transferidos com segurança e rastreabilidade completa.
