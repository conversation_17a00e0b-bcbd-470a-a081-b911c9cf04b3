<?php
require_once '../auth_check.php';

echo "<h2>🔍 Debug - Renderização do Dashboard</h2>";

// Executar a mesma consulta do dashboard
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

echo "<h3>Dados Retornados pela Consulta</h3>";
echo "<p><strong>Total de registros:</strong> " . count($tecnicos_dados) . "</p>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Array Index</th><th>ID</th><th>Nome</th><th>Email</th><th>Meta</th><th>Atualizados</th><th>Progresso</th></tr>";

foreach ($tecnicos_dados as $index => $tecnico) {
    echo "<tr>";
    echo "<td><strong>$index</strong></td>";
    echo "<td>" . $tecnico['id'] . "</td>";
    echo "<td>" . htmlspecialchars($tecnico['nome']) . "</td>";
    echo "<td>" . htmlspecialchars($tecnico['email']) . "</td>";
    echo "<td>" . $tecnico['meta_total'] . "</td>";
    echo "<td>" . $tecnico['atualizados_total'] . "</td>";
    echo "<td>" . $tecnico['progresso_total'] . "%</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>Simulação do Loop de Renderização</h3>";
echo "<p>Simulando exatamente como o dashboard renderiza os cards:</p>";

echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
$tecnico_index = 1;
foreach ($tecnicos_dados as $tecnico) {
    echo "<div style='border: 1px solid #ddd; margin: 5px; padding: 10px; background: #f9f9f9;'>";
    echo "<strong>Card " . $tecnico_index . ":</strong><br>";
    echo "ID: " . $tecnico['id'] . "<br>";
    echo "Nome: " . htmlspecialchars($tecnico['nome']) . "<br>";
    echo "Email: " . htmlspecialchars($tecnico['email']) . "<br>";
    echo "Meta: " . $tecnico['atualizados_total'] . "/" . $tecnico['meta_total'] . "<br>";
    echo "Progresso: " . $tecnico['progresso_total'] . "%<br>";
    echo "Classe CSS: tecnico-" . $tecnico_index . "<br>";
    echo "</div>";
    $tecnico_index++;
}
echo "</div>";

echo "<h3>Verificação de Duplicações no Array</h3>";

// Verificar se há duplicações no array retornado
$ids_encontrados = [];
$nomes_encontrados = [];
$duplicacoes_encontradas = false;

foreach ($tecnicos_dados as $index => $tecnico) {
    $id = $tecnico['id'];
    $nome = $tecnico['nome'];
    
    if (in_array($id, $ids_encontrados)) {
        echo "<p style='color: red;'>❌ <strong>Duplicação de ID encontrada:</strong> ID $id aparece novamente no índice $index</p>";
        $duplicacoes_encontradas = true;
    } else {
        $ids_encontrados[] = $id;
    }
    
    if (in_array($nome, $nomes_encontrados)) {
        echo "<p style='color: red;'>❌ <strong>Duplicação de Nome encontrada:</strong> '$nome' aparece novamente no índice $index</p>";
        $duplicacoes_encontradas = true;
    } else {
        $nomes_encontrados[] = $nome;
    }
}

if (!$duplicacoes_encontradas) {
    echo "<p style='color: green;'>✅ <strong>Nenhuma duplicação encontrada no array de dados.</strong></p>";
}

echo "<h3>Análise dos Dados</h3>";
echo "<p><strong>IDs únicos encontrados:</strong> " . implode(', ', $ids_encontrados) . "</p>";
echo "<p><strong>Nomes únicos encontrados:</strong></p>";
echo "<ul>";
foreach ($nomes_encontrados as $nome) {
    echo "<li>" . htmlspecialchars($nome) . "</li>";
}
echo "</ul>";

// Verificar se Felipe está no resultado
$felipe_encontrado = false;
$sidnei_count = 0;

foreach ($tecnicos_dados as $tecnico) {
    if (stripos($tecnico['nome'], 'Felipe') !== false) {
        $felipe_encontrado = true;
        echo "<p style='color: green;'>✅ <strong>Felipe encontrado:</strong> " . htmlspecialchars($tecnico['nome']) . "</p>";
    }
    
    if (stripos($tecnico['nome'], 'Sidnei') !== false) {
        $sidnei_count++;
        echo "<p style='color: blue;'>ℹ️ <strong>Sidnei encontrado (ocorrência $sidnei_count):</strong> " . htmlspecialchars($tecnico['nome']) . "</p>";
    }
}

if (!$felipe_encontrado) {
    echo "<p style='color: red;'>❌ <strong>Felipe NÃO encontrado no resultado da consulta.</strong></p>";
}

if ($sidnei_count > 1) {
    echo "<p style='color: red;'>❌ <strong>Sidnei aparece $sidnei_count vezes no resultado!</strong></p>";
} elseif ($sidnei_count == 1) {
    echo "<p style='color: green;'>✅ <strong>Sidnei aparece apenas 1 vez no resultado.</strong></p>";
}

echo "<h3>Informações de Debug</h3>";
echo "<p><strong>Timestamp:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Total de técnicos esperados:</strong> 5 (conforme debug_duplicacao.php)</p>";
echo "<p><strong>Total de técnicos retornados:</strong> " . count($tecnicos_dados) . "</p>";

if (count($tecnicos_dados) != 5) {
    echo "<p style='color: red;'>⚠️ <strong>ATENÇÃO:</strong> Número de técnicos diferente do esperado!</p>";
}

echo "<hr>";
echo "<p><a href='dashboard.php'>🚀 Dashboard</a> | <a href='debug_duplicacao.php'>🔧 Debug Duplicação</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
