<?php
// Limpar cache e forçar nova consulta
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

require_once '../auth_check.php';

echo "<h2>🧹 Limpeza de Cache - Dashboard Técnicos</h2>";

// Executar a consulta e mostrar resultado em tempo real
echo "<h3>Consulta Executada em: " . date('Y-m-d H:i:s') . "</h3>";

$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");

$start_time = microtime(true);
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();
$end_time = microtime(true);

echo "<p><strong>Tempo de execução:</strong> " . round(($end_time - $start_time) * 1000, 2) . " ms</p>";
echo "<p><strong>Total de registros:</strong> " . count($tecnicos_dados) . "</p>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Posição</th><th>ID</th><th>Nome</th><th>Meta</th><th>Atualizados</th><th>Progresso</th></tr>";

foreach ($tecnicos_dados as $index => $tecnico) {
    $posicao = $index + 1;
    echo "<tr>";
    echo "<td><strong>$posicao</strong></td>";
    echo "<td>" . $tecnico['id'] . "</td>";
    echo "<td>" . htmlspecialchars($tecnico['nome']) . "</td>";
    echo "<td>" . $tecnico['meta_total'] . "</td>";
    echo "<td>" . $tecnico['atualizados_total'] . "</td>";
    echo "<td>" . $tecnico['progresso_total'] . "%</td>";
    echo "</tr>";
}
echo "</table>";

// Verificar especificamente Felipe e Sidnei
$felipe_encontrado = false;
$sidnei_encontrado = false;

foreach ($tecnicos_dados as $tecnico) {
    if (stripos($tecnico['nome'], 'Felipe') !== false) {
        $felipe_encontrado = true;
        echo "<p style='color: green;'>✅ <strong>Felipe encontrado:</strong> " . htmlspecialchars($tecnico['nome']) . "</p>";
    }
    
    if (stripos($tecnico['nome'], 'Sidnei') !== false) {
        $sidnei_encontrado = true;
        echo "<p style='color: green;'>✅ <strong>Sidnei encontrado:</strong> " . htmlspecialchars($tecnico['nome']) . "</p>";
    }
}

if (!$felipe_encontrado) {
    echo "<p style='color: red;'>❌ <strong>Felipe NÃO encontrado</strong></p>";
}

if (!$sidnei_encontrado) {
    echo "<p style='color: red;'>❌ <strong>Sidnei NÃO encontrado</strong></p>";
}

echo "<hr>";
echo "<p><strong>Ações:</strong></p>";
echo "<p><a href='dashboard.php?nocache=" . time() . "' target='_blank'>🚀 Abrir Dashboard (sem cache)</a></p>";
echo "<p><a href='clear_cache.php'>🔄 Executar novamente</a></p>";
echo "<p><a href='debug_renderizacao.php'>🔧 Debug Renderização</a></p>";
echo "<p><a href='../'>⬅️ Voltar</a></p>";
?>
