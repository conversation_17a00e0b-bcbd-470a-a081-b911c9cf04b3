<?php
require_once '../auth_check.php';

echo "<h2>🔍 Debug - Duplicação de Técnicos</h2>";

// 1. Consulta original (sem LIMIT)
echo "<h3>1. Consulta Original (sem LIMIT)</h3>";
try {
    $stmt = $pdo_mci->prepare("
        SELECT
            u.id,
            u.nome_completo as nome,
            u.email,
            COUNT(r.id) as meta_total,
            COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
            ROUND(
                (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
            ) as progresso_total
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
        GROUP BY u.id, u.nome_completo, u.email
        HAVING meta_total > 0
        ORDER BY meta_total DESC, u.nome_completo
    ");
    $stmt->execute();
    $todos_tecnicos = $stmt->fetchAll();
    
    echo "<p><strong>Total de técnicos encontrados:</strong> " . count($todos_tecnicos) . "</p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Posição</th><th>ID</th><th>Nome</th><th>Email</th><th>Meta Total</th><th>Atualizados</th><th>Progresso</th></tr>";
    
    foreach ($todos_tecnicos as $index => $tec) {
        $posicao = $index + 1;
        $cor = $posicao <= 6 ? 'background-color: #d4edda;' : '';
        
        echo "<tr style='$cor'>";
        echo "<td><strong>$posicao</strong></td>";
        echo "<td>" . $tec['id'] . "</td>";
        echo "<td>" . htmlspecialchars($tec['nome']) . "</td>";
        echo "<td>" . htmlspecialchars($tec['email']) . "</td>";
        echo "<td>" . $tec['meta_total'] . "</td>";
        echo "<td>" . $tec['atualizados_total'] . "</td>";
        echo "<td>" . $tec['progresso_total'] . "%</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><em>Os 6 primeiros (destacados em verde) serão exibidos no dashboard.</em></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 2. Consulta com LIMIT 6
echo "<h3>2. Consulta com LIMIT 6 (usada no dashboard)</h3>";
try {
    $stmt = $pdo_mci->prepare("
        SELECT
            u.id,
            u.nome_completo as nome,
            u.email,
            COUNT(r.id) as meta_total,
            COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
            ROUND(
                (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
            ) as progresso_total
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
        GROUP BY u.id, u.nome_completo, u.email
        HAVING meta_total > 0
        ORDER BY meta_total DESC, u.nome_completo
        LIMIT 6
    ");
    $stmt->execute();
    $tecnicos_dashboard = $stmt->fetchAll();
    
    echo "<p><strong>Técnicos que aparecerão no dashboard:</strong> " . count($tecnicos_dashboard) . "</p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Posição</th><th>ID</th><th>Nome</th><th>Email</th><th>Meta Total</th><th>Atualizados</th><th>Progresso</th></tr>";
    
    foreach ($tecnicos_dashboard as $index => $tec) {
        $posicao = $index + 1;
        
        echo "<tr>";
        echo "<td><strong>$posicao</strong></td>";
        echo "<td>" . $tec['id'] . "</td>";
        echo "<td>" . htmlspecialchars($tec['nome']) . "</td>";
        echo "<td>" . htmlspecialchars($tec['email']) . "</td>";
        echo "<td>" . $tec['meta_total'] . "</td>";
        echo "<td>" . $tec['atualizados_total'] . "</td>";
        echo "<td>" . $tec['progresso_total'] . "%</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 3. Verificar duplicações por nome
echo "<h3>3. Verificação de Duplicações</h3>";
try {
    $nomes = array_column($todos_tecnicos, 'nome');
    $nomes_unicos = array_unique($nomes);
    $duplicacoes = array_diff_assoc($nomes, $nomes_unicos);
    
    if (empty($duplicacoes)) {
        echo "<p>✅ <strong>Nenhuma duplicação encontrada por nome.</strong></p>";
    } else {
        echo "<p>❌ <strong>Duplicações encontradas:</strong></p>";
        echo "<ul>";
        foreach ($duplicacoes as $nome) {
            echo "<li>" . htmlspecialchars($nome) . "</li>";
        }
        echo "</ul>";
    }
    
    // Verificar duplicações por ID
    $ids = array_column($todos_tecnicos, 'id');
    $ids_unicos = array_unique($ids);
    $duplicacoes_id = array_diff_assoc($ids, $ids_unicos);
    
    if (empty($duplicacoes_id)) {
        echo "<p>✅ <strong>Nenhuma duplicação encontrada por ID.</strong></p>";
    } else {
        echo "<p>❌ <strong>Duplicações de ID encontradas:</strong></p>";
        echo "<ul>";
        foreach ($duplicacoes_id as $id) {
            echo "<li>ID: " . $id . "</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 4. Verificar registros por técnico específico
echo "<h3>4. Análise Detalhada - Maycon e Sidnei</h3>";
try {
    // Maycon
    $stmt = $pdo_mci->prepare("
        SELECT COUNT(*) as total_registros
        FROM cad_registros r
        INNER JOIN sicoob_access_control.usuarios u ON r.tecnico_responsavel = u.id
        WHERE u.nome_completo LIKE '%Maycon%'
    ");
    $stmt->execute();
    $maycon_registros = $stmt->fetch();
    
    // Sidnei
    $stmt = $pdo_mci->prepare("
        SELECT COUNT(*) as total_registros
        FROM cad_registros r
        INNER JOIN sicoob_access_control.usuarios u ON r.tecnico_responsavel = u.id
        WHERE u.nome_completo LIKE '%Sidnei%'
    ");
    $stmt->execute();
    $sidnei_registros = $stmt->fetch();
    
    echo "<p><strong>Maycon:</strong> " . $maycon_registros['total_registros'] . " registros</p>";
    echo "<p><strong>Sidnei:</strong> " . $sidnei_registros['total_registros'] . " registros</p>";
    
    // Verificar se Sidnei está na lista completa
    $sidnei_encontrado = false;
    foreach ($todos_tecnicos as $tec) {
        if (stripos($tec['nome'], 'Sidnei') !== false) {
            $sidnei_encontrado = true;
            echo "<p>✅ <strong>Sidnei encontrado na consulta:</strong> " . htmlspecialchars($tec['nome']) . " (Meta: " . $tec['meta_total'] . ")</p>";
            break;
        }
    }
    
    if (!$sidnei_encontrado) {
        echo "<p>❌ <strong>Sidnei NÃO encontrado na consulta principal.</strong></p>";
        
        // Verificar por que Sidnei não aparece
        $stmt = $pdo_mci->prepare("
            SELECT 
                u.id,
                u.nome_completo,
                u.ativo,
                COUNT(r.id) as total_registros
            FROM sicoob_access_control.usuarios u
            LEFT JOIN cad_registros r ON u.id = r.tecnico_responsavel
            WHERE u.nome_completo LIKE '%Sidnei%'
            GROUP BY u.id, u.nome_completo, u.ativo
        ");
        $stmt->execute();
        $sidnei_debug = $stmt->fetchAll();
        
        echo "<p><strong>Debug Sidnei:</strong></p>";
        foreach ($sidnei_debug as $s) {
            echo "<p>ID: " . $s['id'] . ", Nome: " . htmlspecialchars($s['nome_completo']) . ", Ativo: " . ($s['ativo'] ? 'Sim' : 'Não') . ", Registros: " . $s['total_registros'] . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='dashboard.php'>🚀 Dashboard</a> | <a href='debug_tecnicos.php'>🔧 Debug Geral</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
