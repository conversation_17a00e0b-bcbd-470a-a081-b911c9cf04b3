<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Layout Dashboard MCI</title>
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;

            /* Cores da identidade visual para funcionários */
            --cor-funcionario-1: #00AE9D; /* Turquesa */
            --cor-funcionario-2: #003641; /* Verde escuro */
            --cor-funcionario-3: #6c757d; /* Cinza */
            --cor-funcionario-4: #C9D200; /* Verde claro */
            --cor-funcionario-5: #70B86C; /* Verde médio */
            --cor-funcionario-6: #494790; /* Roxo */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            height: 100vh;
        }

        .test-container {
            padding: 0.5rem;
            height: 100vh;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

        .test-header {
            text-align: center;
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: white;
            padding: 1rem;
            border-radius: 15px;
            margin-bottom: 1rem;
            flex-shrink: 0;
        }

        .test-metrics {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1.5rem;
            flex-shrink: 0;
        }

        .progress-bar-overview {
            height: 12px;
            background-color: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            margin-top: 1rem;
            display: flex;
        }

        .progress-segment {
            height: 100%;
            transition: width 0.8s ease;
        }

        .progress-segment:first-child {
            border-radius: 6px 0 0 6px;
        }

        .progress-segment:last-child {
            border-radius: 0 6px 6px 0;
        }

        /* Classes específicas para cada funcionário */
        .funcionario-1 { --cor-funcionario: var(--cor-funcionario-1); }
        .funcionario-2 { --cor-funcionario: var(--cor-funcionario-2); }
        .funcionario-3 { --cor-funcionario: var(--cor-funcionario-3); }
        .funcionario-4 { --cor-funcionario: var(--cor-funcionario-4); }
        .funcionario-5 { --cor-funcionario: var(--cor-funcionario-5); }
        .funcionario-6 { --cor-funcionario: var(--cor-funcionario-6); }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 0.8rem;
            flex: 1;
            overflow: hidden;
            height: 100%;
        }

        .test-card {
            background: white;
            border-radius: 16px;
            padding: clamp(0.8rem, 2vw, 1.5rem);
            border: 2px solid transparent;
            border-top: 3px solid var(--cor-funcionario, var(--sicoob-turquesa));
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            text-align: center;
            height: 100%;
            min-height: 0;
            overflow: hidden;
        }

        .test-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            padding: 2px;
            background: linear-gradient(135deg,
                var(--cor-funcionario, var(--sicoob-turquesa)) 0%,
                transparent 50%,
                var(--cor-funcionario, var(--sicoob-turquesa)) 100%);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            opacity: 0.3;
            z-index: -1;
        }

        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .test-card:hover::before {
            opacity: 0.5;
        }

        .avatar-container {
            position: relative;
            margin-bottom: clamp(0.5rem, 1.5vw, 1rem);
            flex-shrink: 0;
        }

        .avatar {
            width: clamp(60px, 8vw, 90px);
            height: clamp(60px, 8vw, 90px);
            border-radius: 50%;
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: clamp(1.2rem, 4vw, 2.2rem);
            font-weight: 700;
            position: relative;
            z-index: 2;
        }

        .progress-ring {
            position: absolute;
            top: -5px;
            left: -5px;
            width: calc(clamp(60px, 8vw, 90px) + 10px);
            height: calc(clamp(60px, 8vw, 90px) + 10px);
            border-radius: 50%;
            background: conic-gradient(
                var(--cor-funcionario, var(--sicoob-turquesa)) 0deg,
                var(--cor-funcionario, var(--sicoob-turquesa)) var(--progress-angle, 0deg),
                #e9ecef var(--progress-angle, 0deg),
                #e9ecef 360deg
            );
            z-index: 1;
        }

        .progress-ring::before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            width: clamp(60px, 8vw, 90px);
            height: clamp(60px, 8vw, 90px);
            border-radius: 50%;
            background: white;
        }

        .screen-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            z-index: 1000;
        }

        /* Responsividade - Sempre 3x2 */
        @media (min-width: 1920px) {
            .avatar { width: 80px; height: 80px; }
            .progress-ring { width: 90px; height: 90px; }
            .progress-ring::before { width: 80px; height: 80px; }
        }

        @media (min-width: 3840px) {
            .avatar { width: 90px; height: 90px; }
            .progress-ring { width: 100px; height: 100px; }
            .progress-ring::before { width: 90px; height: 90px; }
        }
    </style>
</head>
<body>
    <div class="screen-info">
        Resolução: <span id="resolution"></span> | 
        Viewport: <span id="viewport"></span>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h1>Dashboard MCI - Teste de Layout</h1>
            <p>Verificação de ajuste para TV</p>
        </div>

        <div class="test-metrics">
            <h3>Meta Anual da Equipe</h3>
            <div style="font-size: 2rem; font-weight: 700; color: var(--sicoob-turquesa); margin: 0.5rem 0;">450</div>
            <div style="color: #6c757d; margin-bottom: 1rem;">338 concluídos (75.1%)</div>
            <div class="progress-bar-overview">
                <div class="progress-segment" style="width: 20%; background-color: #00AE9D;" title="Ana Silva: 68 atualizações"></div>
                <div class="progress-segment" style="width: 15%; background-color: #003641;" title="Bruno Santos: 45 atualizações"></div>
                <div class="progress-segment" style="width: 18%; background-color: #6c757d;" title="Carlos Oliveira: 81 atualizações"></div>
                <div class="progress-segment" style="width: 11%; background-color: #C9D200;" title="Diana Costa: 30 atualizações"></div>
                <div class="progress-segment" style="width: 14%; background-color: #70B86C;" title="Eduardo Lima: 64 atualizações"></div>
                <div class="progress-segment" style="width: 11%; background-color: #494790;" title="Fernanda Rocha: 50 atualizações"></div>
            </div>
        </div>

        <div class="test-grid">
            <div class="test-card funcionario-1">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 270deg;"></div>
                    <div class="avatar">A</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Ana Silva</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Anual</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-funcionario); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">68/75</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">90.7%</div>
                </div>
            </div>

            <div class="test-card funcionario-2">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 216deg;"></div>
                    <div class="avatar">B</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Bruno Santos</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Anual</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-funcionario); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">45/75</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">60.0%</div>
                </div>
            </div>

            <div class="test-card funcionario-3">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 324deg;"></div>
                    <div class="avatar">C</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Carlos Oliveira</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Anual</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-funcionario); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">81/90</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">90.0%</div>
                </div>
            </div>

            <div class="test-card funcionario-4">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 180deg;"></div>
                    <div class="avatar">D</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Diana Costa</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Anual</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-funcionario); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">30/60</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">50.0%</div>
                </div>
            </div>

            <div class="test-card funcionario-5">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 288deg;"></div>
                    <div class="avatar">E</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Eduardo Lima</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Anual</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-funcionario); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">64/80</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">80.0%</div>
                </div>
            </div>

            <div class="test-card funcionario-6">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 252deg;"></div>
                    <div class="avatar">F</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Fernanda Rocha</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Anual</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-funcionario); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">50/72</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">69.4%</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateScreenInfo() {
            document.getElementById('resolution').textContent = `${screen.width}x${screen.height}`;
            document.getElementById('viewport').textContent = `${window.innerWidth}x${window.innerHeight}`;
        }

        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);

        // Teste de fullscreen
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F11') {
                e.preventDefault();
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }
        });
    </script>
</body>
</html>
