<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

$logger = new MciLogger();

echo "<h2>✅ Teste - Nomes da API Intranet nos Dashboards</h2>";

echo "<div style='border: 2px solid #28a745; padding: 15px; background: #d4edda; margin: 10px 0;'>";
echo "<h3>🔧 Alteração Implementada:</h3>";
echo "<p><strong>Objetivo:</strong> Exibir sempre o nome da API Intranet quando disponível, ao invés do nome do banco de dados</p>";
echo "<p><strong>Dashboards afetados:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Dashboard Cadastro:</strong> <code>mci/cadastro/dashboard.php</code></li>";
echo "<li>✅ <strong>Dashboard Técnicos Agrícolas:</strong> <code>mci/tecagricola/dashboard.php</code></li>";
echo "</ul>";
echo "</div>";

// Carregar API
try {
    require_once 'cadastro/config_api.php';
    $intranetAPI = getIntranetAPI($logger);
    $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
    echo "<p>✅ <strong>API carregada com " . count($usuarios_intranet) . " usuários</strong></p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

echo "<h3>📊 Teste 1: Dashboard de Cadastro</h3>";

// Testar funcionários do cadastro
$funcionarios_ids = [17, 18, 19, 20, 21, 22];
$funcionarios_teste = [];

foreach ($funcionarios_ids as $funcionario_id) {
    $query_funcionario = "
        SELECT u.id, u.nome_completo, u.email
        FROM sicoob_access_control.usuarios u
        WHERE u.id = ? AND u.ativo = TRUE
    ";
    
    $stmt = $pdo_sicoob->prepare($query_funcionario);
    $stmt->execute([$funcionario_id]);
    $funcionario = $stmt->fetch();
    
    if ($funcionario) {
        // Buscar dados da intranet
        $usuario_intranet = null;
        if (!empty($funcionario['email'])) {
            $email_key = strtolower(trim($funcionario['email']));
            $usuario_intranet = $usuarios_intranet[$email_key] ?? null;
        }
        
        // Aplicar lógica do dashboard
        $nome_banco = $funcionario['nome_completo'];
        $nome_api = $usuario_intranet['nome'] ?? null;
        $nome_exibir = $nome_banco;
        
        if ($usuario_intranet && !empty($usuario_intranet['nome'])) {
            $nome_exibir = $usuario_intranet['nome'];
        }
        
        $funcionarios_teste[] = [
            'id' => $funcionario_id,
            'nome_banco' => $nome_banco,
            'nome_api' => $nome_api,
            'nome_exibir' => $nome_exibir,
            'email' => $funcionario['email'],
            'tem_api' => $usuario_intranet !== null,
            'mudou_nome' => $nome_banco !== $nome_exibir
        ];
    }
}

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Nome no Banco</th><th>Nome na API</th><th>Nome Exibido</th><th>Email</th><th>Tem API</th><th>Nome Mudou</th></tr>";

foreach ($funcionarios_teste as $func) {
    $cor = '';
    if ($func['mudou_nome']) {
        $cor = 'style="background-color: #d4edda;"'; // Verde se mudou
    } elseif ($func['tem_api']) {
        $cor = 'style="background-color: #fff3cd;"'; // Amarelo se tem API mas não mudou
    } else {
        $cor = 'style="background-color: #f8d7da;"'; // Vermelho se não tem API
    }
    
    echo "<tr $cor>";
    echo "<td>" . $func['id'] . "</td>";
    echo "<td>" . htmlspecialchars($func['nome_banco']) . "</td>";
    echo "<td>" . htmlspecialchars($func['nome_api'] ?? 'N/A') . "</td>";
    echo "<td><strong>" . htmlspecialchars($func['nome_exibir']) . "</strong></td>";
    echo "<td>" . htmlspecialchars($func['email']) . "</td>";
    echo "<td>" . ($func['tem_api'] ? '✅ Sim' : '❌ Não') . "</td>";
    echo "<td>" . ($func['mudou_nome'] ? '✅ Sim' : '❌ Não') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>🌱 Teste 2: Dashboard Técnicos Agrícolas</h3>";

// Testar técnicos agrícolas
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

$tecnicos_teste = [];

foreach ($tecnicos_dados as $tecnico) {
    // Buscar dados da intranet
    $usuario_intranet = null;
    if (!empty($tecnico['email'])) {
        $email_key = strtolower(trim($tecnico['email']));
        $usuario_intranet = $usuarios_intranet[$email_key] ?? null;
    }
    
    // Aplicar lógica do dashboard
    $nome_banco = $tecnico['nome'];
    $nome_api = $usuario_intranet['nome'] ?? null;
    $nome_exibir = $nome_banco;
    
    if ($usuario_intranet && !empty($usuario_intranet['nome'])) {
        $nome_exibir = $usuario_intranet['nome'];
    }
    
    $tecnicos_teste[] = [
        'id' => $tecnico['id'],
        'nome_banco' => $nome_banco,
        'nome_api' => $nome_api,
        'nome_exibir' => $nome_exibir,
        'email' => $tecnico['email'],
        'meta_total' => $tecnico['meta_total'],
        'tem_api' => $usuario_intranet !== null,
        'mudou_nome' => $nome_banco !== $nome_exibir
    ];
}

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Nome no Banco</th><th>Nome na API</th><th>Nome Exibido</th><th>Email</th><th>Meta</th><th>Tem API</th><th>Nome Mudou</th></tr>";

foreach ($tecnicos_teste as $tec) {
    $cor = '';
    if ($tec['mudou_nome']) {
        $cor = 'style="background-color: #d4edda;"'; // Verde se mudou
    } elseif ($tec['tem_api']) {
        $cor = 'style="background-color: #fff3cd;"'; // Amarelo se tem API mas não mudou
    } else {
        $cor = 'style="background-color: #f8d7da;"'; // Vermelho se não tem API
    }
    
    // Destacar Maycon
    if ($tec['id'] == 58) {
        $cor = 'style="background-color: #cce5ff; border: 2px solid #007bff;"';
    }
    
    echo "<tr $cor>";
    echo "<td>" . $tec['id'] . "</td>";
    echo "<td>" . htmlspecialchars($tec['nome_banco']) . "</td>";
    echo "<td>" . htmlspecialchars($tec['nome_api'] ?? 'N/A') . "</td>";
    echo "<td><strong>" . htmlspecialchars($tec['nome_exibir']) . "</strong></td>";
    echo "<td>" . htmlspecialchars($tec['email']) . "</td>";
    echo "<td>" . $tec['meta_total'] . "</td>";
    echo "<td>" . ($tec['tem_api'] ? '✅ Sim' : '❌ Não') . "</td>";
    echo "<td>" . ($tec['mudou_nome'] ? '✅ Sim' : '❌ Não') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>📊 Resumo dos Resultados</h3>";

$total_funcionarios = count($funcionarios_teste);
$funcionarios_com_api = count(array_filter($funcionarios_teste, fn($f) => $f['tem_api']));
$funcionarios_nome_mudou = count(array_filter($funcionarios_teste, fn($f) => $f['mudou_nome']));

$total_tecnicos = count($tecnicos_teste);
$tecnicos_com_api = count(array_filter($tecnicos_teste, fn($t) => $t['tem_api']));
$tecnicos_nome_mudou = count(array_filter($tecnicos_teste, fn($t) => $t['mudou_nome']));

echo "<div style='border: 2px solid #17a2b8; padding: 15px; background: #d1ecf1;'>";
echo "<h4>📈 Dashboard de Cadastro:</h4>";
echo "<p><strong>Total de funcionários:</strong> $total_funcionarios</p>";
echo "<p><strong>Com dados da API:</strong> $funcionarios_com_api ($funcionarios_com_api/$total_funcionarios)</p>";
echo "<p><strong>Nome alterado pela API:</strong> $funcionarios_nome_mudou ($funcionarios_nome_mudou/$total_funcionarios)</p>";

echo "<h4>🌱 Dashboard Técnicos Agrícolas:</h4>";
echo "<p><strong>Total de técnicos:</strong> $total_tecnicos</p>";
echo "<p><strong>Com dados da API:</strong> $tecnicos_com_api ($tecnicos_com_api/$total_tecnicos)</p>";
echo "<p><strong>Nome alterado pela API:</strong> $tecnicos_nome_mudou ($tecnicos_nome_mudou/$total_tecnicos)</p>";

// Verificar Maycon especificamente
$maycon_teste = array_filter($tecnicos_teste, fn($t) => $t['id'] == 58);
if (!empty($maycon_teste)) {
    $maycon = array_values($maycon_teste)[0];
    echo "<h4>🎯 Maycon (ID 58):</h4>";
    echo "<p><strong>Nome no banco:</strong> " . htmlspecialchars($maycon['nome_banco']) . "</p>";
    echo "<p><strong>Nome na API:</strong> " . htmlspecialchars($maycon['nome_api'] ?? 'N/A') . "</p>";
    echo "<p><strong>Nome que será exibido:</strong> <strong>" . htmlspecialchars($maycon['nome_exibir']) . "</strong></p>";
    echo "<p><strong>Tem dados da API:</strong> " . ($maycon['tem_api'] ? '✅ Sim' : '❌ Não') . "</p>";
    echo "<p><strong>Nome mudou:</strong> " . ($maycon['mudou_nome'] ? '✅ Sim' : '❌ Não') . "</p>";
}

echo "</div>";

echo "<h3>🎨 Legenda das Cores</h3>";
echo "<div style='display: flex; gap: 15px; flex-wrap: wrap;'>";
echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; border: 1px solid #c3e6cb;'>✅ <strong>Verde:</strong> Nome alterado pela API</div>";
echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; border: 1px solid #ffeaa7;'>⚠️ <strong>Amarelo:</strong> Tem API mas nome igual</div>";
echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; border: 1px solid #f5c6cb;'>❌ <strong>Vermelho:</strong> Sem dados da API</div>";
echo "<div style='background: #cce5ff; padding: 10px; border-radius: 5px; border: 2px solid #007bff;'>🎯 <strong>Azul:</strong> Maycon (destaque)</div>";
echo "</div>";

echo "<h3>🚀 Próximos Passos</h3>";
echo "<div style='border: 2px solid #28a745; padding: 15px; background: #d4edda;'>";
echo "<p><strong>Para verificar as alterações:</strong></p>";
echo "<ol>";
echo "<li><a href='cadastro/dashboard.php' target='_blank'>🚀 Dashboard de Cadastro</a> - Verificar se os nomes estão corretos</li>";
echo "<li><a href='tecagricola/dashboard.php' target='_blank'>🌱 Dashboard Técnicos Agrícolas</a> - Verificar se os nomes estão corretos</li>";
echo "<li>Aguardar auto-refresh ou forçar refresh (Ctrl+F5)</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><a href='dashboard.php'>🏠 Dashboard Principal</a> | <a href='cadastro/dashboard.php'>📊 Dashboard Cadastro</a> | <a href='tecagricola/dashboard.php'>🌱 Dashboard Técnicos</a></p>";
?>
