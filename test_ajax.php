<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste AJAX - Transferência em Massa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste AJAX - Transferência em Massa</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>Testar API get_funcionario_registros.php</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Selecione um funcionário para testar:</label>
                    <select id="funcionario_teste" class="form-select">
                        <option value="">Carregando funcionários...</option>
                    </select>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testarAjax()">
                    Testar AJAX
                </button>
                
                <div id="resultado" class="result" style="display: none;"></div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>Teste Manual da API</h5>
            </div>
            <div class="card-body">
                <p>Você também pode testar manualmente enviando uma requisição POST para:</p>
                <code>ajax/get_funcionario_registros.php</code>
                
                <h6 class="mt-3">Exemplo de payload JSON:</h6>
                <pre>{"funcionario_id": "20"}</pre>
                
                <h6 class="mt-3">Funcionários disponíveis para teste:</h6>
                <div id="lista_funcionarios">Carregando...</div>
            </div>
        </div>
    </div>

    <script>
        // Carregar funcionários ao inicializar
        document.addEventListener('DOMContentLoaded', function() {
            carregarFuncionarios();
        });

        function carregarFuncionarios() {
            // Simular dados dos funcionários (normalmente viria do PHP)
            const funcionarios = [
                {id: 17, nome_completo: "Jussara Cristina Queiros Soares"},
                {id: 18, nome_completo: "Luana Viana Ramos"},
                {id: 19, nome_completo: "Emilay Cristina Guimaraes Nascimento"},
                {id: 20, nome_completo: "Milena Rodrigues Alves"},
                {id: 21, nome_completo: "Cristiano Prazer"},
                {id: 22, nome_completo: "Luis Otavio Santos"}
            ];

            const select = document.getElementById('funcionario_teste');
            const lista = document.getElementById('lista_funcionarios');
            
            select.innerHTML = '<option value="">Selecione um funcionário...</option>';
            let listaHtml = '<ul>';
            
            funcionarios.forEach(func => {
                select.innerHTML += `<option value="${func.id}">${func.nome_completo}</option>`;
                listaHtml += `<li>ID: ${func.id} - ${func.nome_completo}</li>`;
            });
            
            listaHtml += '</ul>';
            lista.innerHTML = listaHtml;
        }

        function testarAjax() {
            const funcionarioId = document.getElementById('funcionario_teste').value;
            const resultadoDiv = document.getElementById('resultado');
            
            if (!funcionarioId) {
                mostrarResultado('Por favor, selecione um funcionário.', 'error');
                return;
            }

            // Mostrar loading
            mostrarResultado('🔄 Testando API...', 'info');

            // Fazer requisição AJAX
            fetch('ajax/get_funcionario_registros.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    funcionario_id: funcionarioId
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    let html = '<h6>✅ Sucesso! Dados retornados:</h6>';
                    html += '<pre>' + JSON.stringify(data.data, null, 2) + '</pre>';
                    
                    html += '<h6>📊 Resumo:</h6>';
                    html += `<ul>`;
                    html += `<li><strong>Funcionário ID:</strong> ${data.data.funcionario_id}</li>`;
                    html += `<li><strong>Total de Registros:</strong> ${data.data.total_registros}</li>`;
                    html += `<li><strong>Registros Disponíveis:</strong> ${data.data.registros_disponiveis}</li>`;
                    html += `<li><strong>Registros Removidos:</strong> ${data.data.registros_removidos}</li>`;
                    html += `</ul>`;
                    
                    if (data.data.distribuicao_status && data.data.distribuicao_status.length > 0) {
                        html += '<h6>📈 Distribuição por Status:</h6>';
                        html += '<ul>';
                        data.data.distribuicao_status.forEach(status => {
                            html += `<li>${status.status}: ${status.quantidade} registros</li>`;
                        });
                        html += '</ul>';
                    }
                    
                    mostrarResultado(html, 'success');
                } else {
                    mostrarResultado(`❌ Erro: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                mostrarResultado(`❌ Erro na requisição: ${error.message}`, 'error');
            });
        }

        function mostrarResultado(html, tipo) {
            const resultadoDiv = document.getElementById('resultado');
            resultadoDiv.innerHTML = html;
            resultadoDiv.className = `result ${tipo}`;
            resultadoDiv.style.display = 'block';
        }
    </script>
</body>
</html>
