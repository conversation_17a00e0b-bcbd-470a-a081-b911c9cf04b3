<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug JavaScript Filtros - MCI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        body { background-color: #f8f9fa; padding: 20px; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        .btn-excel { background-color: #28a745; border-color: #28a745; color: white; }
        .btn-excel:hover { background-color: #218838; border-color: #1e7e34; color: white; }
        .debug-output { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-primary mb-4">
            <i class="fas fa-bug"></i> 
            Debug JavaScript - Captura de Filtros
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Objetivo do Debug</h5>
            <p class="mb-0">
                Vamos simular exatamente a estrutura da página gerenciar.php para verificar 
                se o JavaScript está capturando corretamente os filtros de Status e PA.
            </p>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-filter"></i> Simulação do Formulário de Filtros
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Simulando a estrutura exata do gerenciar.php -->
                        <form method="GET" class="row g-2 align-items-end">
                            <div class="col-lg-2">
                                <label class="form-label small">Status</label>
                                <select name="status" class="form-select form-select-sm">
                                    <option value="">Todos</option>
                                    <option value="Pendente">Pendente</option>
                                    <option value="Solicitado">Solicitado</option>
                                    <option value="Atualizado">Atualizado</option>
                                    <option value="Removido">Removido</option>
                                </select>
                            </div>
                            
                            <div class="col-lg-1">
                                <label class="form-label small">PA</label>
                                <select name="pa" class="form-select form-select-sm">
                                    <option value="">Todos</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="5">5</option>
                                    <option value="10">10</option>
                                </select>
                            </div>
                            
                            <div class="col-lg-2">
                                <label class="form-label small">Associado</label>
                                <input type="text" name="associado" class="form-control form-control-sm" placeholder="Nome ou CPF...">
                            </div>
                            
                            <div class="col-lg-2">
                                <label class="form-label small">Funcionário</label>
                                <select name="funcionario" class="form-select form-select-sm">
                                    <option value="">Todos</option>
                                    <option value="17">Jussara</option>
                                    <option value="20">Milena</option>
                                </select>
                            </div>
                            
                            <div class="col-lg-2">
                                <label class="form-label small">Técnico</label>
                                <select name="tecnico" class="form-select form-select-sm">
                                    <option value="">Todos</option>
                                    <option value="1">Técnico 1</option>
                                    <option value="2">Técnico 2</option>
                                </select>
                            </div>
                            
                            <div class="col-lg-1">
                                <button type="submit" class="btn btn-primary btn-sm w-100">
                                    <i class="fas fa-filter"></i> Filtrar
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <button type="button" class="btn btn-excel" onclick="testarCapturaDeFiltros()">
                                <i class="fas fa-file-excel"></i> Testar Captura de Filtros
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-terminal"></i> Output do Debug
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="debug-output" class="debug-output">
                            <em>Clique em "Testar Captura de Filtros" para ver o resultado</em>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Simulando modal de edição para testar conflito -->
        <div class="modal" id="editModal" tabindex="-1" style="display: none;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form method="POST" id="editForm">
                        <div class="modal-body">
                            <select name="status" class="form-select">
                                <option value="1">Pendente</option>
                                <option value="2">Solicitado</option>
                            </select>
                            <select name="funcionario" class="form-select">
                                <option value="17">Jussara</option>
                                <option value="20">Milena</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-code"></i> Código JavaScript Usado
                        </h6>
                    </div>
                    <div class="card-body">
                        <pre class="small"><code>function testarCapturaDeFiltros() {
    // Mesma lógica do gerenciar.php
    const formFiltros = document.querySelector('form[method="GET"]');
    const status = formFiltros?.querySelector('select[name="status"]')?.value || '';
    const pa = formFiltros?.querySelector('select[name="pa"]')?.value || '';
    const associado = formFiltros?.querySelector('input[name="associado"]')?.value || '';
    const funcionario = formFiltros?.querySelector('select[name="funcionario"]')?.value || '';
    const tecnico = formFiltros?.querySelector('select[name="tecnico"]')?.value || '';
    
    // Construir URL
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    if (pa) params.append('pa', pa);
    if (associado) params.append('associado', associado);
    if (funcionario) params.append('funcionario', funcionario);
    if (tecnico) params.append('tecnico', tecnico);
    params.append('download', 'excel');
    
    const finalUrl = 'export_excel_real.php?' + params.toString();
    
    // Mostrar resultado
    document.getElementById('debug-output').innerHTML = `
        &lt;strong&gt;Filtros Capturados:&lt;/strong&gt;&lt;br&gt;
        • Status: "${status}"&lt;br&gt;
        • PA: "${pa}"&lt;br&gt;
        • Associado: "${associado}"&lt;br&gt;
        • Funcionário: "${funcionario}"&lt;br&gt;
        • Técnico: "${tecnico}"&lt;br&gt;&lt;br&gt;
        &lt;strong&gt;URL Gerada:&lt;/strong&gt;&lt;br&gt;
        &lt;code&gt;${finalUrl}&lt;/code&gt;&lt;br&gt;&lt;br&gt;
        &lt;a href="${finalUrl}" class="btn btn-sm btn-success" target="_blank"&gt;
            &lt;i class="fas fa-download"&gt;&lt;/i&gt; Testar Download
        &lt;/a&gt;
    `;
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-list-check"></i> Testes Recomendados
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-tag"></i> Teste 1: Status</h6>
                                <ol class="small">
                                    <li>Selecione "Pendente" no campo Status</li>
                                    <li>Clique em "Testar Captura de Filtros"</li>
                                    <li>Verifique se Status = "Pendente" no output</li>
                                    <li>Clique em "Testar Download"</li>
                                    <li>Verifique se baixa apenas pendentes</li>
                                </ol>
                            </div>
                            
                            <div class="col-md-4">
                                <h6><i class="fas fa-building"></i> Teste 2: PA</h6>
                                <ol class="small">
                                    <li>Selecione "2" no campo PA</li>
                                    <li>Clique em "Testar Captura de Filtros"</li>
                                    <li>Verifique se PA = "2" no output</li>
                                    <li>Clique em "Testar Download"</li>
                                    <li>Verifique se baixa apenas PA 2</li>
                                </ol>
                            </div>
                            
                            <div class="col-md-4">
                                <h6><i class="fas fa-layer-group"></i> Teste 3: Combinado</h6>
                                <ol class="small">
                                    <li>Selecione "Pendente" + PA "2"</li>
                                    <li>Clique em "Testar Captura de Filtros"</li>
                                    <li>Verifique ambos no output</li>
                                    <li>Clique em "Testar Download"</li>
                                    <li>Verifique se baixa apenas pendentes do PA 2</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-warning mt-4">
            <h6><i class="fas fa-exclamation-triangle"></i> Se os Filtros Não Funcionarem Aqui</h6>
            <p class="mb-0">
                Se mesmo nesta página de teste os filtros de Status e PA não funcionarem, 
                o problema está no código JavaScript ou na estrutura HTML. 
                Se funcionarem aqui mas não no gerenciar.php, o problema está na estrutura específica daquela página.
            </p>
        </div>
    </div>

    <script>
        function testarCapturaDeFiltros() {
            console.log('=== TESTE DE CAPTURA DE FILTROS ===');
            
            // Mesma lógica do gerenciar.php
            const formFiltros = document.querySelector('form[method="GET"]');
            console.log('Formulário encontrado:', formFiltros);
            
            const status = formFiltros?.querySelector('select[name="status"]')?.value || '';
            const pa = formFiltros?.querySelector('select[name="pa"]')?.value || '';
            const associado = formFiltros?.querySelector('input[name="associado"]')?.value || '';
            const funcionario = formFiltros?.querySelector('select[name="funcionario"]')?.value || '';
            const tecnico = formFiltros?.querySelector('select[name="tecnico"]')?.value || '';
            
            console.log('Filtros capturados:', {status, pa, associado, funcionario, tecnico});
            
            // Debug adicional
            console.log('Campos encontrados:', {
                statusField: formFiltros?.querySelector('select[name="status"]'),
                paField: formFiltros?.querySelector('select[name="pa"]'),
                associadoField: formFiltros?.querySelector('input[name="associado"]'),
                funcionarioField: formFiltros?.querySelector('select[name="funcionario"]'),
                tecnicoField: formFiltros?.querySelector('select[name="tecnico"]')
            });
            
            // Construir URL
            const params = new URLSearchParams();
            if (status) params.append('status', status);
            if (pa) params.append('pa', pa);
            if (associado) params.append('associado', associado);
            if (funcionario) params.append('funcionario', funcionario);
            if (tecnico) params.append('tecnico', tecnico);
            params.append('download', 'excel');
            
            const finalUrl = 'export_excel_real.php?' + params.toString();
            console.log('URL gerada:', finalUrl);
            
            // Mostrar resultado
            document.getElementById('debug-output').innerHTML = `
                <strong>Filtros Capturados:</strong><br>
                • Status: "${status}"<br>
                • PA: "${pa}"<br>
                • Associado: "${associado}"<br>
                • Funcionário: "${funcionario}"<br>
                • Técnico: "${tecnico}"<br><br>
                <strong>URL Gerada:</strong><br>
                <code>${finalUrl}</code><br><br>
                <a href="${finalUrl}" class="btn btn-sm btn-success" target="_blank">
                    <i class="fas fa-download"></i> Testar Download
                </a>
            `;
        }
    </script>
</body>
</html>
