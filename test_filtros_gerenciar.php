<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>e Filtros Gerenciar - MCI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        body { background-color: #f8f9fa; padding: 20px; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        .btn-excel { background-color: #28a745; border-color: #28a745; color: white; }
        .btn-excel:hover { background-color: #218838; border-color: #1e7e34; color: white; }
        .problem { background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0; }
        .solution { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-primary mb-4">
            <i class="fas fa-wrench"></i> 
            Correção Final - Filtros na Página Gerenciar
        </h1>
        
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> Problema Identificado</h5>
            <p class="mb-0">
                Na página `test_filtros_download.php` os filtros funcionaram, mas na página `gerenciar.php` ainda baixava tudo. 
                <strong>Causa:</strong> Múltiplos campos com mesmo `name` (formulário de filtros + modais de edição).
            </p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bug"></i> Problema Específico
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="problem">
                            <h6><i class="fas fa-code"></i> JavaScript Ambíguo</h6>
                            <p><strong>Código Anterior:</strong></p>
                            <pre class="small"><code>const status = document.querySelector('select[name="status"]')?.value || '';</code></pre>
                            
                            <p><strong>Problema:</strong></p>
                            <ul class="small">
                                <li>Página `gerenciar.php` tem múltiplos campos `name="status"`</li>
                                <li>Formulário de filtros: `select[name="status"]`</li>
                                <li>Modal de edição: `select[name="status"]`</li>
                                <li>JavaScript capturava o primeiro (modal), não o filtro</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tools"></i> Correção Aplicada
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="solution">
                            <h6><i class="fas fa-code"></i> JavaScript Específico</h6>
                            <p><strong>Código Corrigido:</strong></p>
                            <pre class="small"><code>const formFiltros = document.querySelector('form[method="GET"]');
const status = formFiltros?.querySelector('select[name="status"]')?.value || '';</code></pre>
                            
                            <p><strong>Solução:</strong></p>
                            <ul class="small">
                                <li>Primeiro localiza o formulário de filtros específico</li>
                                <li>Depois busca os campos dentro desse formulário</li>
                                <li>Evita conflito com campos dos modais</li>
                                <li>Captura apenas valores dos filtros aplicados</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-search"></i> Estrutura da Página Gerenciar
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-filter"></i> Formulário de Filtros</h6>
                                <pre class="small"><code>&lt;form method="GET" class="row g-2 align-items-end"&gt;
    &lt;select name="status"&gt;...&lt;/select&gt;
    &lt;select name="pa"&gt;...&lt;/select&gt;
    &lt;input name="associado"&gt;...&lt;/input&gt;
    &lt;select name="funcionario"&gt;...&lt;/select&gt;
    &lt;select name="tecnico"&gt;...&lt;/select&gt;
    &lt;select name="mes_renda"&gt;...&lt;/select&gt;
&lt;/form&gt;</code></pre>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-edit"></i> Modal de Edição</h6>
                                <pre class="small"><code>&lt;form method="POST" id="editForm"&gt;
    &lt;select name="funcionario"&gt;...&lt;/select&gt;
    &lt;select name="tecnico_responsavel"&gt;...&lt;/select&gt;
    &lt;select name="status"&gt;...&lt;/select&gt;
&lt;/form&gt;</code></pre>
                                <p class="small text-muted">
                                    ⚠️ Campos com nomes similares causavam conflito
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-play"></i> Teste a Correção
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Como Testar:</h6>
                            <ol>
                                <li><strong>Acesse</strong> a página gerenciar.php</li>
                                <li><strong>Aplique um filtro</strong> (ex: Status = "Pendente")</li>
                                <li><strong>Clique</strong> em "Filtrar" para aplicar</li>
                                <li><strong>Clique</strong> em "Download Excel"</li>
                                <li><strong>Verifique</strong> se o arquivo baixado contém apenas registros pendentes</li>
                            </ol>
                        </div>
                        
                        <div class="text-center">
                            <a href="gerenciar.php" class="btn btn-primary btn-lg" target="_blank">
                                <i class="fas fa-list"></i> Testar na Página Gerenciar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-code"></i> Código JavaScript Completo Corrigido
                        </h6>
                    </div>
                    <div class="card-body">
                        <pre class="small"><code>function downloadExcel() {
    try {
        console.log('Iniciando download Excel...');
        
        // Construir URL com os filtros atuais da interface
        const params = new URLSearchParams();
        
        // Capturar filtros atuais dos campos da interface (especificamente do formulário de filtros)
        const formFiltros = document.querySelector('form[method="GET"]');
        const status = formFiltros?.querySelector('select[name="status"]')?.value || '';
        const pa = formFiltros?.querySelector('select[name="pa"]')?.value || '';
        const associado = formFiltros?.querySelector('input[name="associado"]')?.value || '';
        const funcionario = formFiltros?.querySelector('select[name="funcionario"]')?.value || '';
        const tecnico = formFiltros?.querySelector('select[name="tecnico"]')?.value || '';
        const mesRenda = formFiltros?.querySelector('select[name="mes_renda"]')?.value || '';
        
        // Capturar ordenação atual (das variáveis PHP)
        const sort = '<?php echo htmlspecialchars($sort_column); ?>';
        const dir = '<?php echo htmlspecialchars($sort_direction); ?>';
        
        console.log('Filtros capturados da interface:', {status, pa, associado, funcionario, tecnico, mesRenda, sort, dir});
        
        if (status) params.append('status', status);
        if (pa) params.append('pa', pa);
        if (associado) params.append('associado', associado);
        if (funcionario) params.append('funcionario', funcionario);
        if (tecnico) params.append('tecnico', tecnico);
        if (mesRenda) params.append('mes_renda', mesRenda);
        if (sort) params.append('sort', sort);
        if (dir) params.append('dir', dir);
        
        // Adicionar parâmetro para indicar que é download
        params.append('download', 'excel');
        
        // Construir URL final
        const finalUrl = 'export_excel.php?' + params.toString();
        console.log('URL final:', finalUrl);
        
        // Mostrar feedback visual
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando...';
        btn.disabled = true;
        
        // Redirecionar para download (usando export real para evitar problema de collation)
        const realUrl = finalUrl.replace('export_excel.php', 'export_excel_real.php');
        console.log('URL real (sem JOIN):', realUrl);
        window.location.href = realUrl;
        
        // Restaurar botão após um tempo
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 3000);
        
    } catch (error) {
        console.error('Erro no download Excel:', error);
        alert('Erro ao gerar relatório. Verifique o console para mais detalhes.');
    }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-list-check"></i> Testes Recomendados
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-tag"></i> Teste 1: Status</h6>
                                <ol class="small">
                                    <li>Vá para gerenciar.php</li>
                                    <li>Selecione Status = "Pendente"</li>
                                    <li>Clique em "Filtrar"</li>
                                    <li>Clique em "Download Excel"</li>
                                    <li>Verifique se baixou apenas pendentes</li>
                                </ol>
                            </div>
                            
                            <div class="col-md-4">
                                <h6><i class="fas fa-building"></i> Teste 2: PA</h6>
                                <ol class="small">
                                    <li>Vá para gerenciar.php</li>
                                    <li>Selecione um PA específico</li>
                                    <li>Clique em "Filtrar"</li>
                                    <li>Clique em "Download Excel"</li>
                                    <li>Verifique se baixou apenas do PA</li>
                                </ol>
                            </div>
                            
                            <div class="col-md-4">
                                <h6><i class="fas fa-user"></i> Teste 3: Funcionário</h6>
                                <ol class="small">
                                    <li>Vá para gerenciar.php</li>
                                    <li>Selecione um funcionário</li>
                                    <li>Clique em "Filtrar"</li>
                                    <li>Clique em "Download Excel"</li>
                                    <li>Verifique se baixou apenas do funcionário</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5><i class="fas fa-check-circle"></i> Correção Específica Aplicada!</h5>
            <p class="mb-0">
                O JavaScript foi corrigido para capturar especificamente os campos do formulário de filtros, 
                evitando conflito com os campos dos modais de edição. 
                <strong>Agora o download na página gerenciar.php deve respeitar os filtros aplicados!</strong>
            </p>
        </div>
    </div>
</body>
</html>
