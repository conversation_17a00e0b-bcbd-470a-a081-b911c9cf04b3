<?php
/**
 * <PERSON>ript para criar a tabela mci_metas
 * Execute este arquivo para criar a tabela de metas do sistema MCI
 */

require_once 'auth_check.php';

// Verificar se o usuário tem permissão de administrador
if ($_SESSION['mci_permission_level'] !== 'administrador') {
    die('Acesso negado. Apenas administradores podem executar este script.');
}

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criar Tabela MCI Metas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { margin-top: 2rem; }
        .result { margin: 1rem 0; padding: 1rem; border-radius: 5px; }
        .success { background-color: #d1edff; border-left: 4px solid #0d6efd; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2>Criação da Tabela MCI Metas</h2>
                <p class="text-muted">Este script criará a tabela <code>mci_metas</code> necessária para o sistema de metas.</p>
                
                <?php
                try {
                    echo "<h4>Executando criação da tabela mci_metas...</h4>";
                    
                    // 1. Verificar se a tabela já existe
                    echo "<div class='result warning'>";
                    echo "<strong>1. Verificando se a tabela já existe...</strong><br>";
                    
                    $stmt = $pdo_mci->prepare("SHOW TABLES LIKE 'mci_metas'");
                    $stmt->execute();
                    $table_exists = $stmt->fetch();
                    
                    if ($table_exists) {
                        echo "⚠️ Tabela 'mci_metas' já existe.";
                    } else {
                        echo "✅ Tabela 'mci_metas' não existe. Prosseguindo com a criação.";
                    }
                    echo "</div>";
                    
                    // 2. Criar tabela mci_metas
                    echo "<div class='result success'>";
                    echo "<strong>2. Criando tabela mci_metas...</strong><br>";
                    
                    $sql_create_table = "
                        CREATE TABLE IF NOT EXISTS mci_metas (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            porcentagem_meta DECIMAL(5,2) NOT NULL COMMENT 'Porcentagem da meta (ex: 75.00 para 75%)',
                            ativo BOOLEAN DEFAULT TRUE COMMENT 'Meta ativa/inativa',
                            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de criação',
                            data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data de atualização',
                            usuario_criacao INT COMMENT 'ID do usuário que criou',
                            usuario_atualizacao INT COMMENT 'ID do usuário que atualizou',
                            
                            INDEX idx_ativo (ativo),
                            INDEX idx_data_criacao (data_criacao)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela de configuração de metas do MCI'
                    ";
                    
                    $pdo_mci->exec($sql_create_table);
                    echo "✅ Tabela 'mci_metas' criada com sucesso!<br>";
                    echo "</div>";
                    
                    // 3. Inserir meta padrão se não existir
                    echo "<div class='result success'>";
                    echo "<strong>3. Verificando e inserindo meta padrão...</strong><br>";
                    
                    $stmt = $pdo_mci->prepare("SELECT COUNT(*) FROM mci_metas WHERE ativo = TRUE");
                    $stmt->execute();
                    $has_active_meta = $stmt->fetchColumn() > 0;
                    
                    if (!$has_active_meta) {
                        $stmt = $pdo_mci->prepare("
                            INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) 
                            VALUES (75.00, TRUE, ?)
                        ");
                        $stmt->execute([$_SESSION['user_id']]);
                        echo "✅ Meta padrão de 75% inserida com sucesso!<br>";
                    } else {
                        echo "ℹ️ Meta ativa já existe. Não foi necessário inserir meta padrão.<br>";
                    }
                    echo "</div>";
                    
                    // 4. Verificar estrutura da tabela
                    echo "<div class='result success'>";
                    echo "<strong>4. Estrutura da tabela criada:</strong><br>";
                    
                    $stmt = $pdo_mci->prepare("DESCRIBE mci_metas");
                    $stmt->execute();
                    $columns = $stmt->fetchAll();
                    
                    echo "<table class='table table-sm table-bordered mt-2'>";
                    echo "<thead><tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($columns as $column) {
                        echo "<tr>";
                        echo "<td><code>{$column['Field']}</code></td>";
                        echo "<td>{$column['Type']}</td>";
                        echo "<td>{$column['Null']}</td>";
                        echo "<td>{$column['Key']}</td>";
                        echo "<td>{$column['Default']}</td>";
                        echo "<td>{$column['Extra']}</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                    echo "</div>";
                    
                    // 5. Mostrar dados atuais
                    echo "<div class='result success'>";
                    echo "<strong>5. Dados atuais na tabela:</strong><br>";
                    
                    $stmt = $pdo_mci->prepare("SELECT * FROM mci_metas ORDER BY id DESC");
                    $stmt->execute();
                    $metas = $stmt->fetchAll();
                    
                    if (empty($metas)) {
                        echo "Nenhuma meta encontrada na tabela.";
                    } else {
                        echo "<table class='table table-sm table-bordered mt-2'>";
                        echo "<thead><tr><th>ID</th><th>Porcentagem</th><th>Ativo</th><th>Data Criação</th><th>Usuário Criação</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($metas as $meta) {
                            $ativo_badge = $meta['ativo'] ? "<span class='badge bg-success'>Sim</span>" : "<span class='badge bg-secondary'>Não</span>";
                            echo "<tr>";
                            echo "<td>{$meta['id']}</td>";
                            echo "<td>{$meta['porcentagem_meta']}%</td>";
                            echo "<td>{$ativo_badge}</td>";
                            echo "<td>" . date('d/m/Y H:i:s', strtotime($meta['data_criacao'])) . "</td>";
                            echo "<td>{$meta['usuario_criacao']}</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                    }
                    echo "</div>";
                    
                    // 6. Teste de funcionamento
                    echo "<div class='result success'>";
                    echo "<strong>6. Teste de funcionamento:</strong><br>";
                    
                    $stmt = $pdo_mci->prepare("SELECT porcentagem_meta FROM mci_metas WHERE ativo = TRUE ORDER BY id DESC LIMIT 1");
                    $stmt->execute();
                    $meta_atual = $stmt->fetch();
                    $porcentagem_meta = $meta_atual ? $meta_atual['porcentagem_meta'] : 75.00;
                    
                    echo "✅ Meta atual carregada: <strong>{$porcentagem_meta}%</strong><br>";
                    echo "✅ Sistema de metas funcionando corretamente!";
                    echo "</div>";
                    
                    echo "<div class='alert alert-success mt-4'>";
                    echo "<h5>✅ Instalação Concluída com Sucesso!</h5>";
                    echo "<p>A tabela <code>mci_metas</code> foi criada e configurada corretamente.</p>";
                    echo "<p><strong>Próximos passos:</strong></p>";
                    echo "<ul>";
                    echo "<li>Acesse a <a href='metas.php'>página de metas</a> para configurar a porcentagem desejada</li>";
                    echo "<li>Teste o <a href='cadastro/dashboard.php'>dashboard</a> para verificar se está funcionando</li>";
                    echo "<li>Execute o <a href='cadastro/test_api.php'>teste de API</a> para verificar a integração</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "<div class='result error'>";
                    echo "<strong>❌ Erro durante a execução:</strong><br>";
                    echo htmlspecialchars($e->getMessage());
                    echo "</div>";
                    
                    echo "<div class='alert alert-danger mt-4'>";
                    echo "<h5>❌ Erro na Instalação</h5>";
                    echo "<p>Ocorreu um erro durante a criação da tabela. Verifique:</p>";
                    echo "<ul>";
                    echo "<li>Se você tem permissões de administrador</li>";
                    echo "<li>Se a conexão com o banco de dados está funcionando</li>";
                    echo "<li>Se o banco 'mci' existe e está acessível</li>";
                    echo "</ul>";
                    echo "</div>";
                }
                ?>
                
                <div class="mt-4">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Voltar ao MCI
                    </a>
                    <a href="metas.php" class="btn btn-success">
                        <i class="fas fa-target"></i> Ir para Metas
                    </a>
                    <a href="cadastro/dashboard.php" class="btn btn-info">
                        <i class="fas fa-chart-line"></i> Testar Dashboard
                    </a>
                    <button onclick="location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-sync-alt"></i> Executar Novamente
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
