<?php
/**
 * Script de Diagnóstico Preciso - Divergência entre Cadastro e Técnicos Agrícolas
 * Foca apenas em registros com status "Solicitado" ou "Atualizado"
 * Nestes casos, ambos os campos (funcionário e técnico) devem estar preenchidos
 */

require_once 'config/config.php';
require_once 'classes/Logger.php';

$logger = new MciLogger();
$logger->log('Diagnóstico preciso de divergência', 'Análise focada em registros Solicitado/Atualizado');

echo "<h1>🔍 Diagnóstico Preciso - Divergência Cadastro vs Técnicos</h1>";
echo "<p><strong>Foco:</strong> Apenas registros com status 'Solicitado' ou 'Atualizado'</p>";
echo "<p><strong>Regra:</strong> Nestes casos, ambos os campos (funcionário e técnico) devem estar preenchidos</p>";
echo "<hr>";

try {
    // 1. ANÁLISE DA EQUIPE DE CADASTRO (Status Solicitado/Atualizado)
    echo "<h2>📊 1. Análise da Equipe de Cadastro (Solicitado/Atualizado)</h2>";
    
    $funcionarios_ids = [17, 18, 19, 20, 21, 22];
    $total_cadastro = 0;
    $total_atualizados_cadastro = 0;
    
    foreach ($funcionarios_ids as $funcionario_id) {
        $query_cadastro = "
            SELECT
                COUNT(r.id) as total_registros,
                SUM(CASE
                    WHEN r.data_solicitacao_laudo IS NOT NULL
                    AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                    THEN 1 ELSE 0
                END) as atualizados_ano
            FROM cad_registros r
            INNER JOIN cad_status s ON r.status = s.id
            WHERE r.funcionario = ?
            AND s.nome IN ('Solicitado', 'Atualizado')
            AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        ";
        
        $stmt = $pdo_mci->prepare($query_cadastro);
        $stmt->execute([$funcionario_id]);
        $dados = $stmt->fetch();
        
        if ($dados['total_registros'] > 0) {
            echo "<p><strong>Funcionário ID $funcionario_id:</strong> ";
            echo "Total (Solicitado/Atualizado): " . number_format($dados['total_registros']) . " | ";
            echo "Atualizados no ano: " . number_format($dados['atualizados_ano']) . "</p>";
            
            $total_cadastro += $dados['total_registros'];
            $total_atualizados_cadastro += $dados['atualizados_ano'];
        }
    }
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📈 Resumo Cadastro (Solicitado/Atualizado):</h3>";
    echo "<p><strong>Total de Registros:</strong> " . number_format($total_cadastro) . "</p>";
    echo "<p><strong>Total Atualizados no Ano:</strong> " . number_format($total_atualizados_cadastro) . "</p>";
    echo "</div>";
    
    // 2. ANÁLISE DOS TÉCNICOS AGRÍCOLAS (Status Solicitado/Atualizado)
    echo "<h2>🌱 2. Análise dos Técnicos Agrícolas (Solicitado/Atualizado)</h2>";
    
    $query_tecnicos = "
        SELECT
            u.id,
            u.nome_completo,
            COUNT(r.id) as total_registros,
            COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        INNER JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 
        AND r.tecnico_responsavel IS NOT NULL
        AND s.nome IN ('Solicitado', 'Atualizado')
        GROUP BY u.id, u.nome_completo
        HAVING total_registros > 0
        ORDER BY total_registros DESC
    ";
    
    $stmt = $pdo_mci->prepare($query_tecnicos);
    $stmt->execute();
    $tecnicos = $stmt->fetchAll();
    
    $total_tecnicos = 0;
    $total_atualizados_tecnicos = 0;
    
    foreach ($tecnicos as $tecnico) {
        echo "<p><strong>{$tecnico['nome_completo']} (ID: {$tecnico['id']}):</strong> ";
        echo "Total (Solicitado/Atualizado): " . number_format($tecnico['total_registros']) . " | ";
        echo "Atualizados: " . number_format($tecnico['atualizados_total']) . "</p>";
        
        $total_tecnicos += $tecnico['total_registros'];
        $total_atualizados_tecnicos += $tecnico['atualizados_total'];
    }
    
    echo "<div style='background: #f3e5f5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📈 Resumo Técnicos (Solicitado/Atualizado):</h3>";
    echo "<p><strong>Total de Registros Atribuídos:</strong> " . number_format($total_tecnicos) . "</p>";
    echo "<p><strong>Total Atualizados:</strong> " . number_format($total_atualizados_tecnicos) . "</p>";
    echo "</div>";
    
    // 3. ANÁLISE DA DIVERGÊNCIA (Status Solicitado/Atualizado)
    echo "<h2>⚠️ 3. Análise da Divergência (Solicitado/Atualizado)</h2>";
    
    $diferenca_total = $total_tecnicos - $total_cadastro;
    $diferenca_atualizados = $total_atualizados_tecnicos - $total_atualizados_cadastro;
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🔍 Identificação do Problema:</h3>";
    echo "<p><strong>Diferença Total de Registros:</strong> " . number_format($diferenca_total) . "</p>";
    echo "<p><strong>Diferença de Atualizados:</strong> " . number_format($diferenca_atualizados) . "</p>";
    echo "</div>";
    
    // 4. INVESTIGAÇÃO DETALHADA (Status Solicitado/Atualizado)
    echo "<h2>🔬 4. Investigação Detalhada (Solicitado/Atualizado)</h2>";
    
    // 4.1 Verificar registros sem funcionário mas com técnico (Status Solicitado/Atualizado)
    $query_sem_funcionario = "
        SELECT COUNT(*) as total
        FROM cad_registros r
        INNER JOIN cad_status s ON r.status = s.id
        WHERE r.funcionario IS NULL 
        AND r.tecnico_responsavel IS NOT NULL
        AND s.nome IN ('Solicitado', 'Atualizado')
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_sem_funcionario);
    $stmt->execute();
    $sem_funcionario = $stmt->fetch();
    
    echo "<p><strong>Registros SEM funcionário mas COM técnico (Solicitado/Atualizado):</strong> " . number_format($sem_funcionario['total']) . "</p>";
    
    // 4.2 Verificar registros com funcionário mas sem técnico (Status Solicitado/Atualizado)
    $query_sem_tecnico = "
        SELECT COUNT(*) as total
        FROM cad_registros r
        INNER JOIN cad_status s ON r.status = s.id
        WHERE r.funcionario IS NOT NULL 
        AND r.tecnico_responsavel IS NULL
        AND s.nome IN ('Solicitado', 'Atualizado')
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_sem_tecnico);
    $stmt->execute();
    $sem_tecnico = $stmt->fetch();
    
    echo "<p><strong>Registros COM funcionário mas SEM técnico (Solicitado/Atualizado):</strong> " . number_format($sem_tecnico['total']) . "</p>";
    
    // 4.3 Verificar registros com ambos preenchidos (Status Solicitado/Atualizado)
    $query_ambos = "
        SELECT COUNT(*) as total
        FROM cad_registros r
        INNER JOIN cad_status s ON r.status = s.id
        WHERE r.funcionario IS NOT NULL 
        AND r.tecnico_responsavel IS NOT NULL
        AND s.nome IN ('Solicitado', 'Atualizado')
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_ambos);
    $stmt->execute();
    $ambos = $stmt->fetch();
    
    echo "<p><strong>Registros COM funcionário E técnico (Solicitado/Atualizado):</strong> " . number_format($ambos['total']) . "</p>";
    
    // 4.4 Verificar registros sem nenhum dos dois (Status Solicitado/Atualizado)
    $query_nenhum = "
        SELECT COUNT(*) as total
        FROM cad_registros r
        INNER JOIN cad_status s ON r.status = s.id
        WHERE r.funcionario IS NULL 
        AND r.tecnico_responsavel IS NULL
        AND s.nome IN ('Solicitado', 'Atualizado')
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_nenhum);
    $stmt->execute();
    $nenhum = $stmt->fetch();
    
    echo "<p><strong>Registros SEM funcionário E SEM técnico (Solicitado/Atualizado):</strong> " . number_format($nenhum['total']) . "</p>";
    
    // 5. ANÁLISE POR STATUS INDIVIDUAL
    echo "<h2>📋 5. Análise por Status Individual</h2>";
    
    $status_analisados = ['Solicitado', 'Atualizado'];
    
    foreach ($status_analisados as $status_nome) {
        echo "<h3>Status: $status_nome</h3>";
        
        // Total de registros com este status
        $query_status_total = "
            SELECT COUNT(*) as total
            FROM cad_registros r
            INNER JOIN cad_status s ON r.status = s.id
            WHERE s.nome = ?
            AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        ";
        
        $stmt = $pdo_mci->prepare($query_status_total);
        $stmt->execute([$status_nome]);
        $total_status = $stmt->fetch();
        
        echo "<p><strong>Total de registros com status '$status_nome':</strong> " . number_format($total_status['total']) . "</p>";
        
        // Distribuição de atribuições
        $query_status_distribuicao = "
            SELECT
                COUNT(CASE WHEN r.funcionario IS NOT NULL AND r.tecnico_responsavel IS NOT NULL THEN 1 END) as ambos,
                COUNT(CASE WHEN r.funcionario IS NOT NULL AND r.tecnico_responsavel IS NULL THEN 1 END) as so_funcionario,
                COUNT(CASE WHEN r.funcionario IS NULL AND r.tecnico_responsavel IS NOT NULL THEN 1 END) as so_tecnico,
                COUNT(CASE WHEN r.funcionario IS NULL AND r.tecnico_responsavel IS NULL THEN 1 END) as nenhum
            FROM cad_registros r
            INNER JOIN cad_status s ON r.status = s.id
            WHERE s.nome = ?
            AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        ";
        
        $stmt = $pdo_mci->prepare($query_status_distribuicao);
        $stmt->execute([$status_nome]);
        $distribuicao = $stmt->fetch();
        
        echo "<ul>";
        echo "<li><strong>Ambos preenchidos:</strong> " . number_format($distribuicao['ambos']) . "</li>";
        echo "<li><strong>Só funcionário:</strong> " . number_format($distribuicao['so_funcionario']) . "</li>";
        echo "<li><strong>Só técnico:</strong> " . number_format($distribuicao['so_tecnico']) . "</li>";
        echo "<li><strong>Nenhum:</strong> " . number_format($distribuicao['nenhum']) . "</li>";
        echo "</ul>";
        
        // Verificar se há inconsistências
        if ($distribuicao['so_funcionario'] > 0 || $distribuicao['so_tecnico'] > 0) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>⚠️ INCONSISTÊNCIA DETECTADA:</strong> ";
            echo "Registros com status '$status_nome' devem ter ambos os campos preenchidos!";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>✅ CONSISTENTE:</strong> ";
            echo "Todos os registros com status '$status_nome' têm ambos os campos preenchidos.";
            echo "</div>";
        }
    }
    
    // 6. CONCLUSÃO E SOLUÇÃO
    echo "<h2>💡 6. Conclusão e Solução</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🎯 Causa da Divergência (Focada em Solicitado/Atualizado):</h3>";
    
    if ($sem_funcionario['total'] > 0) {
        echo "<p><strong>✅ PRINCIPAL CAUSA IDENTIFICADA:</strong></p>";
        echo "<p>Existem <strong>" . number_format($sem_funcionario['total']) . " registros</strong> com status 'Solicitado' ou 'Atualizado' ";
        echo "que foram atribuídos a técnicos agrícolas mas <strong>NÃO foram atribuídos a funcionários de cadastro</strong>.</p>";
        echo "<p>Estes registros aparecem na contagem dos técnicos mas não na contagem do cadastro.</p>";
    }
    
    if ($sem_tecnico['total'] > 0) {
        echo "<p><strong>⚠️ CAUSA SECUNDÁRIA:</strong></p>";
        echo "<p>Existem <strong>" . number_format($sem_tecnico['total']) . " registros</strong> com status 'Solicitado' ou 'Atualizado' ";
        echo "que foram atribuídos a funcionários de cadastro mas <strong>NÃO foram atribuídos a técnicos agrícolas</strong>.</p>";
    }
    
    if ($sem_funcionario['total'] == 0 && $sem_tecnico['total'] == 0) {
        echo "<p><strong>✅ NENHUMA DIVERGÊNCIA CRÍTICA:</strong></p>";
        echo "<p>Todos os registros com status 'Solicitado' ou 'Atualizado' têm ambos os campos preenchidos corretamente.</p>";
    }
    
    echo "<p><strong>📊 Resumo da Regra de Negócio:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Status 'Pendente':</strong> Pode ter apenas funcionário (técnico será atribuído depois)</li>";
    echo "<li><strong>Status 'Solicitado':</strong> DEVE ter funcionário E técnico</li>";
    echo "<li><strong>Status 'Atualizado':</strong> DEVE ter funcionário E técnico</li>";
    echo "<li><strong>Status 'Removido':</strong> Não é considerado nas métricas</li>";
    echo "</ul>";
    echo "</div>";
    
    // 7. RECOMENDAÇÕES ESPECÍFICAS
    echo "<h2>🚀 7. Recomendações Específicas</h2>";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🔧 Ações Recomendadas:</h3>";
    echo "<ol>";
    
    if ($sem_funcionario['total'] > 0 || $sem_tecnico['total'] > 0) {
        echo "<li><strong>Correção Imediata:</strong> Atribuir funcionários aos registros sem funcionário</li>";
        echo "<li><strong>Correção Imediata:</strong> Atribuir técnicos aos registros sem técnico</li>";
        echo "<li><strong>Validação:</strong> Implementar regra que exija ambos os campos para status 'Solicitado' e 'Atualizado'</li>";
    }
    
    echo "<li><strong>Prevenção:</strong> Validar durante importação que registros com status 'Solicitado'/'Atualizado' tenham ambos os campos</li>";
    echo "<li><strong>Monitoramento:</strong> Criar relatório diário de inconsistências</li>";
    echo "<li><strong>Workflow:</strong> Implementar processo que garanta atribuição sequencial (primeiro funcionário, depois técnico)</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ Erro durante o diagnóstico:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    $logger->logFile("Erro no diagnóstico preciso de divergência: " . $e->getMessage(), 'ERROR');
}

echo "<hr>";
echo "<p><strong>Data do diagnóstico:</strong> " . date('d/m/Y H:i:s') . "</p>";
echo "<p><a href='metas.php'>← Voltar para Metas</a></p>";
?>
