<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Gerenciar <PERSON><PERSON></h1>";
echo "<p>Esta ferramenta permite remover/adicionar chaves estrangeiras para facilitar importações.</p>";
echo "<hr>";

try {
    echo "<h3>1. Verificando chaves estrangeiras atuais...</h3>";
    
    // Verificar chaves estrangeiras existentes
    $stmt = $pdo_mci->prepare("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = 'mci' 
        AND TABLE_NAME = 'cad_registros' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
        ORDER BY COLUMN_NAME
    ");
    $stmt->execute();
    $chaves_existentes = $stmt->fetchAll();
    
    if (!empty($chaves_existentes)) {
        echo "<h4>Chaves estrangeiras encontradas:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Nome da Constraint</th><th>Coluna</th><th>Referencia</th></tr>";
        foreach ($chaves_existentes as $chave) {
            echo "<tr>";
            echo "<td><code>{$chave['CONSTRAINT_NAME']}</code></td>";
            echo "<td><strong>{$chave['COLUMN_NAME']}</strong></td>";
            echo "<td>{$chave['REFERENCED_TABLE_NAME']}.{$chave['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>✅ Nenhuma chave estrangeira encontrada</p>";
    }
    
    // Verificar estrutura das colunas
    echo "<h3>2. Verificando estrutura das colunas...</h3>";
    
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field IN ('funcionario', 'tecnico_responsavel')");
    $stmt->execute();
    $colunas = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th></tr>";
    foreach ($colunas as $coluna) {
        echo "<tr>";
        echo "<td><strong>{$coluna['Field']}</strong></td>";
        echo "<td><code>{$coluna['Type']}</code></td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>{$coluna['Key']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $acao = $_POST['acao'] ?? '';
        
        if ($acao == 'remover_fks') {
            echo "<h3>3. Removendo chaves estrangeiras...</h3>";
            
            $removidas = 0;
            foreach ($chaves_existentes as $chave) {
                try {
                    $constraint_name = $chave['CONSTRAINT_NAME'];
                    $pdo_mci->exec("ALTER TABLE cad_registros DROP FOREIGN KEY $constraint_name");
                    echo "<p>✅ Removida: $constraint_name</p>";
                    $removidas++;
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Erro ao remover $constraint_name: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
            
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4 style='color: #155724;'>✅ Chaves Estrangeiras Removidas</h4>";
            echo "<p style='color: #155724;'>$removidas chaves estrangeiras foram removidas.</p>";
            echo "<p style='color: #155724;'><strong>Agora você pode:</strong></p>";
            echo "<ul style='color: #155724;'>";
            echo "<li>Importar dados sem restrições de chave estrangeira</li>";
            echo "<li>Mapear nomes para IDs manualmente</li>";
            echo "<li>Recriar as chaves estrangeiras depois</li>";
            echo "</ul>";
            echo "</div>";
            
        } elseif ($acao == 'adicionar_fks') {
            echo "<h3>3. Adicionando chaves estrangeiras...</h3>";
            
            $adicionadas = 0;
            
            // Verificar se as colunas são INT
            $funcionario_eh_int = false;
            $tecnico_eh_int = false;
            
            foreach ($colunas as $coluna) {
                if ($coluna['Field'] == 'funcionario' && strpos($coluna['Type'], 'int') !== false) {
                    $funcionario_eh_int = true;
                }
                if ($coluna['Field'] == 'tecnico_responsavel' && strpos($coluna['Type'], 'int') !== false) {
                    $tecnico_eh_int = true;
                }
            }
            
            if ($funcionario_eh_int) {
                try {
                    $pdo_mci->exec("
                        ALTER TABLE cad_registros 
                        ADD CONSTRAINT fk_registros_funcionario 
                        FOREIGN KEY (funcionario) REFERENCES sicoob_access_control.usuarios(id) 
                        ON UPDATE CASCADE ON DELETE SET NULL
                    ");
                    echo "<p>✅ Chave estrangeira funcionario adicionada</p>";
                    $adicionadas++;
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Erro FK funcionario: " . htmlspecialchars($e->getMessage()) . "</p>";
                    
                    // Verificar valores inválidos
                    $stmt = $pdo_mci->prepare("
                        SELECT DISTINCT r.funcionario 
                        FROM cad_registros r 
                        LEFT JOIN sicoob_access_control.usuarios u ON r.funcionario = u.id 
                        WHERE r.funcionario IS NOT NULL AND u.id IS NULL
                        LIMIT 5
                    ");
                    $stmt->execute();
                    $invalidos = $stmt->fetchAll();
                    
                    if (!empty($invalidos)) {
                        echo "<p style='color: orange;'>⚠️ Valores inválidos encontrados em funcionario:</p>";
                        foreach ($invalidos as $inv) {
                            echo "<p>- ID: " . htmlspecialchars($inv['funcionario']) . "</p>";
                        }
                    }
                }
            } else {
                echo "<p style='color: orange;'>⚠️ Coluna funcionario não é INT, FK não adicionada</p>";
            }
            
            if ($tecnico_eh_int) {
                try {
                    $pdo_mci->exec("
                        ALTER TABLE cad_registros 
                        ADD CONSTRAINT fk_registros_tecnico 
                        FOREIGN KEY (tecnico_responsavel) REFERENCES sicoob_access_control.usuarios(id) 
                        ON UPDATE CASCADE ON DELETE SET NULL
                    ");
                    echo "<p>✅ Chave estrangeira tecnico_responsavel adicionada</p>";
                    $adicionadas++;
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Erro FK tecnico: " . htmlspecialchars($e->getMessage()) . "</p>";
                    
                    // Verificar valores inválidos
                    $stmt = $pdo_mci->prepare("
                        SELECT DISTINCT r.tecnico_responsavel 
                        FROM cad_registros r 
                        LEFT JOIN sicoob_access_control.usuarios u ON r.tecnico_responsavel = u.id 
                        WHERE r.tecnico_responsavel IS NOT NULL AND u.id IS NULL
                        LIMIT 5
                    ");
                    $stmt->execute();
                    $invalidos = $stmt->fetchAll();
                    
                    if (!empty($invalidos)) {
                        echo "<p style='color: orange;'>⚠️ Valores inválidos encontrados em tecnico_responsavel:</p>";
                        foreach ($invalidos as $inv) {
                            echo "<p>- ID: " . htmlspecialchars($inv['tecnico_responsavel']) . "</p>";
                        }
                    }
                }
            } else {
                echo "<p style='color: orange;'>⚠️ Coluna tecnico_responsavel não é INT, FK não adicionada</p>";
            }
            
            if ($adicionadas > 0) {
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4 style='color: #155724;'>✅ Chaves Estrangeiras Adicionadas</h4>";
                echo "<p style='color: #155724;'>$adicionadas chaves estrangeiras foram adicionadas.</p>";
                echo "</div>";
            }
            
        } elseif ($acao == 'limpar_valores_invalidos') {
            echo "<h3>3. Limpando valores inválidos...</h3>";
            
            $limpezas = 0;
            
            // Limpar funcionarios inválidos
            try {
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros r 
                    LEFT JOIN sicoob_access_control.usuarios u ON r.funcionario = u.id 
                    SET r.funcionario = NULL 
                    WHERE r.funcionario IS NOT NULL AND u.id IS NULL
                ");
                $stmt->execute();
                $affected = $stmt->rowCount();
                echo "<p>✅ $affected registros de funcionario inválidos limpos</p>";
                $limpezas += $affected;
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro ao limpar funcionarios: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
            // Limpar tecnicos inválidos
            try {
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros r 
                    LEFT JOIN sicoob_access_control.usuarios u ON r.tecnico_responsavel = u.id 
                    SET r.tecnico_responsavel = NULL 
                    WHERE r.tecnico_responsavel IS NOT NULL AND u.id IS NULL
                ");
                $stmt->execute();
                $affected = $stmt->rowCount();
                echo "<p>✅ $affected registros de tecnico inválidos limpos</p>";
                $limpezas += $affected;
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro ao limpar tecnicos: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4 style='color: #155724;'>✅ Valores Inválidos Limpos</h4>";
            echo "<p style='color: #155724;'>$limpezas registros foram limpos.</p>";
            echo "<p style='color: #155724;'>Agora você pode tentar adicionar as chaves estrangeiras novamente.</p>";
            echo "</div>";
        }
    } else {
        // Mostrar opções
        echo "<h3>3. Opções Disponíveis</h3>";
        
        echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";
        
        // Opção 1: Remover FKs
        if (!empty($chaves_existentes)) {
            echo "<div style='flex: 1; background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<h5>🔓 Remover Chaves Estrangeiras</h5>";
            echo "<p>Remove as chaves estrangeiras para permitir importação livre.</p>";
            echo "<p><strong>Use quando:</strong> Precisa importar dados sem validação</p>";
            echo "<form method='POST'>";
            echo "<input type='hidden' name='acao' value='remover_fks'>";
            echo "<button type='submit' style='background-color: #ffc107; color: black; padding: 10px 15px; border: none; border-radius: 5px;'>Remover FKs</button>";
            echo "</form>";
            echo "</div>";
        }
        
        // Opção 2: Adicionar FKs
        echo "<div style='flex: 1; background-color: #d1ecf1; padding: 15px; border-radius: 5px;'>";
        echo "<h5>🔒 Adicionar Chaves Estrangeiras</h5>";
        echo "<p>Adiciona chaves estrangeiras para garantir integridade.</p>";
        echo "<p><strong>Use quando:</strong> Dados estão corretos e mapeados</p>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='acao' value='adicionar_fks'>";
        echo "<button type='submit' style='background-color: #17a2b8; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Adicionar FKs</button>";
        echo "</form>";
        echo "</div>";
        
        // Opção 3: Limpar valores inválidos
        echo "<div style='flex: 1; background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h5>🧹 Limpar Valores Inválidos</h5>";
        echo "<p>Remove IDs que não existem na tabela de usuários.</p>";
        echo "<p><strong>Use quando:</strong> Há valores inválidos impedindo FKs</p>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='acao' value='limpar_valores_invalidos'>";
        echo "<button type='submit' style='background-color: #dc3545; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Limpar Inválidos</button>";
        echo "</form>";
        echo "</div>";
        
        echo "</div>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px;'>";
        echo "<h5>💡 Fluxo Recomendado para Importação:</h5>";
        echo "<ol>";
        echo "<li><strong>Remover FKs</strong> - Para permitir importação livre</li>";
        echo "<li><strong>Importar dados</strong> - Via página de importação</li>";
        echo "<li><strong>Mapear usuários</strong> - Via ferramenta de mapeamento gradual</li>";
        echo "<li><strong>Limpar inválidos</strong> - Se necessário</li>";
        echo "<li><strong>Adicionar FKs</strong> - Para garantir integridade</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<p><a href='importar.php'>Importar dados</a> | <a href='mapear_usuarios_gradual.php'>Mapear usuários</a> | <a href='gerenciar.php'>Ver registros</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante o gerenciamento</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

@media (max-width: 768px) {
    div[style*="display: flex"] {
        flex-direction: column !important;
    }
}
</style>
