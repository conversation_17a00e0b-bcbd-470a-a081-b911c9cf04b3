<?php
require_once '../auth_check.php';
require_once '../classes/Logger.php';

$logger = new MciLogger();

echo "<h2>🔍 Debug - Filtros da API da Intranet</h2>";

// Email do Maycon
$email_maycon = '<EMAIL>';
echo "<p><strong>Investigando:</strong> " . htmlspecialchars($email_maycon) . "</p>";

// Carregar API sem filtros
try {
    require_once '../cadastro/config_api.php';
    
    // Fazer chamada direta à API SEM os filtros
    $apiFields = [
        'api_user' => API_INTRANET_USER,
        'api_token' => API_INTRANET_TOKEN,
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];
    
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => API_INTRANET_URL,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
        CURLOPT_TIMEOUT => API_INTRANET_TIMEOUT,
        CURLOPT_USERAGENT => 'MCI Dashboard/1.0'
    ));
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $error = curl_error($curl);
    curl_close($curl);
    
    if ($error || $httpCode !== 200) {
        echo "<p style='color: red;'>❌ <strong>Erro na API:</strong> $error (HTTP $httpCode)</p>";
        exit;
    }
    
    $usuarios_brutos = json_decode($response, true);
    
    if (!is_array($usuarios_brutos)) {
        echo "<p style='color: red;'>❌ <strong>Resposta inválida da API</strong></p>";
        exit;
    }
    
    echo "<p>✅ <strong>API respondeu com " . count($usuarios_brutos) . " usuários (SEM filtros)</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// Buscar Maycon nos dados brutos
echo "<h3>🔍 Busca nos Dados Brutos (SEM filtros)</h3>";

$maycon_bruto = null;
$email_normalizado = strtolower(trim($email_maycon));

foreach ($usuarios_brutos as $usuario) {
    if (isset($usuario['email']) && strtolower(trim($usuario['email'])) === $email_normalizado) {
        $maycon_bruto = $usuario;
        break;
    }
}

if ($maycon_bruto) {
    echo "<p style='color: green;'>✅ <strong>Maycon ENCONTRADO nos dados brutos!</strong></p>";
    
    echo "<h4>📊 Dados Completos do Maycon:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    
    foreach ($maycon_bruto as $campo => $valor) {
        $destaque = '';
        if (in_array($campo, ['status', 'bloqueado', 'email', 'nome', 'foto'])) {
            $destaque = 'style="background-color: #fff3cd;"';
        }
        
        echo "<tr $destaque>";
        echo "<td><strong>" . htmlspecialchars($campo) . "</strong></td>";
        echo "<td>" . htmlspecialchars($valor ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar filtros
    echo "<h4>🔍 Verificação dos Filtros:</h4>";
    
    $status = $maycon_bruto['status'] ?? 'N/A';
    $bloqueado = $maycon_bruto['bloqueado'] ?? 'N/A';
    $foto = $maycon_bruto['foto'] ?? null;
    
    echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 10px 0;'>";
    echo "<h5>📋 Análise dos Filtros:</h5>";
    
    // Filtro 1: Status
    if (isset($maycon_bruto['status']) && $maycon_bruto['status'] == 1) {
        echo "<p style='color: green;'>✅ <strong>Status:</strong> " . $status . " (ATIVO - PASSA no filtro)</p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>Status:</strong> " . $status . " (INATIVO - NÃO PASSA no filtro)</p>";
    }
    
    // Filtro 2: Bloqueado
    if (isset($maycon_bruto['bloqueado']) && $maycon_bruto['bloqueado'] == 0) {
        echo "<p style='color: green;'>✅ <strong>Bloqueado:</strong> " . $bloqueado . " (NÃO BLOQUEADO - PASSA no filtro)</p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>Bloqueado:</strong> " . $bloqueado . " (BLOQUEADO - NÃO PASSA no filtro)</p>";
    }
    
    // Verificar se passaria nos filtros
    $passaria_filtros = (isset($maycon_bruto['status']) && $maycon_bruto['status'] == 1) && 
                       (isset($maycon_bruto['bloqueado']) && $maycon_bruto['bloqueado'] == 0);
    
    if ($passaria_filtros) {
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ <strong>RESULTADO: Maycon DEVERIA aparecer no dashboard!</strong></p>";
        
        // Verificar foto
        if (!empty($foto)) {
            $foto_url = API_INTRANET_FOTO_URL . $foto;
            echo "<p><strong>URL da foto:</strong> " . htmlspecialchars($foto_url) . "</p>";
            echo "<img src='" . htmlspecialchars($foto_url) . "' style='width: 100px; height: 100px; border-radius: 50%; object-fit: cover; border: 3px solid #28a745;' onerror=\"this.style.border='3px solid #dc3545'; this.alt='❌ Erro ao carregar';\">";
        } else {
            echo "<p style='color: orange;'>⚠️ <strong>Foto:</strong> Campo 'foto' vazio - usará placeholder</p>";
        }
        
    } else {
        echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ <strong>RESULTADO: Maycon está sendo FILTRADO pela API!</strong></p>";
    }
    
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>❌ <strong>Maycon NÃO encontrado nem nos dados brutos!</strong></p>";
    
    // Buscar por nome
    echo "<h4>🔍 Busca por Nome:</h4>";
    $encontrados_por_nome = [];
    
    foreach ($usuarios_brutos as $usuario) {
        if (isset($usuario['nome']) && stripos($usuario['nome'], 'maycon') !== false) {
            $encontrados_por_nome[] = $usuario;
        }
    }
    
    if (!empty($encontrados_por_nome)) {
        echo "<p style='color: orange;'>⚠️ <strong>Encontrados " . count($encontrados_por_nome) . " usuários com nome 'Maycon':</strong></p>";
        
        foreach ($encontrados_por_nome as $index => $usuario) {
            echo "<div style='border: 1px solid #ffc107; padding: 10px; margin: 5px 0; background: #fff3cd;'>";
            echo "<h5>Usuário " . ($index + 1) . ":</h5>";
            echo "<p><strong>Nome:</strong> " . htmlspecialchars($usuario['nome'] ?? 'N/A') . "</p>";
            echo "<p><strong>Email:</strong> " . htmlspecialchars($usuario['email'] ?? 'N/A') . "</p>";
            echo "<p><strong>Status:</strong> " . htmlspecialchars($usuario['status'] ?? 'N/A') . "</p>";
            echo "<p><strong>Bloqueado:</strong> " . htmlspecialchars($usuario['bloqueado'] ?? 'N/A') . "</p>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>❌ <strong>Nenhum usuário com nome 'Maycon' encontrado na API!</strong></p>";
    }
}

// Comparar com dados processados
echo "<h3>⚙️ Comparação com Dados Processados</h3>";

$intranetAPI = new IntranetAPI($logger);
$usuarios_processados = $intranetAPI->buscarUsuarios();
$mapa_processado = $intranetAPI->criarMapaUsuariosPorEmail();

echo "<p><strong>Usuários após filtros:</strong> " . count($usuarios_processados) . "</p>";
echo "<p><strong>Usuários no mapa por email:</strong> " . count($mapa_processado) . "</p>";

if (isset($mapa_processado[$email_normalizado])) {
    echo "<p style='color: green;'>✅ <strong>Maycon ENCONTRADO no mapa processado!</strong></p>";
    $maycon_processado = $mapa_processado[$email_normalizado];
    echo "<p><strong>Foto URL:</strong> " . htmlspecialchars($maycon_processado['foto_url'] ?? 'NULL') . "</p>";
} else {
    echo "<p style='color: red;'>❌ <strong>Maycon NÃO encontrado no mapa processado!</strong></p>";
    
    // Listar primeiros 10 emails do mapa para comparação
    echo "<h4>📋 Primeiros 10 emails no mapa processado:</h4>";
    $count = 0;
    foreach ($mapa_processado as $email => $dados) {
        if ($count >= 10) break;
        echo "<p>" . htmlspecialchars($email) . " - " . htmlspecialchars($dados['nome'] ?? 'N/A') . "</p>";
        $count++;
    }
}

echo "<hr>";
echo "<p><a href='dashboard.php'>🚀 Dashboard</a> | <a href='debug_maycon.php'>🎯 Debug Maycon</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
