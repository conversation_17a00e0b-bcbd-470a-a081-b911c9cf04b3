[2025-06-26 08:35:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 08:35:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 08:54:36] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: JOSE GERALDO MENDES CAMPOS (CPF/CNPJ: 57019940668) | Justificativa: Laudo de vistoria apontou atividade divergente, constante no CNAE cadastrado na IE.
[2025-06-26 09:02:45] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40009 | Funcionário: Milena <PERSON> | Solicitação Laudo: 25/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 25/06/2025
[2025-06-26 09:08:28] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40017 | Funcionário: <PERSON><PERSON> | Solicitação Laudo: 27/03/2025 | Técnico: Sidnei Gama | SISBR: 26/06/2025
[2025-06-26 09:12:15] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39633 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 06/02/2025 | Técnico: Sidnei Gama | SISBR: 26/06/2025
[2025-06-26 09:16:42] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40010 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 31/03/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 09:18:57] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40047 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 31/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 12/05/2025
[2025-06-26 09:20:56] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: DJALMA SANTOS NEIVA (CPF/CNPJ: 55754325720) | Justificativa: ÁREA FOI VENDIDA, CONFORME DESCRITO NO PARECER TÉCNICO.

[2025-06-26 09:23:42] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40055 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 03/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 09:24:21] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40069 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 03/04/2025 | Técnico: Felipe Jorge Dutra
[2025-06-26 09:27:17] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40072 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 03/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 13/06/2025
[2025-06-26 09:31:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39894 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 14/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 17/06/2025
[2025-06-26 09:33:38] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39895 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 14/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 16/06/2025
[2025-06-26 09:36:21] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40076 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 03/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 09:37:33] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39927 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 07/03/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 03/06/2025
[2025-06-26 09:38:37] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40077 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 10/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 14/05/2025
[2025-06-26 09:39:07] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40098 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 31/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 22/05/2025
[2025-06-26 09:39:55] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39948 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 14/03/2025 | Técnico: Felipe Jorge Dutra | SISBR: 09/04/2025
[2025-06-26 09:41:25] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39952 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 21/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 11/04/2025
[2025-06-26 09:42:06] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40110 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 09:42:58] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40113 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 16/05/2025
[2025-06-26 09:43:32] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40127 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 03/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 08/04/2025
[2025-06-26 09:50:18] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39630 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 31/01/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 10:00:25] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39642 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 13/01/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 10:00:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:00:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:00:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:00:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:03:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:03:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:03:19] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40126 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 10/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 10:03:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:03:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:04:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:04:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:04:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:04:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:04:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:04:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:05:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:05:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:07:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:07:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:08:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:08:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:08:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:08:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:09:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:09:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:09:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:09:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:10:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:10:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:12:41] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39719 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 24/02/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 10:12:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:12:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:13:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:13:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:13:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:13:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:13:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:13:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:13:56] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40128 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 10/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 10:14:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:14:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:14:32] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40146 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 31/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 20/05/2025
[2025-06-26 10:14:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:14:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:17:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:17:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:18:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:18:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:18:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:18:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:18:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:18:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:19:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:19:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:19:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:19:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:22:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:22:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:22:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:22:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:23:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:23:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:23:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:23:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:24:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:24:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:24:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:24:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:27:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:27:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:27:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:27:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:28:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:28:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:28:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:28:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:28:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:28:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:29:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:29:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:31:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:31:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:32:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:32:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:32:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:32:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:33:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:33:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:33:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:33:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:34:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:34:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:36:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:36:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:37:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:37:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:37:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:37:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:37:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:37:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:38:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:38:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:38:29] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39716 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 24/02/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 10:38:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:38:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:41:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:41:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:42:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:42:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:42:14] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40148 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 10:42:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:42:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:42:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:42:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:43:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:43:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:43:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:43:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:46:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:46:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:46:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:46:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:47:01] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40156 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 10:47:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:47:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:47:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:47:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:48:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:48:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:48:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:48:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:49:32] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40157 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 10:51:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:51:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:51:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:51:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:51:49] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39727 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/02/2025 | Técnico: Sidnei Gama | SISBR: 26/06/2025
[2025-06-26 10:52:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:52:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:52:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:52:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:52:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:52:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:53:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:53:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:53:38] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40158 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 31/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 21/05/2025
[2025-06-26 10:53:50] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39735 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/02/2024 | Técnico: Sidnei Gama
[2025-06-26 10:54:30] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39735 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/02/2024 | Técnico: Sidnei Gama
[2025-06-26 10:55:10] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39726 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/02/2025 | Técnico: Felipe Jorge Dutra
[2025-06-26 10:55:56] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39629 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 31/01/2025 | Técnico: Felipe Jorge Dutra
[2025-06-26 10:55:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:55:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:56:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:56:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:57:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:57:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:57:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:57:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:57:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:57:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 10:58:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 10:58:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:00:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:00:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:01:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:01:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:01:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:01:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:01:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:01:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:02:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:02:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:03:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:03:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:04:26] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40164 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 11:05:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:05:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:05:43] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39735 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/02/2024 | Técnico: Sidnei Gama | SISBR: 26/06/2025
[2025-06-26 11:06:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:06:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:06:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:06:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:06:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:06:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:07:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:07:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:07:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:07:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:09:08] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: WANDA DE MOURA VITERBO (CPF/CNPJ: 77115686653) | Justificativa: ASSOCIADA FALECEU.
[2025-06-26 11:10:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:10:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:10:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:10:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:11:09] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40470 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 12/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 11:11:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:11:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:11:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:11:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:12:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:12:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:12:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:12:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:14:14] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40479 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 18/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 11:15:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:15:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:15:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:15:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:16:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:16:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:16:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:16:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:16:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:16:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:17:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:17:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:17:32] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40481 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 23/06/2025 | Técnico: Felipe Jorge Dutra
[2025-06-26 11:18:04] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40492 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 23/06/2025 | Técnico: Leonardo Lopes De Oliveira
[2025-06-26 11:20:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:20:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:20:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:20:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:21:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:21:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:21:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:21:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:21:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:21:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:22:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:22:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:22:49] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40514 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 23/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 11:24:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:24:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:25:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:25:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:25:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:25:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:25:56] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40535 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 12/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 17/06/2025
[2025-06-26 11:26:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:26:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:26:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:26:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:26:53] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39737 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/02/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 11:27:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:27:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:27:13] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40531 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 21/05/2025
[2025-06-26 11:28:26] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40533 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 23/06/2025 | Técnico: Felipe Jorge Dutra
[2025-06-26 11:29:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:29:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:30:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:30:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:30:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:30:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:30:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:30:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:31:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:31:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:31:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:31:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:34:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:34:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:35:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:35:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:35:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:35:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:35:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:35:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:36:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:36:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:36:24] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39738 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/02/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 11:36:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:36:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:37:29] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40662 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 28/04/2025 | Técnico: Leonardo Lopes De Oliveira
[2025-06-26 11:39:10] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: CLEOMAR ANDRE PRATA BERTOLASSE (CPF/CNPJ: 12307014608) | Justificativa: FLUXO EM ATUALIZAÇÃO PELO PA.
[2025-06-26 11:39:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:39:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:39:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:39:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:39:57] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40724 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Sidnei Gama | SISBR: 26/06/2025
[2025-06-26 11:40:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:40:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:40:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:40:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:40:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:40:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:41:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:41:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:42:40] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40062 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Sidnei Gama | SISBR: 04/06/2025
[2025-06-26 11:43:22] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40065 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 07/05/2025
[2025-06-26 11:44:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:44:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:44:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:44:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:44:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40090 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 04/04/2025 | Técnico: Sidnei Gama | SISBR: 17/06/2025
[2025-06-26 11:45:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:45:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:45:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:45:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:45:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:45:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:46:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:46:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:48:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:48:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:49:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:49:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:49:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:49:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:50:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:50:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:50:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:50:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:51:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:51:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:53:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:53:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:54:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:54:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:54:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:54:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:54:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:54:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:55:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:55:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:55:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:55:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:58:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:58:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:59:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:59:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:59:27] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40835 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 12/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 18/06/2025
[2025-06-26 11:59:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:59:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:59:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 11:59:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 11:59:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40836 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 12/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 18/06/2025
[2025-06-26 12:00:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:00:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:00:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:00:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:02:44] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40737 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 12/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 12:03:11] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40660 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 23/06/2025 | Técnico: Leonardo Lopes De Oliveira
[2025-06-26 12:03:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:03:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:03:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:03:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:04:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:04:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:04:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:04:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:05:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:05:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:05:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:05:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:08:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:08:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:08:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:08:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:09:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:09:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:09:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:09:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:09:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:09:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:10:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:10:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:12:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:12:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:13:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:13:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:14:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:14:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:14:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:14:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:14:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:14:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:15:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:15:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:16:52] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40728 | Funcionário: Jussara Cristina Queiros Soares | Solicitação Laudo: 23/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 12:17:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:17:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:18:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:18:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:18:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:18:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:18:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:18:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:19:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:19:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:19:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:19:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:22:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:22:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:23:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:23:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:23:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:23:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:23:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:23:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:24:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:24:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:24:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:24:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:27:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:27:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:27:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:27:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:28:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:28:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:28:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:28:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:29:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:29:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:29:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:29:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:30:24] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40758 | Funcionário: Jussara Cristina Queiros Soares | Solicitação Laudo: 23/06/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 12:32:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:32:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:32:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:32:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:33:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:33:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:33:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:33:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:33:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:33:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:34:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:34:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:36:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:36:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:37:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:37:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:38:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:38:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:38:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:38:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:38:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:38:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:39:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:39:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:41:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:41:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:42:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:42:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:42:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:42:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:42:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:42:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:43:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:43:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:44:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:44:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:46:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:46:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:47:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:47:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:47:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:47:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:47:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:47:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:48:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:48:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:48:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:48:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:51:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:51:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:51:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:51:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:52:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:52:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:52:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:52:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:53:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:53:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:53:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:53:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:56:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:56:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:56:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:56:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:57:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:57:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:57:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:57:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:57:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:57:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 12:58:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 12:58:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:01:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:01:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:01:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:01:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:02:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:02:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:02:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:02:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:02:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:02:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:03:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:03:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:05:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:05:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:06:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:06:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:06:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:06:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:07:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:07:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:07:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:07:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:08:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:08:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:10:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:10:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:11:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:11:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:11:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:11:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:11:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:11:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:12:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:12:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:12:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:12:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:15:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:15:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:15:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:15:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:16:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:16:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:16:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:16:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:17:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:17:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:17:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:17:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:20:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:20:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:20:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:20:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:21:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:21:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:21:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:21:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:21:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:21:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:22:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:22:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:25:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:25:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:25:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:25:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:26:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:26:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:26:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:26:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:26:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:26:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:27:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:27:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:29:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:29:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:30:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:30:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:30:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:30:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:31:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:31:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:31:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:31:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:32:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:32:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:34:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:34:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:35:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:35:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:35:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:35:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:35:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:35:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:36:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:36:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:36:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:36:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:39:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:39:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:39:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:39:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:40:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:40:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:40:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:40:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:41:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:41:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:41:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:41:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:44:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:44:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:44:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:44:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:45:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:45:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:45:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:45:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:45:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:45:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:46:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:46:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:49:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:49:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:49:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:49:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:50:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:50:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:50:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:50:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:50:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:50:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:51:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:51:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:53:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:53:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:54:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:54:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:54:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40604 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 13:54:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:54:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:55:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:55:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:55:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:55:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:56:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:56:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:58:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:58:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:59:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:59:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:59:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:59:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 13:59:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 13:59:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:00:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:00:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:00:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:00:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:01:39] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40482 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 17/04/2025 | Técnico: Sidnei Gama | SISBR: 26/06/2025
[2025-06-26 14:03:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:03:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:04:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:04:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:04:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:04:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:04:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:04:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:05:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:05:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:05:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:05:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:08:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:08:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:08:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:08:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:09:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:09:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:09:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:09:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:10:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:10:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:10:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:10:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:13:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:13:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:13:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:13:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:14:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:14:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:14:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:14:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:14:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:14:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:15:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:15:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:17:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:17:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:18:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:18:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:18:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:18:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:19:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:19:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:19:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:19:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:20:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:20:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:22:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:22:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:23:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:23:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:23:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:23:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:23:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:23:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:24:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:24:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:24:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:24:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:27:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:27:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:28:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:28:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:28:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:28:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:28:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:28:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:29:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:29:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:29:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:29:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:32:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:32:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:32:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:32:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:33:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:33:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:33:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:33:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:34:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:34:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:34:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:34:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:37:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:37:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:37:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:37:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:38:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:38:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:38:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:38:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:38:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:38:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:38:55] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40155 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 10/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 14:39:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:39:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:41:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:41:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:42:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:42:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:42:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:42:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:43:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:43:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:43:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:43:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:44:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:44:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:46:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:46:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:47:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:47:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:47:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:47:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:47:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:47:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:48:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:48:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:48:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:48:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:51:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:51:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:52:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:52:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:52:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:52:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:52:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:52:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:53:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:53:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:53:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:53:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:56:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:56:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:56:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:56:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:57:14] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40056 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 03/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 26/06/2025
[2025-06-26 14:57:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:57:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:57:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:57:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:58:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:58:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 14:58:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 14:58:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:01:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:01:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:01:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:01:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:02:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:02:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:02:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:02:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:02:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:02:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:03:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:03:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:06:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:06:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:06:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:06:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:07:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:07:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:07:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:07:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:07:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:07:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:08:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:08:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:10:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:10:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:11:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:11:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:11:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:11:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:12:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:12:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:12:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:12:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:13:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:13:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:15:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:15:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:16:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:16:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:16:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:16:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:16:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:16:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:17:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:17:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:17:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:17:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:20:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:20:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:21:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:21:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:21:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:21:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:21:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:21:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:22:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:22:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:22:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:22:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:25:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:25:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:25:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:25:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:26:12] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40149 | Funcionário: Milena Rodrigues Alves | Solicitação Laudo: 31/03/2025 | Técnico: Maycon de Souza Dias | SISBR: 26/06/2025
[2025-06-26 15:26:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:26:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:26:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:26:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:27:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:27:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:27:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:27:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:30:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:30:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:30:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:30:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:31:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:31:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:31:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:31:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:31:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:31:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:32:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:32:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:34:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:34:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:35:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:35:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:35:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:35:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:36:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:36:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:36:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:36:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:37:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:37:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:39:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:39:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:40:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:40:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:40:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:40:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:40:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:40:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:41:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:41:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:41:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:41:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:44:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:44:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:45:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:45:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:45:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:45:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:45:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:45:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:46:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:46:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:46:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:46:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:49:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:49:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:49:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:49:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:50:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:50:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:50:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:50:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:51:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:51:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:51:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:51:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:54:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:54:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:54:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:54:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:55:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:55:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:55:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:55:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:55:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:55:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:56:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:56:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:58:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:58:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:59:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:59:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 15:59:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 15:59:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:00:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:00:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:00:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:00:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:01:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:01:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:03:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:03:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:04:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:04:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:04:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:04:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:04:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:04:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:05:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:05:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:05:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:05:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:08:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:08:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:09:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:09:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:09:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:09:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:09:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:09:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:10:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:10:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:10:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:10:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:13:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:13:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:13:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:13:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:14:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:14:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:14:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:14:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:15:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:15:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:15:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:15:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:18:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:18:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:18:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:18:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:19:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:19:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:19:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:19:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:19:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:19:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:20:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:20:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:22:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:22:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:23:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:23:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:24:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:24:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:24:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:24:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:24:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:24:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:25:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:25:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:27:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:27:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:28:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:28:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:28:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:28:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:28:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:28:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:29:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:29:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:30:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:30:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:32:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:32:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:33:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:33:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:33:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:33:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:33:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:33:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:34:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:34:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:34:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:34:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:37:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:37:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:37:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:37:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:38:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:38:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:38:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:38:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:39:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:39:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:39:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:39:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:42:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:42:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:42:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:42:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:43:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:43:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:43:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:43:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:43:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:43:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:44:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:44:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:47:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:47:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:47:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:47:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:48:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:48:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:48:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:48:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:48:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:48:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:49:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:49:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:51:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:51:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:52:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:52:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:52:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:52:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:53:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:53:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:53:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:53:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:54:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:54:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:56:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:56:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:57:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:57:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:57:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:57:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:57:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:57:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:58:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:58:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 16:58:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 16:58:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:01:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:01:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:01:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:01:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:02:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:02:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:02:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:02:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:02:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:02:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:03:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:03:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:03:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:03:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:03:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:03:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:03:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:03:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:04:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:04:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:04:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:04:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:05:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:05:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:05:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:05:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:06:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:06:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:06:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:06:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:06:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:06:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:07:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:07:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:07:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:07:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:07:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:07:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:07:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:07:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:07:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:07:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:08:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:08:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:08:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:08:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:08:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:08:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:09:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:09:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:09:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:09:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:10:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:10:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:10:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:10:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:11:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:11:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:11:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:11:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:11:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:11:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:11:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:11:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:12:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:12:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:12:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:12:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:12:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:12:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:12:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:12:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:13:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:13:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:13:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:13:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:13:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:13:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:14:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:14:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:14:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:14:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:15:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:15:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:15:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:15:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:15:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:15:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:16:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:16:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:16:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:16:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:16:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:16:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:16:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:16:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:17:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:17:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:17:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:17:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:17:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:17:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:18:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:18:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:18:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:18:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:18:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:18:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:19:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:19:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:19:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:19:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:20:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:20:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:20:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:20:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:20:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:20:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:21:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:21:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:21:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:21:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:21:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:21:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:21:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:21:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:21:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:21:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:22:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:22:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:22:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:22:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:22:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:22:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:22:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:22:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:23:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:23:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:24:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:24:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:24:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:24:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:25:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:25:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:25:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:25:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:25:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:25:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:25:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:25:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:26:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:26:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:26:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:26:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:26:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:26:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:26:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:26:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:27:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:27:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:27:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:27:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:27:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:27:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:27:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:27:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:28:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:28:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:29:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:29:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:29:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:29:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:30:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:30:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:30:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:30:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:30:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:30:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:30:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:30:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:31:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:31:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:31:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:31:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:31:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:31:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:31:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:31:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:31:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:31:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:32:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:32:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:32:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:32:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:32:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:32:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:33:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:33:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:33:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:33:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:34:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:34:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:35:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:35:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:35:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:35:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:35:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:35:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:35:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:35:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:36:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:36:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:36:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:36:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:36:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:36:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:36:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:36:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:36:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:36:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:37:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:37:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:37:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:37:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:37:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:37:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:38:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:38:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:38:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:38:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:39:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:39:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:39:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:39:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:40:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:40:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:40:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:40:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:40:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:40:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:40:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:40:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:41:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:41:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:41:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:41:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:41:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:41:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:41:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:41:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:42:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:42:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:42:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:42:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:42:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:42:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:43:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:43:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:43:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:43:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:44:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:44:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:44:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:44:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:44:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:44:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:45:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:45:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:45:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:45:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:45:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:45:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:45:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:45:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:46:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:46:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:46:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:46:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:46:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:46:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:46:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:46:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:47:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:47:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:47:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:47:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:48:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:48:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:48:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:48:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:49:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:49:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:49:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:49:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:49:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:49:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:49:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:49:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:50:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:50:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:50:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:50:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:50:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:50:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:51:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:51:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:51:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:51:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:51:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:51:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:51:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:51:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:52:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:52:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:52:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:52:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:53:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:53:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:53:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:53:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:54:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:54:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:54:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:54:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:54:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:54:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:54:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:54:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:55:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:55:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:55:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:55:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:55:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:55:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:55:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:55:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:55:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:55:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:56:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:56:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:56:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:56:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:57:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:57:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:57:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:57:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:58:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:58:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:58:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:58:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:58:55] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40743 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 20/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 12/05/2025
[2025-06-26 17:59:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:59:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:59:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:59:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:59:25] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40782 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 20/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 24/06/2025
[2025-06-26 17:59:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:59:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:59:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 17:59:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 17:59:54] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40647 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 20/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 18:00:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:00:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:00:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:00:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:00:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:00:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:00:34] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40580 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 20/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 18:00:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:00:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:00:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:00:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:01:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:01:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:01:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:01:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:02:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:02:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:02:23] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40541 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 20/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 26/06/2025
[2025-06-26 18:02:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:02:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:03:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:03:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:03:28] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40851 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 13/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 18:03:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:03:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:04:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:04:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:04:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:04:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:05:00] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40853 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 13/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 25/06/2025
[2025-06-26 18:05:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:05:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:05:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:05:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:06:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:06:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:07:36] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40748 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 13/06/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 26/06/2025
[2025-06-26 18:08:18] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40746 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 13/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 25/06/2025
[2025-06-26 18:08:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:08:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:09:01] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40715 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 13/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 25/06/2025
[2025-06-26 18:09:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:09:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:09:40] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40423 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 30/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 12/06/2025
[2025-06-26 18:09:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:09:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:09:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:09:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:10:20] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40409 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 30/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 06/06/2025
[2025-06-26 18:10:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:10:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:10:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:10:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:13:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:13:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:14:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:14:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:14:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:14:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:14:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:14:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:15:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:15:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:15:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:15:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:18:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:18:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:18:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:18:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:19:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:19:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:19:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:19:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:20:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:20:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:20:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:20:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:23:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:23:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:23:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:23:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:24:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:24:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:24:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:24:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:24:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:24:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:25:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:25:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:27:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:27:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:28:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:28:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:28:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:28:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:29:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:29:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:29:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:29:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:30:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:30:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:32:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:32:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:33:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:33:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:33:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:33:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:33:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:33:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:34:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:34:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:34:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:34:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:37:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:37:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:38:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:38:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:38:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:38:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:38:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:38:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:39:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:39:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:39:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:39:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:42:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:42:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:42:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:42:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:43:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:43:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:43:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:43:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:44:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:44:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:44:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:44:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:47:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:47:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:47:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:47:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:48:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:48:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:48:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:48:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:48:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:48:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:49:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:49:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:51:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:51:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:52:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:52:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:52:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:52:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:53:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:53:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:53:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:53:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:54:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:54:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:56:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:56:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:57:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:57:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:57:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:57:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:57:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:57:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:58:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:58:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 18:58:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 18:58:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:01:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:01:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:02:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:02:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:02:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:02:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:02:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:02:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:03:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:03:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:03:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:03:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:06:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:06:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:06:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:06:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:07:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:07:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:07:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:07:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:08:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:08:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:08:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:08:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:11:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:11:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:11:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:11:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:12:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:12:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:12:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:12:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:12:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:12:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:13:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:13:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:15:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:15:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:16:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:16:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:17:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:17:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:17:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:17:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:17:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:17:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:18:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:18:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:20:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:20:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:21:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:21:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:21:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:21:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:21:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:21:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:22:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:22:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:22:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:22:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:25:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:25:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:26:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:26:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:26:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:26:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:26:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:26:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:27:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:27:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:27:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:27:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:30:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:30:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:30:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:30:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:31:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:31:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:31:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:31:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:32:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:32:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:32:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:32:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:35:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:35:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:35:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:35:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:36:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:36:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:36:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:36:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:36:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:36:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:37:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:37:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:39:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:39:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:40:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:40:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:41:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:41:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:41:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:41:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:41:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:41:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:42:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:42:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:44:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:44:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:45:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:45:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:45:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:45:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:45:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:45:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:46:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:46:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:47:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:47:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:49:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:49:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:50:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:50:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:50:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:50:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:50:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:50:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:51:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:51:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:51:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:51:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:54:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:54:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:54:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:54:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:55:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:55:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:55:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:55:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:56:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:56:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:56:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:56:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:59:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:59:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 19:59:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 19:59:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:00:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:00:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:00:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:00:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:00:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:00:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:01:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:01:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:03:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:03:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:04:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:04:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:05:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:05:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:05:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:05:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:05:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:05:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:06:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:06:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:08:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:08:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:09:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:09:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:09:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:09:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:09:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:09:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:10:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:10:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:11:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:11:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:13:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:13:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:14:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:14:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:14:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:14:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:14:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:14:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:15:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:15:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:15:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:15:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:18:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:18:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:18:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:18:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:19:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:19:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:19:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:19:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:20:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:20:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:20:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:20:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:23:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:23:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:23:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:23:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:24:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:24:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:24:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:24:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:24:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:24:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:25:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:25:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:28:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:28:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:28:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:28:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:29:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:29:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:29:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:29:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:29:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:29:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:30:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:30:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:32:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:32:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:33:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:33:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:33:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:33:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:33:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:33:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:34:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:34:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:35:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:35:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:37:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:37:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:38:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:38:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:38:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:38:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:38:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:38:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:39:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:39:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:39:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:39:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:42:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:42:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:42:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:42:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:43:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:43:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:43:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:43:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:44:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:44:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:44:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:44:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:47:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:47:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:47:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:47:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:48:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:48:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:48:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:48:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:48:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:48:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:49:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:49:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:52:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:52:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:52:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:52:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:53:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:53:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:53:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:53:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:53:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:53:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:54:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:54:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:56:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:56:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:57:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:57:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:57:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:57:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:58:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:58:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:58:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:58:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 20:59:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 20:59:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:01:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:01:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:02:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:02:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:02:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:02:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:02:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:02:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:03:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:03:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:03:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:03:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:06:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:06:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:06:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:06:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:07:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:07:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:07:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:07:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:08:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:08:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:08:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:08:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:11:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:11:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:11:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:11:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:12:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:12:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:12:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:12:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:12:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:12:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:13:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:13:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:16:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:16:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:16:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:16:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:17:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:17:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:17:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:17:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:17:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:17:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:18:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:18:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:20:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:20:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:21:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:21:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:21:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:21:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:22:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:22:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:22:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:22:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:23:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:23:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:25:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:25:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:26:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:26:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:26:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:26:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:26:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:26:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:27:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:27:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:27:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:27:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:30:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:30:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:30:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:30:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:31:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:31:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:31:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:31:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:32:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:32:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:32:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:32:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:35:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:35:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:35:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:35:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:36:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:36:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:36:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:36:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:36:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:36:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:37:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:37:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:40:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:40:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:40:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:40:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:41:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:41:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:41:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:41:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:41:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:41:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:42:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:42:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:44:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:44:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:45:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:45:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:45:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:45:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:46:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:46:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:46:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:46:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:47:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:47:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:49:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:49:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:50:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:50:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:50:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:50:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:50:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:50:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:51:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:51:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:51:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:51:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:54:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:54:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:54:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:54:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:55:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:55:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:55:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:55:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:56:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:56:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:56:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:56:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:59:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:59:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 21:59:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 21:59:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:00:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:00:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:00:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:00:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:00:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:00:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:01:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:01:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:04:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:04:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:04:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:04:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:05:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:05:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:05:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:05:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:05:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:05:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:06:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:06:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:08:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:08:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:09:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:09:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:09:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:09:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:10:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:10:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:10:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:10:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:11:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:11:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:13:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:13:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:14:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:14:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:14:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:14:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:14:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:14:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:15:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:15:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:15:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:15:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:18:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:18:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:19:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:19:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:19:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:19:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:19:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:19:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:20:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:20:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:20:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:20:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:23:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:23:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:23:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:23:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:24:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:24:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:24:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:24:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:25:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:25:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:25:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:25:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:28:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:28:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:28:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:28:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:29:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:29:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:29:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:29:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:29:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:29:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:30:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:30:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:32:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:32:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:33:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:33:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:33:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:33:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:34:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:34:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:34:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:34:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:35:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:35:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:37:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:37:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:38:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:38:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:38:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:38:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:38:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:38:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:39:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:39:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:39:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:39:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:42:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:42:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:43:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:43:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:43:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:43:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:43:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:43:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:44:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:44:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:44:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:44:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:47:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:47:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:47:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:47:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:48:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:48:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:48:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:48:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:49:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:49:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:49:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:49:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:52:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:52:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:52:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:52:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:53:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:53:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:53:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:53:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:53:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:53:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:54:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:54:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:56:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:56:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:57:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:57:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:57:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:57:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:58:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:58:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:58:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:58:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 22:59:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 22:59:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:01:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:01:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:02:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:02:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:02:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:02:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:02:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:02:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:03:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:03:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:03:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:03:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:06:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:06:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:07:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:07:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:07:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:07:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:07:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:07:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:08:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:08:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:08:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:08:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:11:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:11:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:11:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:11:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:12:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:12:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:12:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:12:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:13:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:13:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:13:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:13:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:16:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:16:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:16:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:16:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:17:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:17:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:17:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:17:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:17:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:17:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:18:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:18:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:20:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:20:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:21:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:21:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:21:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:21:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:22:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:22:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:22:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:22:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:23:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:23:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:25:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:25:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:26:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:26:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:26:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:26:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:26:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:26:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:27:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:27:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:27:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:27:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:30:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:30:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:31:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:31:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:31:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:31:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:31:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:31:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:32:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:32:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:32:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:32:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:35:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:35:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:35:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:35:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:36:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:36:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:36:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:36:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:37:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:37:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:37:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:37:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:40:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:40:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:40:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:40:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:41:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:41:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:41:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:41:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:41:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:41:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:42:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:42:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:44:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:44:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:45:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:45:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:46:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:46:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:46:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:46:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:46:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:46:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:47:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:47:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:49:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:49:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:50:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:50:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:50:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:50:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:50:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:50:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:51:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:51:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:51:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:51:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:54:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:54:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:55:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:55:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:55:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:55:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:55:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:55:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:56:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:56:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:56:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:56:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:59:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:59:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-26 23:59:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-26 23:59:53] [INFO] Cache de usuários da intranet criado com 290 usuários
