<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Relacionar PAs com Pontos de Atendimento</h1>";
echo "<p>Esta ferramenta permite relacionar a coluna 'pa' com a tabela 'pontos_atendimento'.</p>";
echo "<hr>";

try {
    echo "<h3>1. Analisando estrutura atual...</h3>";
    
    // Verificar estrutura atual da coluna PA
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field = 'pa'");
    $stmt->execute();
    $coluna_pa = $stmt->fetch();
    
    if (!$coluna_pa) {
        echo "<p style='color: red;'>❌ Coluna 'pa' não encontrada!</p>";
        exit;
    }
    
    echo "<h4>Estrutura atual da coluna PA:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th><PERSON><PERSON><PERSON></th></tr>";
    echo "<tr>";
    echo "<td><strong>{$coluna_pa['Field']}</strong></td>";
    echo "<td><code>{$coluna_pa['Type']}</code></td>";
    echo "<td>{$coluna_pa['Null']}</td>";
    echo "<td>{$coluna_pa['Key']}</td>";
    echo "<td>" . ($coluna_pa['Default'] ?? 'NULL') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    // Verificar dados atuais de PA
    echo "<h3>2. Analisando PAs existentes...</h3>";
    
    $stmt = $pdo_mci->prepare("
        SELECT pa, COUNT(*) as total 
        FROM cad_registros 
        GROUP BY pa 
        ORDER BY CAST(pa AS UNSIGNED), pa
    ");
    $stmt->execute();
    $pas_existentes = $stmt->fetchAll();
    
    echo "<h4>PAs encontrados nos registros:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>PA</th><th>Total Registros</th></tr>";
    foreach ($pas_existentes as $pa) {
        echo "<tr>";
        echo "<td><strong>" . htmlspecialchars($pa['pa']) . "</strong></td>";
        echo "<td>{$pa['total']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar tabela pontos_atendimento
    echo "<h3>3. Verificando tabela pontos_atendimento...</h3>";
    
    try {
        // Verificar estrutura da tabela pontos_atendimento
        $stmt = $pdo->prepare("SHOW COLUMNS FROM pontos_atendimento");
        $stmt->execute();
        $colunas_pa_table = $stmt->fetchAll();
        
        echo "<h4>Estrutura da tabela pontos_atendimento:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th></tr>";
        foreach ($colunas_pa_table as $coluna) {
            echo "<tr>";
            echo "<td><strong>{$coluna['Field']}</strong></td>";
            echo "<td><code>{$coluna['Type']}</code></td>";
            echo "<td>{$coluna['Null']}</td>";
            echo "<td>{$coluna['Key']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Buscar pontos de atendimento disponíveis
        $stmt = $pdo->prepare("SELECT * FROM pontos_atendimento ORDER BY id LIMIT 10");
        $stmt->execute();
        $pontos_disponiveis = $stmt->fetchAll();
        
        echo "<h4>Pontos de atendimento disponíveis (amostra):</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        foreach ($colunas_pa_table as $coluna) {
            echo "<th>" . htmlspecialchars($coluna['Field']) . "</th>";
        }
        echo "</tr>";
        foreach ($pontos_disponiveis as $ponto) {
            echo "<tr>";
            foreach ($colunas_pa_table as $coluna) {
                $campo = $coluna['Field'];
                echo "<td>" . htmlspecialchars($ponto[$campo] ?? '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        // Contar total de pontos
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM pontos_atendimento");
        $stmt->execute();
        $total_pontos = $stmt->fetchColumn();
        echo "<p><strong>Total de pontos de atendimento:</strong> $total_pontos</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erro ao acessar tabela pontos_atendimento: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>Verifique se a tabela existe no banco sicoob_access_control.</p>";
        exit;
    }
    
    // Análise de correspondência
    echo "<h3>4. Análise de correspondência...</h3>";
    echo "<p><strong>Relacionamento:</strong> coluna 'pa' (cad_registros) ↔ coluna 'numero' (pontos_atendimento)</p>";

    // Verificar quais PAs existem na tabela pontos_atendimento pela coluna 'numero'
    $pas_encontrados = [];
    $pas_nao_encontrados = [];

    foreach ($pas_existentes as $pa_registro) {
        $pa_valor = $pa_registro['pa'];

        // Buscar especificamente pela coluna 'numero'
        $stmt = $pdo->prepare("
            SELECT * FROM pontos_atendimento
            WHERE numero = ?
            LIMIT 1
        ");
        $stmt->execute([$pa_valor]);
        $ponto_encontrado = $stmt->fetch();

        if ($ponto_encontrado) {
            $pas_encontrados[] = [
                'pa' => $pa_valor,
                'total' => $pa_registro['total'],
                'ponto' => $ponto_encontrado
            ];
        } else {
            $pas_nao_encontrados[] = [
                'pa' => $pa_valor,
                'total' => $pa_registro['total']
            ];
        }
    }
    
    if (!empty($pas_encontrados)) {
        echo "<h5>PAs encontrados na tabela pontos_atendimento:</h5>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>PA</th><th>Registros</th><th>Número Ponto</th><th>Nome Ponto</th><th>ID Ponto</th></tr>";
        foreach ($pas_encontrados as $pa) {
            echo "<tr style='background-color: #d4edda;'>";
            echo "<td><strong>{$pa['pa']}</strong></td>";
            echo "<td>{$pa['total']}</td>";
            echo "<td><strong>{$pa['ponto']['numero']}</strong></td>";
            echo "<td>" . htmlspecialchars($pa['ponto']['nome'] ?? $pa['ponto']['codigo'] ?? 'N/A') . "</td>";
            echo "<td>{$pa['ponto']['id']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    if (!empty($pas_nao_encontrados)) {
        echo "<h5>PAs NÃO encontrados na tabela pontos_atendimento:</h5>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>PA</th><th>Registros</th></tr>";
        foreach ($pas_nao_encontrados as $pa) {
            echo "<tr style='background-color: #f8d7da;'>";
            echo "<td><strong>{$pa['pa']}</strong></td>";
            echo "<td>{$pa['total']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $acao = $_POST['acao'] ?? '';
        
        if ($acao == 'criar_relacionamento') {
            echo "<h3>5. Criando relacionamento...</h3>";
            
            // Verificar se a coluna PA é VARCHAR
            $pa_eh_varchar = strpos($coluna_pa['Type'], 'varchar') !== false;
            
            if (!$pa_eh_varchar) {
                echo "<p style='color: orange;'>⚠️ Coluna PA não é VARCHAR. Tipo atual: {$coluna_pa['Type']}</p>";
                echo "<p>Vamos converter para INT para relacionamento com ID.</p>";
            }
            
            try {
                // Verificar se a coluna PA já tem o tipo correto para relacionamento direto
                $pa_eh_varchar = strpos($coluna_pa['Type'], 'varchar') !== false;

                if ($pa_eh_varchar) {
                    echo "<p>Passo 1: Configurando relacionamento direto PA ↔ numero...</p>";
                    echo "<p>✅ Coluna PA já é VARCHAR, compatível com coluna 'numero'</p>";

                    // Verificar se todos os PAs encontrados têm correspondência
                    $mapeamentos = 0;
                    foreach ($pas_encontrados as $pa) {
                        $mapeamentos += $pa['total'];
                    }

                    echo "<p><strong>Registros que serão relacionados:</strong> $mapeamentos</p>";

                    // Não precisa mapear dados, pois o relacionamento é direto
                    echo "<p>✅ Relacionamento direto: PA (VARCHAR) ↔ numero (VARCHAR)</p>";
                } else {
                    echo "<p>Passo 1: Criando coluna temporária...</p>";
                    $pdo_mci->exec("ALTER TABLE cad_registros ADD COLUMN pa_numero VARCHAR(10) NULL COMMENT 'Número do ponto de atendimento (referência para sicoob_access_control.pontos_atendimento.numero)'");
                    echo "<p>✅ Coluna pa_numero criada</p>";

                    // Passo 2: Copiar valores de PA para pa_numero
                    echo "<p>Passo 2: Copiando valores de PA...</p>";

                    $mapeamentos = 0;
                    foreach ($pas_encontrados as $pa) {
                        try {
                            $stmt = $pdo_mci->prepare("UPDATE cad_registros SET pa_numero = ? WHERE pa = ?");
                            $stmt->execute([$pa['pa'], $pa['pa']]);
                            $affected = $stmt->rowCount();
                            echo "<p>✅ PA '{$pa['pa']}' copiado ($affected registros)</p>";
                            $mapeamentos += $affected;
                        } catch (Exception $e) {
                            echo "<p style='color: red;'>❌ Erro ao copiar PA '{$pa['pa']}': " . htmlspecialchars($e->getMessage()) . "</p>";
                        }
                    }

                    echo "<p><strong>Total copiado:</strong> $mapeamentos registros</p>";
                }
                
                // Estrutura mantida como VARCHAR para relacionamento direto
                echo "<p>Passo 2: Estrutura mantida como VARCHAR para relacionamento direto</p>";
                
                // Passo 3: Adicionar chave estrangeira
                echo "<p>Passo 3: Adicionando chave estrangeira...</p>";
                try {
                    if ($pa_eh_varchar) {
                        // Relacionamento direto: pa (VARCHAR) ↔ numero (VARCHAR)
                        $pdo_mci->exec("
                            ALTER TABLE cad_registros
                            ADD CONSTRAINT fk_registros_pa_numero
                            FOREIGN KEY (pa) REFERENCES sicoob_access_control.pontos_atendimento(numero)
                            ON UPDATE CASCADE ON DELETE RESTRICT
                        ");
                        echo "<p>✅ Chave estrangeira pa → pontos_atendimento.numero adicionada</p>";
                    } else {
                        // Relacionamento via coluna adicional
                        $pdo_mci->exec("
                            ALTER TABLE cad_registros
                            ADD CONSTRAINT fk_registros_pa_numero
                            FOREIGN KEY (pa_numero) REFERENCES sicoob_access_control.pontos_atendimento(numero)
                            ON UPDATE CASCADE ON DELETE RESTRICT
                        ");
                        echo "<p>✅ Chave estrangeira pa_numero → pontos_atendimento.numero adicionada</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ Erro ao adicionar chave estrangeira: " . htmlspecialchars($e->getMessage()) . "</p>";
                    echo "<p>Isso pode acontecer se houver valores de PA que não existem na coluna 'numero' da tabela pontos_atendimento.</p>";
                }
                
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4 style='color: #155724;'>🎉 Relacionamento Criado com Sucesso!</h4>";
                echo "<ul style='color: #155724;'>";
                echo "<li>✅ Coluna de relacionamento criada</li>";
                echo "<li>✅ $mapeamentos registros mapeados</li>";
                echo "<li>✅ Chave estrangeira configurada</li>";
                echo "<li>✅ Integridade referencial garantida</li>";
                echo "</ul>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro durante criação do relacionamento: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    } else {
        // Mostrar opções
        echo "<h3>5. Opções de Relacionamento</h3>";
        
        $total_encontrados = count($pas_encontrados);
        $total_nao_encontrados = count($pas_nao_encontrados);
        $percentual_encontrados = $total_encontrados > 0 ? round(($total_encontrados / ($total_encontrados + $total_nao_encontrados)) * 100, 1) : 0;
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px;'>";
        echo "<h4>📊 Resumo da Análise</h4>";
        echo "<ul>";
        echo "<li><strong>PAs encontrados:</strong> $total_encontrados ($percentual_encontrados%)</li>";
        echo "<li><strong>PAs não encontrados:</strong> $total_nao_encontrados</li>";
        echo "<li><strong>Estrutura atual:</strong> PA como " . $coluna_pa['Type'] . "</li>";
        echo "</ul>";
        echo "</div>";
        
        if ($total_encontrados > 0) {
            echo "<div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>🔗 Criar Relacionamento</h4>";
            echo "<p>Esta operação irá criar relacionamento: <strong>pa ↔ pontos_atendimento.numero</strong></p>";
            echo "<ol>";
            echo "<li>Configurar relacionamento direto entre as colunas VARCHAR</li>";
            echo "<li>Validar que PAs existem na coluna 'numero' da tabela pontos_atendimento</li>";
            echo "<li>Configurar chave estrangeira: pa → pontos_atendimento.numero</li>";
            echo "<li>Garantir integridade referencial</li>";
            echo "</ol>";
            
            echo "<form method='POST'>";
            echo "<input type='hidden' name='acao' value='criar_relacionamento'>";

            echo "<div style='background-color: #e7f3ff; padding: 10px; border-radius: 5px; margin: 15px 0;'>";
            echo "<p><strong>Tipo de relacionamento:</strong> VARCHAR ↔ VARCHAR</p>";
            echo "<p>A coluna 'pa' (VARCHAR) será relacionada diretamente com 'pontos_atendimento.numero' (VARCHAR)</p>";
            echo "</div>";

            echo "<div style='margin: 15px 0;'>";
            echo "<label>";
            echo "<input type='checkbox' required> ";
            echo "<strong>Confirmo que desejo criar o relacionamento pa ↔ pontos_atendimento.numero</strong>";
            echo "</label>";
            echo "</div>";

            echo "<button type='submit' style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold;'>🔗 CRIAR RELACIONAMENTO</button>";
            echo "</form>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4 style='color: #721c24;'>❌ Relacionamento Não Recomendado</h4>";
            echo "<p style='color: #721c24;'>Nenhum PA foi encontrado na tabela pontos_atendimento.</p>";
            echo "<p style='color: #721c24;'>Verifique se os dados estão corretos ou se a tabela está populada.</p>";
            echo "</div>";
        }
    }
    
    echo "<hr>";
    echo "<p><a href='gerenciar.php'>Ver registros</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a análise</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
    font-weight: bold;
}
</style>
