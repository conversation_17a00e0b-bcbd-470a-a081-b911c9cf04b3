<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Mapeamento Gradual de Usuários</h1>";
echo "<p>Esta ferramenta permite mapear nomes para IDs de usuários SEM perder dados.</p>";
echo "<hr>";

try {
    echo "<h3>1. Verificando estrutura atual...</h3>";
    
    // Verificar se as colunas são VARCHAR (texto)
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field IN ('funcionario', 'tecnico_responsavel')");
    $stmt->execute();
    $colunas = $stmt->fetchAll();
    
    $funcionario_eh_texto = false;
    $tecnico_eh_texto = false;
    
    foreach ($colunas as $coluna) {
        if ($coluna['Field'] == 'funcionario' && strpos($coluna['Type'], 'varchar') !== false) {
            $funcionario_eh_texto = true;
        }
        if ($coluna['Field'] == 'tecnico_responsavel' && strpos($coluna['Type'], 'varchar') !== false) {
            $tecnico_eh_texto = true;
        }
    }
    
    if (!$funcionario_eh_texto || !$tecnico_eh_texto) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #721c24;'>❌ Estrutura Incompatível</h4>";
        echo "<p style='color: #721c24;'>As colunas precisam ser VARCHAR (texto) para usar esta ferramenta.</p>";
        echo "<p style='color: #721c24;'>Use a <a href='restaurar_dados_usuarios.php'>ferramenta de restauração</a> primeiro.</p>";
        echo "</div>";
        exit;
    }
    
    echo "<p>✅ Estrutura compatível - Colunas são VARCHAR</p>";
    
    // Buscar nomes únicos de funcionários e técnicos
    echo "<h3>2. Analisando nomes existentes...</h3>";
    
    $stmt = $pdo_mci->prepare("
        SELECT funcionario, COUNT(*) as total 
        FROM cad_registros 
        WHERE funcionario IS NOT NULL AND funcionario != '' 
        GROUP BY funcionario 
        ORDER BY total DESC
    ");
    $stmt->execute();
    $funcionarios = $stmt->fetchAll();
    
    $stmt = $pdo_mci->prepare("
        SELECT tecnico_responsavel, COUNT(*) as total 
        FROM cad_registros 
        WHERE tecnico_responsavel IS NOT NULL AND tecnico_responsavel != '' 
        GROUP BY tecnico_responsavel 
        ORDER BY total DESC
    ");
    $stmt->execute();
    $tecnicos = $stmt->fetchAll();
    
    // Buscar usuários disponíveis
    $stmt = $pdo->prepare("SELECT id, nome_completo FROM usuarios WHERE ativo = 1 ORDER BY nome_completo");
    $stmt->execute();
    $usuarios = $stmt->fetchAll();
    
    echo "<h4>Funcionários encontrados: " . count($funcionarios) . "</h4>";
    echo "<h4>Técnicos encontrados: " . count($tecnicos) . "</h4>";
    echo "<h4>Usuários disponíveis: " . count($usuarios) . "</h4>";
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $acao = $_POST['acao'] ?? '';
        
        if ($acao == 'mapear_automatico') {
            echo "<h3>3. Executando mapeamento automático...</h3>";
            
            // Criar mapa de usuários para busca rápida
            $mapa_usuarios = [];
            foreach ($usuarios as $usuario) {
                $mapa_usuarios[trim($usuario['nome_completo'])] = $usuario['id'];
            }
            
            $mapeados_funcionario = 0;
            $mapeados_tecnico = 0;
            
            // Mapear funcionários
            echo "<h4>Mapeando funcionários:</h4>";
            foreach ($funcionarios as $func) {
                $nome = trim($func['funcionario']);
                if (isset($mapa_usuarios[$nome])) {
                    $id_usuario = $mapa_usuarios[$nome];
                    try {
                        $stmt = $pdo_mci->prepare("UPDATE cad_registros SET funcionario = ? WHERE funcionario = ?");
                        $stmt->execute([$id_usuario, $nome]);
                        $affected = $stmt->rowCount();
                        echo "<p>✅ '$nome' → ID $id_usuario ($affected registros)</p>";
                        $mapeados_funcionario += $affected;
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>❌ Erro ao mapear '$nome': " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠️ '$nome' não encontrado nos usuários</p>";
                }
            }
            
            // Mapear técnicos
            echo "<h4>Mapeando técnicos:</h4>";
            foreach ($tecnicos as $tec) {
                $nome = trim($tec['tecnico_responsavel']);
                if (isset($mapa_usuarios[$nome])) {
                    $id_usuario = $mapa_usuarios[$nome];
                    try {
                        $stmt = $pdo_mci->prepare("UPDATE cad_registros SET tecnico_responsavel = ? WHERE tecnico_responsavel = ?");
                        $stmt->execute([$id_usuario, $nome]);
                        $affected = $stmt->rowCount();
                        echo "<p>✅ '$nome' → ID $id_usuario ($affected registros)</p>";
                        $mapeados_tecnico += $affected;
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>❌ Erro ao mapear '$nome': " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠️ '$nome' não encontrado nos usuários</p>";
                }
            }
            
            echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4 style='color: #155724;'>📊 Resumo do Mapeamento</h4>";
            echo "<ul style='color: #155724;'>";
            echo "<li>Funcionários mapeados: $mapeados_funcionario registros</li>";
            echo "<li>Técnicos mapeados: $mapeados_tecnico registros</li>";
            echo "</ul>";
            echo "<p style='color: #155724;'><strong>Próximo passo:</strong> Converter estrutura das colunas para INT</p>";
            echo "</div>";
            
        } elseif ($acao == 'converter_estrutura') {
            echo "<h3>3. Convertendo estrutura para INT...</h3>";
            
            try {
                // Converter colunas para INT
                echo "<p>Convertendo funcionario para INT...</p>";
                $pdo_mci->exec("ALTER TABLE cad_registros MODIFY funcionario INT NULL COMMENT 'ID do funcionário (referência para sicoob_access_control.usuarios)'");
                echo "<p>✅ Coluna funcionario convertida</p>";
                
                echo "<p>Convertendo tecnico_responsavel para INT...</p>";
                $pdo_mci->exec("ALTER TABLE cad_registros MODIFY tecnico_responsavel INT NULL COMMENT 'ID do técnico responsável (referência para sicoob_access_control.usuarios)'");
                echo "<p>✅ Coluna tecnico_responsavel convertida</p>";
                
                // Adicionar chaves estrangeiras
                echo "<p>Adicionando chaves estrangeiras...</p>";
                try {
                    $pdo_mci->exec("
                        ALTER TABLE cad_registros 
                        ADD CONSTRAINT fk_registros_funcionario 
                        FOREIGN KEY (funcionario) REFERENCES sicoob_access_control.usuarios(id) 
                        ON UPDATE CASCADE ON DELETE SET NULL
                    ");
                    echo "<p>✅ Chave estrangeira funcionario adicionada</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ Erro FK funcionario: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
                
                try {
                    $pdo_mci->exec("
                        ALTER TABLE cad_registros 
                        ADD CONSTRAINT fk_registros_tecnico 
                        FOREIGN KEY (tecnico_responsavel) REFERENCES sicoob_access_control.usuarios(id) 
                        ON UPDATE CASCADE ON DELETE SET NULL
                    ");
                    echo "<p>✅ Chave estrangeira tecnico_responsavel adicionada</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ Erro FK tecnico: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
                
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4 style='color: #155724;'>🎉 Conversão Concluída!</h4>";
                echo "<ul style='color: #155724;'>";
                echo "<li>✅ Colunas convertidas para INT</li>";
                echo "<li>✅ Chaves estrangeiras configuradas</li>";
                echo "<li>✅ Dados preservados</li>";
                echo "</ul>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro na conversão: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    } else {
        // Mostrar opções
        echo "<h3>3. Opções de Mapeamento</h3>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🔄 Processo Seguro em 2 Etapas</h4>";
        echo "<p>Este processo preserva todos os dados existentes:</p>";
        echo "<ol>";
        echo "<li><strong>Mapeamento:</strong> Substitui nomes por IDs (mantém estrutura VARCHAR)</li>";
        echo "<li><strong>Conversão:</strong> Converte colunas para INT com chaves estrangeiras</li>";
        echo "</ol>";
        echo "</div>";
        
        // Mostrar preview do mapeamento
        echo "<h4>Preview do Mapeamento Automático:</h4>";
        
        // Criar mapa de usuários
        $mapa_usuarios = [];
        foreach ($usuarios as $usuario) {
            $mapa_usuarios[trim($usuario['nome_completo'])] = $usuario['id'];
        }
        
        echo "<h5>Funcionários:</h5>";
        echo "<table border='1' cellpadding='5' style='max-width: 600px;'>";
        echo "<tr><th>Nome Atual</th><th>Será Mapeado Para</th><th>Registros</th></tr>";
        foreach (array_slice($funcionarios, 0, 10) as $func) {
            $nome = trim($func['funcionario']);
            $mapeado = isset($mapa_usuarios[$nome]) ? "ID " . $mapa_usuarios[$nome] : "❌ Não encontrado";
            $cor = isset($mapa_usuarios[$nome]) ? "color: green;" : "color: red;";
            echo "<tr>";
            echo "<td>" . htmlspecialchars($nome) . "</td>";
            echo "<td style='$cor'><strong>$mapeado</strong></td>";
            echo "<td>{$func['total']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h5>Técnicos:</h5>";
        echo "<table border='1' cellpadding='5' style='max-width: 600px;'>";
        echo "<tr><th>Nome Atual</th><th>Será Mapeado Para</th><th>Registros</th></tr>";
        foreach (array_slice($tecnicos, 0, 10) as $tec) {
            $nome = trim($tec['tecnico_responsavel']);
            $mapeado = isset($mapa_usuarios[$nome]) ? "ID " . $mapa_usuarios[$nome] : "❌ Não encontrado";
            $cor = isset($mapa_usuarios[$nome]) ? "color: green;" : "color: red;";
            echo "<tr>";
            echo "<td>" . htmlspecialchars($nome) . "</td>";
            echo "<td style='$cor'><strong>$mapeado</strong></td>";
            echo "<td>{$tec['total']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Botões de ação
        echo "<div style='margin: 30px 0;'>";
        echo "<h4>Executar Mapeamento:</h4>";
        
        echo "<form method='POST' style='display: inline-block; margin-right: 20px;'>";
        echo "<input type='hidden' name='acao' value='mapear_automatico'>";
        echo "<button type='submit' style='background-color: #007bff; color: white; padding: 15px 25px; border: none; border-radius: 5px; font-size: 16px;'>";
        echo "🔄 ETAPA 1: Mapear Nomes para IDs";
        echo "</button>";
        echo "</form>";
        
        echo "<form method='POST' style='display: inline-block;'>";
        echo "<input type='hidden' name='acao' value='converter_estrutura'>";
        echo "<button type='submit' style='background-color: #28a745; color: white; padding: 15px 25px; border: none; border-radius: 5px; font-size: 16px;'>";
        echo "🔧 ETAPA 2: Converter para INT";
        echo "</button>";
        echo "</form>";
        echo "</div>";
        
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h5>⚠️ Importante:</h5>";
        echo "<ul>";
        echo "<li>Execute as etapas em ordem</li>";
        echo "<li>Nomes que não correspondem a usuários ficarão como texto</li>";
        echo "<li>Apenas nomes exatos serão mapeados</li>";
        echo "<li>Dados não serão perdidos</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<p><a href='restaurar_dados_usuarios.php'>Restaurar dados</a> | <a href='gerenciar.php'>Ver registros</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante o mapeamento</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
    font-weight: bold;
}
</style>
