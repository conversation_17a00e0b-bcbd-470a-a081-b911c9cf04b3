<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Identidade Visual - Botão Download Excel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #70B86C;
            --sicoob-roxo: #494790;
        }
        
        body { 
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); 
            min-height: 100vh;
            padding: 20px; 
        }
        
        .card { 
            border: none; 
            border-radius: 15px; 
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .card-header { 
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); 
            color: white; 
            border-radius: 15px 15px 0 0 !important;
        }
        
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        
        /* Botão de download Excel - Identidade Visual Sicoob */
        .btn-excel {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-excel:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 174, 157, 0.4);
        }

        .btn-excel:focus {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }
        
        /* Variações de botões com cores da identidade */
        .btn-sicoob-turquesa {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white;
        }
        
        .btn-sicoob-turquesa:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
        }
        
        .btn-sicoob-verde-escuro {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
        }
        
        .btn-sicoob-verde-escuro:hover {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white;
        }
        
        .btn-sicoob-verde-claro {
            background-color: var(--sicoob-verde-claro);
            border-color: var(--sicoob-verde-claro);
            color: var(--sicoob-verde-escuro);
        }
        
        .btn-sicoob-verde-claro:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
        }
        
        .color-sample {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: inline-block;
            margin: 5px;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .before-after {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white mb-4 text-center">
            <i class="fas fa-palette"></i> 
            Identidade Visual Sicoob - Botão Download Excel
        </h1>
        
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header text-center">
                        <h5 class="mb-0">
                            <i class="fas fa-file-excel"></i> 
                            Novo Botão com Identidade Visual Sicoob
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="before-after">
                            <h6 class="text-danger">❌ Antes (Verde Padrão Bootstrap)</h6>
                            <button type="button" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> Download Excel
                            </button>
                            <p class="small text-muted mt-2">Cor genérica, sem identidade visual</p>
                        </div>
                        
                        <div class="before-after">
                            <h6 class="text-success">✅ Agora (Identidade Visual Sicoob)</h6>
                            <button type="button" class="btn btn-excel btn-lg">
                                <i class="fas fa-file-excel"></i> Download Excel
                            </button>
                            <p class="small text-muted mt-2">
                                Cor turquesa Sicoob com hover verde escuro<br>
                                <small>Passe o mouse sobre o botão para ver o efeito</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-palette"></i> Cores da Identidade Visual Sicoob
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="color-sample" style="background-color: #00AE9D;"></div>
                                <p class="small mb-0"><strong>Turquesa</strong></p>
                                <p class="small text-muted">#00AE9D</p>
                                <p class="small">✅ Cor principal do botão</p>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="color-sample" style="background-color: #003641;"></div>
                                <p class="small mb-0"><strong>Verde Escuro</strong></p>
                                <p class="small text-muted">#003641</p>
                                <p class="small">✅ Cor do hover</p>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="color-sample" style="background-color: #C9D200;"></div>
                                <p class="small mb-0"><strong>Verde Claro</strong></p>
                                <p class="small text-muted">#C9D200</p>
                                <p class="small">Alternativa</p>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="color-sample" style="background-color: #494790;"></div>
                                <p class="small mb-0"><strong>Roxo</strong></p>
                                <p class="small text-muted">#494790</p>
                                <p class="small">Alternativa</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-magic"></i> Efeitos Aplicados
                        </h6>
                    </div>
                    <div class="card-body">
                        <h6><i class="fas fa-paint-brush"></i> Características do Botão:</h6>
                        <ul class="small">
                            <li><strong>Cor Principal:</strong> Turquesa Sicoob (#00AE9D)</li>
                            <li><strong>Hover:</strong> Verde Escuro Sicoob (#003641)</li>
                            <li><strong>Transição:</strong> Suave (0.3s)</li>
                            <li><strong>Elevação:</strong> Sutil movimento para cima no hover</li>
                            <li><strong>Sombra:</strong> Turquesa com transparência</li>
                            <li><strong>Focus:</strong> Anel turquesa para acessibilidade</li>
                        </ul>
                        
                        <h6 class="mt-3"><i class="fas fa-code"></i> CSS Aplicado:</h6>
                        <pre class="small"><code>.btn-excel {
    background-color: var(--sicoob-turquesa);
    border-color: var(--sicoob-turquesa);
    color: white;
    font-weight: 500;
}

.btn-excel:hover {
    background-color: var(--sicoob-verde-escuro);
    border-color: var(--sicoob-verde-escuro);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 174, 157, 0.3);
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-swatchbook"></i> Variações de Botões com Identidade Visual
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <button type="button" class="btn btn-sicoob-turquesa w-100">
                                    <i class="fas fa-file-excel"></i> Turquesa
                                </button>
                                <p class="small mt-2">Escolhido para o botão</p>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button type="button" class="btn btn-sicoob-verde-escuro w-100">
                                    <i class="fas fa-file-excel"></i> Verde Escuro
                                </button>
                                <p class="small mt-2">Alternativa elegante</p>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button type="button" class="btn btn-sicoob-verde-claro w-100">
                                    <i class="fas fa-file-excel"></i> Verde Claro
                                </button>
                                <p class="small mt-2">Alternativa vibrante</p>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button type="button" class="btn btn-excel w-100">
                                    <i class="fas fa-file-excel"></i> Final
                                </button>
                                <p class="small mt-2">Implementado</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-eye"></i> Teste na Página Real
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <p>Veja o botão com a nova identidade visual na página gerenciar.php:</p>
                        <a href="gerenciar.php" class="btn btn-excel btn-lg" target="_blank">
                            <i class="fas fa-external-link-alt"></i> Ver na Página Gerenciar
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4" style="background: rgba(212, 237, 218, 0.9); border: none; border-radius: 15px;">
            <h5><i class="fas fa-check-circle"></i> Identidade Visual Aplicada!</h5>
            <p class="mb-0">
                O botão de download Excel agora segue a identidade visual do projeto Sicoob, 
                usando as cores turquesa e verde escuro com efeitos suaves e elegantes.
                <strong>A funcionalidade mantém-se 100% funcionando com visual aprimorado!</strong>
            </p>
        </div>
    </div>
</body>
</html>
