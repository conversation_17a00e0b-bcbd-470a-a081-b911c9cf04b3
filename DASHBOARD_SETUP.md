# 🚀 Setup do Dashboard MCI

## ❌ Problema Encontrado
Erro: `Table 'mci.mci_metas' doesn't exist`

## ✅ Solução Rápida

### Passo 1: Criar a Tabela de Metas
Acesse: `/mci/criar_tabela_metas.php`

**OU**

Execute manualmente no banco de dados:
```sql
USE mci;

CREATE TABLE IF NOT EXISTS mci_metas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    porcentagem_meta DECIMAL(5,2) NOT NULL COMMENT 'Porcentagem da meta (ex: 75.00 para 75%)',
    ativo BOOLEAN DEFAULT TRUE COMMENT 'Meta ativa/inativa',
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de criação',
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data de atualização',
    usuario_criacao INT COMMENT 'ID do usuário que criou',
    usuario_atualizacao INT COMMENT 'ID do usuário que atualizou',
    
    INDEX idx_ativo (ativo),
    INDEX idx_data_criacao (data_criacao)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela de configuração de metas do MCI';

INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) 
VALUES (75.00, TRUE, 1);
```

### Passo 2: Verificar Funcionamento
1. Acesse: `/mci/cadastro/dashboard.php`
2. Se ainda houver erro, execute: `/mci/cadastro/test_api.php`

## 📋 Links Úteis

- **Dashboard Principal**: `/mci/cadastro/dashboard.php`
- **Criar Tabela**: `/mci/criar_tabela_metas.php`
- **Teste API**: `/mci/cadastro/test_api.php`
- **Página de Navegação**: `/mci/cadastro/index.php`
- **Configurar Metas**: `/mci/metas.php`

## 🔧 Verificações Adicionais

### Se o erro persistir:
1. Verifique se você tem permissão de administrador no MCI
2. Confirme se o banco `mci` existe
3. Verifique se a conexão com o banco está funcionando
4. Execute o teste de API para verificar a integração

### Logs:
- Verifique os logs em: `/mci/logs/`
- Erros são registrados automaticamente

## 📞 Suporte
Se o problema persistir, verifique:
- Permissões de banco de dados
- Configurações de conexão
- Logs de erro do sistema

---
**Sistema MCI - Dashboard para TV**  
*Configuração necessária para funcionamento completo*
