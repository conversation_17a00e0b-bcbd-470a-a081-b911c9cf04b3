<?php
require_once '../auth_check.php';
require_once '../classes/Logger.php';

$logger = new MciLogger();

echo "<h2>🎯 Debug Específico - Maycon (ID 58)</h2>";

// Buscar dados do Maycon
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL AND u.id = 58
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
");
$stmt->execute();
$maycon = $stmt->fetch();

if (!$maycon) {
    echo "<p style='color: red;'>❌ <strong>Maycon (ID 58) não encontrado!</strong></p>";
    exit;
}

echo "<h3>📋 Dados do Maycon</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Campo</th><th>Valor</th></tr>";
echo "<tr><td><strong>ID</strong></td><td>" . $maycon['id'] . "</td></tr>";
echo "<tr><td><strong>Nome</strong></td><td>" . htmlspecialchars($maycon['nome']) . "</td></tr>";
echo "<tr><td><strong>Email</strong></td><td>" . htmlspecialchars($maycon['email']) . "</td></tr>";
echo "<tr><td><strong>Meta Total</strong></td><td>" . $maycon['meta_total'] . "</td></tr>";
echo "<tr><td><strong>Atualizados</strong></td><td>" . $maycon['atualizados_total'] . "</td></tr>";
echo "<tr><td><strong>Progresso</strong></td><td>" . $maycon['progresso_total'] . "%</td></tr>";
echo "</table>";

// Testar API da Intranet
echo "<h3>🌐 Teste da API Intranet</h3>";

try {
    require_once '../cadastro/config_api.php';
    echo "<p>✅ <strong>Config API carregado</strong></p>";
    
    $intranetAPI = getIntranetAPI($logger);
    echo "<p>✅ <strong>API Intranet inicializada</strong></p>";
    
    // Buscar usuários da intranet
    $usuarios_intranet = [];
    try {
        $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
        echo "<p>✅ <strong>Cache criado com " . count($usuarios_intranet) . " usuários</strong></p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ <strong>Erro ao criar cache:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        exit;
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Erro ao inicializar API:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// Buscar Maycon na API
echo "<h3>🔍 Busca do Maycon na API</h3>";

$email_maycon = strtolower(trim($maycon['email']));
echo "<p><strong>Email normalizado:</strong> " . htmlspecialchars($email_maycon) . "</p>";

if (isset($usuarios_intranet[$email_maycon])) {
    $usuario_intranet = $usuarios_intranet[$email_maycon];
    echo "<p style='color: green;'>✅ <strong>Maycon ENCONTRADO na API!</strong></p>";
    
    echo "<h4>📊 Dados da API:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Campo</th><th>Valor</th></tr>";
    
    foreach ($usuario_intranet as $campo => $valor) {
        $destaque = ($campo === 'foto_url') ? 'style="background-color: #fff3cd;"' : '';
        echo "<tr $destaque>";
        echo "<td><strong>" . htmlspecialchars($campo) . "</strong></td>";
        echo "<td>" . htmlspecialchars($valor ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Testar foto especificamente
    $foto_url = $usuario_intranet['foto_url'] ?? null;
    
    echo "<h4>📸 Teste da Foto:</h4>";
    if ($foto_url) {
        echo "<p><strong>URL da foto:</strong> " . htmlspecialchars($foto_url) . "</p>";
        
        // Testar se a URL é acessível
        echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 10px 0;'>";
        echo "<h5>🖼️ Visualização da Foto:</h5>";
        echo "<img src='" . htmlspecialchars($foto_url) . "' alt='Foto do Maycon' style='width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 3px solid #28a745;' onerror=\"this.style.border='3px solid #dc3545'; this.alt='❌ Erro ao carregar foto'; this.nextElementSibling.style.display='block';\">";
        echo "<p style='color: red; display: none; margin-top: 10px;'>❌ <strong>Erro ao carregar a imagem!</strong></p>";
        echo "</div>";
        
        // Testar com cURL
        echo "<h5>🔗 Teste de Conectividade (cURL):</h5>";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $foto_url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($http_code == 200) {
            echo "<p style='color: green;'>✅ <strong>URL acessível (HTTP $http_code)</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ <strong>URL não acessível (HTTP $http_code)</strong></p>";
            if ($error) {
                echo "<p style='color: red;'><strong>Erro cURL:</strong> " . htmlspecialchars($error) . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ <strong>foto_url é NULL ou vazio!</strong></p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ <strong>Maycon NÃO encontrado na API!</strong></p>";
    
    // Buscar emails similares
    echo "<h4>🔍 Busca por emails similares:</h4>";
    $nome_usuario = explode('@', $email_maycon)[0];
    echo "<p><strong>Buscando por:</strong> " . htmlspecialchars($nome_usuario) . "</p>";
    
    $similares = [];
    foreach ($usuarios_intranet as $api_email => $api_user) {
        if (stripos($api_email, $nome_usuario) !== false) {
            $similares[] = [
                'email' => $api_email,
                'nome' => $api_user['nome'] ?? 'N/A',
                'foto_url' => $api_user['foto_url'] ?? null
            ];
        }
    }
    
    if (!empty($similares)) {
        echo "<p style='color: orange;'>⚠️ <strong>Emails similares encontrados:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Email</th><th>Nome</th><th>Tem Foto</th></tr>";
        
        foreach ($similares as $similar) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($similar['email']) . "</td>";
            echo "<td>" . htmlspecialchars($similar['nome']) . "</td>";
            echo "<td>" . ($similar['foto_url'] ? '✅ Sim' : '❌ Não') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ <strong>Nenhum email similar encontrado</strong></p>";
    }
}

// Simular processamento do dashboard
echo "<h3>🔄 Simulação do Processamento do Dashboard</h3>";

$maycon['foto_url'] = null;
$maycon['setor'] = null;
$maycon['funcao'] = null;

if (!empty($maycon['email'])) {
    $email_key = strtolower(trim($maycon['email']));
    $usuario_intranet = $usuarios_intranet[$email_key] ?? null;

    if ($usuario_intranet) {
        $maycon['foto_url'] = $usuario_intranet['foto_url'] ?? null;
        $maycon['setor'] = $usuario_intranet['setor_nome'] ?? null;
        $maycon['funcao'] = $usuario_intranet['funcao_nome'] ?? null;
        
        echo "<p style='color: green;'>✅ <strong>Dados da intranet adicionados ao Maycon</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>Usuário não encontrado na intranet</strong></p>";
    }
}

echo "<h4>📊 Resultado Final:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Campo</th><th>Valor</th></tr>";
echo "<tr><td><strong>foto_url</strong></td><td>" . htmlspecialchars($maycon['foto_url'] ?? 'NULL') . "</td></tr>";
echo "<tr><td><strong>setor</strong></td><td>" . htmlspecialchars($maycon['setor'] ?? 'NULL') . "</td></tr>";
echo "<tr><td><strong>funcao</strong></td><td>" . htmlspecialchars($maycon['funcao'] ?? 'NULL') . "</td></tr>";
echo "</table>";

// Renderização final
echo "<h4>🎨 Como aparecerá no dashboard:</h4>";
echo "<div style='border: 2px solid #007bff; padding: 20px; text-align: center; background: white; border-radius: 15px; max-width: 300px; margin: 20px auto;'>";

if ($maycon['foto_url']) {
    echo "<img src='" . htmlspecialchars($maycon['foto_url']) . "' alt='" . htmlspecialchars($maycon['nome']) . "' style='width: 80px; height: 80px; border-radius: 50%; object-fit: cover; margin-bottom: 10px;' onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">";
    echo "<div style='width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #003641, #00AE9D); display: none; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: bold; margin: 0 auto 10px;'>M</div>";
} else {
    echo "<div style='width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #003641, #00AE9D); display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: bold; margin: 0 auto 10px;'>M</div>";
}

echo "<h4 style='margin: 10px 0; color: #003641;'>" . htmlspecialchars($maycon['nome']) . "</h4>";
echo "<div style='font-size: 18px; font-weight: bold; color: #00AE9D;'>" . $maycon['atualizados_total'] . "/" . $maycon['meta_total'] . "</div>";
echo "<div style='color: #003641;'>" . $maycon['progresso_total'] . "%</div>";
echo "</div>";

echo "<hr>";
echo "<p><a href='dashboard.php'>🚀 Dashboard</a> | <a href='debug_api_intranet.php'>🔧 Debug API</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
