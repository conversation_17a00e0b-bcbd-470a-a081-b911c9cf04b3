<?php
// Dashboard público - sem autenticação necessária
require_once '../config/config.php';
require_once '../classes/Logger.php';
require_once 'config_api.php';

$logger = new MciLogger();
$intranetAPI = getIntranetAPI($logger);

// Verificar se a tabela mci_metas existe
$tabela_metas_existe = false;
$erro_tabela_metas = '';
try {
    $stmt_check = $pdo_mci->prepare("SHOW TABLES LIKE 'mci_metas'");
    $stmt_check->execute();
    $tabela_metas_existe = $stmt_check->fetch() !== false;
} catch (PDOException $e) {
    $erro_tabela_metas = $e->getMessage();
}

// Se a tabela não existe, mostrar página de erro
if (!$tabela_metas_existe) {
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>MCI - CADASTRO</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            :root {
                --sicoob-verde-escuro: #003641;
                --sicoob-turquesa: #00AE9D;
            }
            body { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); min-height: 100vh; }
            .setup-container { display: flex; align-items: center; justify-content: center; min-height: 100vh; }
            .setup-card { max-width: 600px; border: none; border-radius: 15px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); }
            .setup-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; border-radius: 15px 15px 0 0; }
        </style>
    </head>
    <body>
        <div class="setup-container">
            <div class="card setup-card">
                <div class="card-header setup-header text-center p-4">
                    <h3><i class="fas fa-cogs"></i> Configuração Necessária</h3>
                    <p class="mb-0">MCI - CADASTRO</p>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> Tabela de Metas Não Encontrada</h5>
                        <p>A tabela <code>mci_metas</code> não existe no banco de dados. Esta tabela é necessária para o funcionamento do dashboard.</p>
                    </div>

                    <h6><i class="fas fa-tools"></i> Como resolver:</h6>
                    <ol>
                        <li>Execute o script de criação da tabela de metas</li>
                        <li>Aguarde a conclusão da instalação</li>
                        <li>Retorne a esta página</li>
                    </ol>

                    <div class="d-grid gap-2 mt-4">
                        <a href="../criar_tabela_metas.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic"></i> Criar Tabela de Metas
                        </a>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </a>
                    </div>

                    <?php if (!empty($erro_tabela_metas)): ?>
                    <div class="alert alert-danger mt-3">
                        <small><strong>Erro técnico:</strong> <?php echo htmlspecialchars($erro_tabela_metas); ?></small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Buscar porcentagem de meta atual
$porcentagem_meta = 75.00; // Valor padrão
try {
    $stmt_meta = $pdo_mci->prepare("SELECT porcentagem_meta FROM mci_metas WHERE ativo = TRUE ORDER BY id DESC LIMIT 1");
    $stmt_meta->execute();
    $meta_config = $stmt_meta->fetch();
    $porcentagem_meta = $meta_config ? $meta_config['porcentagem_meta'] : 75.00;
} catch (PDOException $e) {
    $logger->logFile("Erro ao buscar meta: " . $e->getMessage(), 'WARNING');
    $porcentagem_meta = 75.00; // Fallback para valor padrão
}

// Buscar funcionários com registros
$funcionarios_ids = [17, 18, 19, 20, 21, 22]; // IDs dos funcionários do MCI
$funcionarios_dados = [];

// Cache dos usuários da intranet
$usuarios_intranet = [];
try {
    $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
    $logger->logFile("Cache de usuários da intranet criado com " . count($usuarios_intranet) . " usuários", 'INFO');
} catch (Exception $e) {
    $logger->logFile("Erro ao buscar usuários da intranet: " . $e->getMessage(), 'ERROR');
}

foreach ($funcionarios_ids as $funcionario_id) {
    // Buscar dados do funcionário
    $query_funcionario = "
        SELECT u.id, u.nome_completo, u.email
        FROM sicoob_access_control.usuarios u
        WHERE u.id = ? AND u.ativo = TRUE
    ";
    
    $stmt = $pdo_mci->prepare($query_funcionario);
    $stmt->execute([$funcionario_id]);
    $funcionario = $stmt->fetch();
    
    if ($funcionario) {
        // Calcular métricas - considerando apenas data_solicitacao_laudo para equipe de cadastro
        $query_metricas = "
            SELECT
                COUNT(r.id) as total_registros,
                SUM(CASE
                    WHEN r.data_solicitacao_laudo IS NOT NULL
                    AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                    THEN 1 ELSE 0
                END) as atualizados_ano,
                SUM(CASE
                    WHEN r.data_ultima_atualizacao_renda IS NOT NULL
                    AND DATE_FORMAT(DATE_ADD(r.data_ultima_atualizacao_renda, INTERVAL 11 MONTH), '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                    THEN 1 ELSE 0
                END) as registros_vencendo_mes,
                SUM(CASE
                    WHEN r.data_solicitacao_laudo IS NOT NULL
                    AND DATE_FORMAT(r.data_solicitacao_laudo, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                    THEN 1 ELSE 0
                END) as atualizados_mes
            FROM cad_registros r
            WHERE r.funcionario = ?
            AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        ";
        
        $stmt = $pdo_mci->prepare($query_metricas);
        $stmt->execute([$funcionario_id]);
        $metricas = $stmt->fetch();
        
        if ($metricas['total_registros'] > 0) {
            // Buscar dados da intranet pelo email
            $usuario_intranet = null;
            if (!empty($funcionario['email'])) {
                $email_key = strtolower(trim($funcionario['email']));
                $usuario_intranet = $usuarios_intranet[$email_key] ?? null;
            }
            
            // Usar nome da API Intranet quando disponível, senão usar do banco
            $nome_exibir = $funcionario['nome_completo'];
            if ($usuario_intranet && !empty($usuario_intranet['nome'])) {
                $nome_exibir = $usuario_intranet['nome'];
            }

            $funcionarios_dados[$funcionario_id] = [
                'id' => $funcionario_id,
                'nome' => $nome_exibir,
                'email' => $funcionario['email'],
                'foto_url' => $usuario_intranet['foto_url'] ?? null,
                'setor' => $usuario_intranet['setor_nome'] ?? null,
                'funcao' => $usuario_intranet['funcao_nome'] ?? null,
                'total_registros' => $metricas['total_registros'],
                'meta_anual' => ceil($metricas['total_registros'] * $porcentagem_meta / 100),
                'atualizados_ano' => $metricas['atualizados_ano'],
                'registros_vencendo_mes' => $metricas['registros_vencendo_mes'],
                'atualizados_mes' => $metricas['atualizados_mes'],
                'meta_mensal' => ceil($metricas['registros_vencendo_mes'] * $porcentagem_meta / 100),
                'progresso_anual' => ceil($metricas['total_registros'] * $porcentagem_meta / 100) > 0 ? 
                    round(($metricas['atualizados_ano'] / ceil($metricas['total_registros'] * $porcentagem_meta / 100)) * 100, 1) : 0,
                'progresso_mensal' => ceil($metricas['registros_vencendo_mes'] * $porcentagem_meta / 100) > 0 ? 
                    round(($metricas['atualizados_mes'] / ceil($metricas['registros_vencendo_mes'] * $porcentagem_meta / 100)) * 100, 1) : 0
            ];
        }
    }
}

// Calcular métricas gerais
$total_meta_anual = array_sum(array_column($funcionarios_dados, 'meta_anual'));
$total_atualizados_ano = array_sum(array_column($funcionarios_dados, 'atualizados_ano'));
$total_meta_mensal = array_sum(array_column($funcionarios_dados, 'meta_mensal'));
$total_atualizados_mes = array_sum(array_column($funcionarios_dados, 'atualizados_mes'));

$progresso_geral_anual = $total_meta_anual > 0 ? round(($total_atualizados_ano / $total_meta_anual) * 100, 1) : 0;
$progresso_geral_mensal = $total_meta_mensal > 0 ? round(($total_atualizados_mes / $total_meta_mensal) * 100, 1) : 0;

// Calcular segmentos da barra de progresso por funcionário
$cores_funcionarios = ['#00AE9D', '#003641', '#6c757d', '#C9D200', '#70B86C', '#494790'];
$segmentos_progresso = [];
$funcionario_index_calc = 0;

foreach ($funcionarios_dados as $funcionario) {
    if ($total_meta_anual > 0) {
        $porcentagem_contribuicao = ($funcionario['atualizados_ano'] / $total_meta_anual) * 100;
        $segmentos_progresso[] = [
            'porcentagem' => $porcentagem_contribuicao,
            'cor' => $cores_funcionarios[$funcionario_index_calc % count($cores_funcionarios)],
            'nome' => $funcionario['nome'],
            'atualizados' => $funcionario['atualizados_ano']
        ];
    }
    $funcionario_index_calc++;
}

// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCI - CADASTRO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;

            /* Cores da identidade visual para funcionários */
            --cor-funcionario-1: #00AE9D; /* Turquesa */
            --cor-funcionario-2: #003641; /* Verde escuro */
            --cor-funcionario-3: #6c757d; /* Cinza (substitui branco) */
            --cor-funcionario-4: #C9D200; /* Verde claro */
            --cor-funcionario-5: #70B86C; /* Verde médio */
            --cor-funcionario-6: #494790; /* Roxo */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden; /* Eliminar scroll */
            height: 100vh;
            margin: 0;
            padding: 0;
        }

        .dashboard-container {
            padding: 0.5rem;
            height: 100vh;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: white;
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .dashboard-header h1 {
            font-size: clamp(1.5rem, 3vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .dashboard-header .subtitle {
            font-size: clamp(0.8rem, 1.5vw, 1.1rem);
            opacity: 0.9;
        }

        .metrics-overview {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 0.5rem;
            flex-shrink: 0;
        }

        .overview-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .overview-card:hover {
            transform: translateY(-2px);
        }

        .overview-card h3 {
            color: var(--sicoob-verde-escuro);
            font-size: clamp(1rem, 2vw, 1.4rem);
            margin-bottom: 0.5rem;
        }

        .overview-value {
            font-size: clamp(1.5rem, 3vw, 2rem);
            font-weight: 700;
            color: var(--sicoob-turquesa);
            margin-bottom: 0.5rem;
        }

        .overview-progress {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            color: #6c757d;
            margin-bottom: 1rem;
        }

        .progress-bar-overview {
            height: 12px;
            background-color: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            display: flex;
        }

        .progress-segment {
            height: 100%;
            transition: width 0.8s ease;
            position: relative;
        }

        .progress-segment:first-child {
            border-radius: 6px 0 0 6px;
        }

        .progress-segment:last-child {
            border-radius: 0 6px 6px 0;
        }

        .progress-segment:only-child {
            border-radius: 6px;
        }

        .funcionarios-grid {
            display: flex;
            flex-direction: row;
            gap: 0.8rem;
            flex: 1;
            overflow: visible;
            justify-content: space-evenly;
            align-items: stretch;
            padding-top: clamp(15px, 3vw, 25px);
            padding-bottom: clamp(10px, 2vw, 20px);
            height: 100%;
        }

        .funcionario-card {
            background: white;
            border-radius: 16px;
            padding: clamp(5rem, 7vh, 7rem) clamp(0.8rem, 1.5vw, 1.2rem) clamp(1rem, 2vh, 1.5rem);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            border-top: 3px solid var(--cor-funcionario, var(--sicoob-turquesa));
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            text-align: center;
            flex: 1;
            min-width: 0;
            max-width: calc(100% / 6 - 0.8rem);
            overflow: visible;
            gap: clamp(0.5rem, 1.5vh, 1.5rem);
            margin-top: clamp(20px, 4vw, 40px);
        }

        .funcionario-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            padding: 2px;
            background: linear-gradient(135deg,
                var(--cor-funcionario, var(--sicoob-turquesa)) 0%,
                transparent 50%,
                var(--cor-funcionario, var(--sicoob-turquesa)) 100%);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            opacity: 0.3;
            z-index: -1;
        }

        .funcionario-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-top-color: var(--cor-funcionario, var(--sicoob-turquesa));
        }

        .funcionario-card:hover::before {
            opacity: 0.5;
        }

        .funcionario-avatar-container {
            position: absolute;
            top: clamp(-25px, -4vw, -40px);
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            flex-shrink: 0;
            margin: 0;
        }

        .funcionario-avatar {
            width: clamp(60px, 8vw, 100px);
            height: clamp(60px, 8vw, 100px);
            border-radius: 50%;
            object-fit: cover;
            position: relative;
            z-index: 2;
            border: 3px solid white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .funcionario-avatar.placeholder {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: clamp(1rem, 3.5vw, 2.2rem);
            font-weight: 700;
            border: 3px solid white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .progress-ring {
            position: absolute;
            top: -6px;
            left: -6px;
            width: calc(clamp(60px, 8vw, 100px) + 12px);
            height: calc(clamp(60px, 8vw, 100px) + 12px);
            border-radius: 50%;
            background: conic-gradient(
                var(--cor-funcionario, var(--sicoob-turquesa)) 0deg,
                var(--cor-funcionario, var(--sicoob-turquesa)) var(--progress-angle, 0deg),
                #e9ecef var(--progress-angle, 0deg),
                #e9ecef 360deg
            );
            z-index: 1;
        }

        .progress-ring::before {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            width: clamp(60px, 8vw, 100px);
            height: clamp(60px, 8vw, 100px);
            border-radius: 50%;
            background: white;
        }

        .funcionario-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;
            width: 100%;
            text-align: center;
            padding: 0;
            height: 100%;
        }

        .funcionario-info {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .funcionario-info h4 {
            color: var(--sicoob-verde-escuro);
            font-size: clamp(0.9rem, 1.8vw, 1.3rem);
            margin: 0;
            font-weight: 600;
            line-height: 1.2;
            word-wrap: break-word;
            hyphens: auto;
            text-align: center;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            width: 100%;
        }

        .metric-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;
            width: 100%;
            height: 100%;
            gap: clamp(0.5rem, 1vh, 1rem);
        }

        .metric-section {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(0.2rem, 0.5vh, 0.4rem);
        }

        .metric-label {
            font-size: clamp(0.7rem, 1.5vw, 0.9rem);
            color: var(--sicoob-verde-escuro);
            font-weight: 500;
            margin: 0;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 0.8;
        }

        .metric-value-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(0.1rem, 0.3vh, 0.2rem);
        }

        .metric-value {
            font-size: clamp(1.4rem, 3.5vw, 2.5rem);
            font-weight: 700;
            color: var(--cor-funcionario, var(--sicoob-turquesa));
            margin: 0;
            line-height: 1;
            text-align: center;
            width: 100%;
        }

        .metric-progress-bar {
            width: 100%;
            height: clamp(4px, 0.8vh, 8px);
            background-color: #e9ecef;
            border-radius: clamp(2px, 0.4vh, 4px);
            overflow: hidden;
            position: relative;
        }

        .metric-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--cor-funcionario, var(--sicoob-turquesa)), var(--sicoob-verde-escuro));
            border-radius: clamp(2px, 0.4vh, 4px);
            transition: width 0.8s ease-in-out;
            position: relative;
        }

        .metric-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .metric-percentage {
            font-size: clamp(1rem, 2.5vw, 1.6rem);
            font-weight: 600;
            color: var(--sicoob-verde-escuro);
            margin: 0;
            line-height: 1;
            text-align: center;
            width: 100%;
        }

        .metric-stats {
            display: flex;
            justify-content: space-between;
            width: 100%;
            font-size: clamp(0.6rem, 1.2vw, 0.8rem);
            color: var(--sicoob-verde-escuro);
            opacity: 0.7;
            font-weight: 500;
        }

        /* Classes específicas para cada funcionário */
        .funcionario-1 { --cor-funcionario: var(--cor-funcionario-1); }
        .funcionario-2 { --cor-funcionario: var(--cor-funcionario-2); }
        .funcionario-3 { --cor-funcionario: var(--cor-funcionario-3); }
        .funcionario-4 { --cor-funcionario: var(--cor-funcionario-4); }
        .funcionario-5 { --cor-funcionario: var(--cor-funcionario-5); }
        .funcionario-6 { --cor-funcionario: var(--cor-funcionario-6); }



        /* Responsividade para TV - Layout sempre 3x2 */
        @media (min-width: 1920px) {
            .dashboard-container {
                padding: 1.2rem;
            }
        }

        @media (min-width: 3840px) {
            .dashboard-container {
                padding: 1.5rem;
            }
        }

        /* Ajustes para telas pequenas */
        @media (max-width: 1200px) {
            .funcionarios-grid {
                gap: 0.6rem;
                padding-top: clamp(12px, 2.5vh, 20px);
                padding-bottom: clamp(10px, 1.5vh, 20px);
            }

            .funcionario-card {
                max-width: calc(100% / 6 - 0.6rem);
                margin-top: clamp(18px, 3vh, 30px);
                padding: clamp(4rem, 6vh, 6rem) clamp(0.8rem, 1.5vw, 1.2rem) clamp(1rem, 1.8vh, 1.4rem);
            }

            .metric-value {
                font-size: clamp(1.2rem, 3vw, 2rem);
            }

            .metric-percentage {
                font-size: clamp(0.9rem, 2.2vw, 1.4rem);
            }
        }

        /* Ajustes para tablets */
        @media (max-width: 768px) {
            .funcionarios-grid {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
                padding-top: clamp(10px, 2vh, 20px);
                padding-bottom: clamp(10px, 1.5vh, 20px);
            }

            .funcionario-card {
                max-width: calc(50% - 0.5rem);
                padding: clamp(4rem, 5vh, 5rem) clamp(0.6rem, 1.2vw, 1rem) clamp(0.8rem, 1.5vh, 1.2rem);
                margin-top: clamp(15px, 2.5vh, 25px);
                flex: 1 1 calc(50% - 0.5rem);
            }

            .funcionario-avatar-container {
                top: clamp(-20px, -3vh, -30px);
            }

            .metric-value {
                font-size: clamp(1rem, 2.5vw, 1.6rem);
            }

            .metric-percentage {
                font-size: clamp(0.8rem, 2vw, 1.2rem);
            }

            .metric-stats {
                font-size: clamp(0.5rem, 1vw, 0.7rem);
            }
        }

        /* Ajustes para telas muito pequenas */
        @media (max-width: 480px) {
            .dashboard-container {
                height: 100vh;
                overflow: hidden;
            }

            .funcionarios-grid {
                flex-direction: column;
                align-items: center;
                padding-top: clamp(8px, 1.5vh, 15px);
                padding-bottom: clamp(10px, 1vh, 15px);
                gap: clamp(0.3rem, 1vh, 0.8rem);
                overflow: hidden;
            }

            .funcionario-card {
                max-width: 100%;
                padding: clamp(3rem, 4vh, 4rem) clamp(0.8rem, 1.5vw, 1.2rem) clamp(0.8rem, 1.5vh, 1.2rem);
                margin-top: clamp(8px, 1vh, 15px);
                flex: 1;
                min-height: 0;
            }

            .funcionario-avatar-container {
                top: clamp(-18px, -2vh, -25px);
            }

            .metric-value {
                font-size: clamp(0.9rem, 2vw, 1.3rem);
            }

            .metric-percentage {
                font-size: clamp(0.7rem, 1.8vw, 1rem);
            }

            .metric-stats {
                font-size: clamp(0.4rem, 0.8vw, 0.6rem);
            }

            .metric-label {
                font-size: clamp(0.6rem, 1.2vw, 0.8rem);
            }
        }

        /* Auto-refresh animation */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .refreshing {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="dashboard-header">
            <h1><i class="fas fa-chart-line"></i> MCI - CADASTRO</h1>
            <div class="subtitle">Antecipação Cadastral de Produtores Rurais - Monitoramento em Tempo Real</div>
        </div>

        <!-- Métricas Gerais -->
        <div class="metrics-overview">
            <div class="overview-card">
                <h3><i class="fas fa-calendar-check"></i> <strong>META ANUAL DA EQUIPE</strong></h3>
                <div class="overview-value"><?php echo number_format($total_meta_anual); ?></div>
                <div class="overview-progress"><?php echo $total_atualizados_ano; ?> concluídos (<?php echo $progresso_geral_anual; ?>%)</div>
                <div class="progress-bar-overview">
                    <?php foreach ($segmentos_progresso as $segmento): ?>
                        <div class="progress-segment"
                             style="width: <?php echo $segmento['porcentagem']; ?>%; background-color: <?php echo $segmento['cor']; ?>;"
                             title="<?php echo htmlspecialchars($segmento['nome']); ?>: <?php echo $segmento['atualizados']; ?> atualizações">
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Grid de Funcionários -->
        <div class="funcionarios-grid">
            <?php
            $funcionario_index = 1;
            foreach ($funcionarios_dados as $funcionario):
            ?>
            <div class="funcionario-card funcionario-<?php echo $funcionario_index; ?>">
                <!-- Lado Esquerdo: Avatar -->
                <div class="funcionario-avatar-container">
                    <div class="progress-ring" style="--progress-angle: <?php echo ($funcionario['progresso_anual'] * 3.6); ?>deg;"></div>
                    <?php if ($funcionario['foto_url']): ?>
                        <img src="<?php echo htmlspecialchars($funcionario['foto_url']); ?>"
                             alt="<?php echo htmlspecialchars($funcionario['nome']); ?>"
                             class="funcionario-avatar">
                    <?php else: ?>
                        <div class="funcionario-avatar placeholder">
                            <?php echo strtoupper(substr($funcionario['nome'], 0, 1)); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Lado Direito: Informações -->
                <div class="funcionario-content">
                    <div class="funcionario-info">
                        <h4><?php echo htmlspecialchars($funcionario['nome']); ?></h4>
                    </div>

                    <div class="metric-info">
                        <!-- Seção de Meta -->
                        <div class="metric-section">
        
                            <div class="metric-value-container">
                                <div class="metric-value"><?php echo $funcionario['atualizados_ano']; ?>/<?php echo $funcionario['meta_anual']; ?></div>
                                <div class="metric-progress-bar">
                                    <div class="metric-progress-fill" style="width: <?php echo $funcionario['progresso_anual']; ?>%;"></div>
                                </div>
                                <div class="metric-stats">
                                    <span>Concluídos: <?php echo $funcionario['atualizados_ano']; ?> |</span>
                                    <span>| Restantes: <?php echo ($funcionario['meta_anual'] - $funcionario['atualizados_ano']); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Seção de Performance -->
                        <div class="metric-section">
                            <div class="metric-label">Performance</div>
                            <div class="metric-percentage"><?php echo $funcionario['progresso_anual']; ?>%</div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            $funcionario_index++;
            endforeach;
            ?>
        </div>
    </div>

    <script>
        // Auto-refresh da página a cada 30 segundos
        let refreshTimer = setTimeout(() => {
            document.body.classList.add('refreshing');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }, 30000); // 30 segundos

        // Pausar refresh quando a página não está visível
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                clearTimeout(refreshTimer);
            } else {
                refreshTimer = setTimeout(() => {
                    document.body.classList.add('refreshing');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }, 30000); // 30 segundos
            }
        });

        // Animação de entrada para os cards
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.funcionario-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
