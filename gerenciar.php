<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

$logger = new MciLogger();
$message = '';
$error = '';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_registro':
                $id = $_POST['id'];
                $funcionario = !empty($_POST['funcionario']) ? $_POST['funcionario'] : null;
                $data_solicitacao_laudo = !empty($_POST['data_solicitacao_laudo']) ? $_POST['data_solicitacao_laudo'] : null;
                $tecnico_responsavel = !empty($_POST['tecnico_responsavel']) ? $_POST['tecnico_responsavel'] : null;
                $data_atual_sisbr = !empty($_POST['data_atual_sisbr']) ? $_POST['data_atual_sisbr'] : null;
                $observacoes = $_POST['observacoes'] ?? '';

                // Determinar status automaticamente baseado nos campos preenchidos
                $status = null;
                if ($data_atual_sisbr) {
                    // Se preencheu SISBR, status = Atualizado
                    $stmt_status = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = 'Atualizado' LIMIT 1");
                    $stmt_status->execute();
                    $status_atualizado = $stmt_status->fetch();
                    $status = $status_atualizado['id'] ?? null;
                } elseif ($data_solicitacao_laudo && $tecnico_responsavel) {
                    // Se preencheu Laudo + Técnico (mas não SISBR), status = Solicitado
                    $stmt_status = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = 'Solicitado' LIMIT 1");
                    $stmt_status->execute();
                    $status_solicitado = $stmt_status->fetch();
                    $status = $status_solicitado['id'] ?? null;
                } else {
                    // Se nenhum campo preenchido, status = Pendente
                    $stmt_status = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = 'Pendente' LIMIT 1");
                    $stmt_status->execute();
                    $status_pendente = $stmt_status->fetch();
                    $status = $status_pendente['id'] ?? null;
                }

                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros
                    SET funcionario = ?,
                        data_solicitacao_laudo = ?,
                        tecnico_responsavel = ?,
                        data_atual_sisbr = ?,
                        status = ?,
                        observacoes = ?,
                        usuario_atualizacao = ?,
                        data_atualizacao = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $funcionario,
                    $data_solicitacao_laudo,
                    $tecnico_responsavel,
                    $data_atual_sisbr,
                    $status,
                    $observacoes,
                    $_SESSION['user_id'],
                    $id
                ]);

                // Log detalhado para sistema centralizado
                $detalhes_log = "MCI - Atualização de registro ID: $id";
                if ($funcionario) {
                    $stmt_func = $pdo_sicoob->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
                    $stmt_func->execute([$funcionario]);
                    $func_nome = $stmt_func->fetchColumn();
                    if ($func_nome) $detalhes_log .= " | Funcionário: $func_nome";
                }
                if ($data_solicitacao_laudo) $detalhes_log .= " | Solicitação Laudo: " . date('d/m/Y', strtotime($data_solicitacao_laudo));
                if ($tecnico_responsavel) {
                    $stmt_tec = $pdo_sicoob->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
                    $stmt_tec->execute([$tecnico_responsavel]);
                    $tec_nome = $stmt_tec->fetchColumn();
                    if ($tec_nome) $detalhes_log .= " | Técnico: $tec_nome";
                }
                if ($data_atual_sisbr) $detalhes_log .= " | SISBR: " . date('d/m/Y', strtotime($data_atual_sisbr));

                $logger->log('MCI - Atualização de registro', $detalhes_log, $id);
                $message = 'Registro atualizado com sucesso!';
                break;

            case 'update_status':
                $id = $_POST['id'];
                $status = $_POST['status'];
                $observacoes = $_POST['observacoes'] ?? '';
                
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros 
                    SET status = ?, observacoes = ?, usuario_atualizacao = ?, data_atualizacao = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$status, $observacoes, $_SESSION['user_id'], $id]);
                
                // Log detalhado para sistema centralizado
                $stmt_status_nome = $pdo_mci->prepare("SELECT nome FROM cad_status WHERE id = ?");
                $stmt_status_nome->execute([$status]);
                $status_nome = $stmt_status_nome->fetchColumn();

                $detalhes_log = "MCI - Atualização de status do registro ID: $id para '$status_nome'";
                if ($observacoes) $detalhes_log .= " | Observações: $observacoes";

                $logger->log('MCI - Atualização de status', $detalhes_log, $id);
                $message = 'Status atualizado com sucesso!';
                break;
                
            case 'remove':
                $id = $_POST['id'];
                $justificativa = $_POST['justificativa'] ?? '';

                if (empty($justificativa)) {
                    throw new Exception('Justificativa é obrigatória para remoção');
                }

                // Buscar dados do registro para log
                $stmt = $pdo_mci->prepare("SELECT nome_cliente, numero_cpf_cnpj FROM cad_registros WHERE id = ?");
                $stmt->execute([$id]);
                $registro = $stmt->fetch();

                // Buscar ID do status "Removido"
                $stmt_status = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = 'Removido' AND ativo = TRUE LIMIT 1");
                $stmt_status->execute();
                $status_removido = $stmt_status->fetch();

                if (!$status_removido) {
                    // Criar status "Removido" se não existir
                    $stmt_create = $pdo_mci->prepare("INSERT INTO cad_status (nome, cor, ativo) VALUES ('Removido', '#dc3545', TRUE)");
                    $stmt_create->execute();
                    $status_removido_id = $pdo_mci->lastInsertId();
                } else {
                    $status_removido_id = $status_removido['id'];
                }

                // Atualizar registro para status "Removido"
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros
                    SET status = ?, observacoes = ?, usuario_atualizacao = ?, data_atualizacao = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$status_removido_id, $justificativa, $_SESSION['user_id'], $id]);

                // Log detalhado para sistema centralizado
                $detalhes_log = "MCI - Remoção de registro: {$registro['nome_cliente']} (CPF/CNPJ: {$registro['numero_cpf_cnpj']}) | Justificativa: $justificativa";

                $logger->log('MCI - Remoção de registro', $detalhes_log, $id);
                $message = 'Registro removido com sucesso!';
                break;

            case 'reativar':
                $id = $_POST['id'];

                // Buscar dados do registro para log
                $stmt = $pdo_mci->prepare("SELECT nome_cliente, numero_cpf_cnpj FROM cad_registros WHERE id = ?");
                $stmt->execute([$id]);
                $registro = $stmt->fetch();

                // Buscar ID do status "Pendente" para reativação
                $stmt_status = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = 'Pendente' AND ativo = TRUE LIMIT 1");
                $stmt_status->execute();
                $status_pendente = $stmt_status->fetch();

                if (!$status_pendente) {
                    throw new Exception('Status "Pendente" não encontrado');
                }

                // Atualizar registro para status "Pendente" e limpar observações de remoção
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros
                    SET status = ?, observacoes = '', usuario_atualizacao = ?, data_atualizacao = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$status_pendente['id'], $_SESSION['user_id'], $id]);

                // Log detalhado para sistema centralizado
                $detalhes_log = "MCI - Reativação de registro: {$registro['nome_cliente']} (CPF/CNPJ: {$registro['numero_cpf_cnpj']})";

                $logger->log('MCI - Reativação de registro', $detalhes_log, $id);
                $message = 'Registro reativado com sucesso!';
                break;

            case 'delete':
                $id = $_POST['id'];

                // Buscar dados do registro para log
                $stmt = $pdo_mci->prepare("SELECT nome_cliente, numero_cpf_cnpj FROM cad_registros WHERE id = ?");
                $stmt->execute([$id]);
                $registro = $stmt->fetch();

                $stmt = $pdo_mci->prepare("DELETE FROM cad_registros WHERE id = ?");
                $stmt->execute([$id]);

                // Log detalhado para sistema centralizado
                $detalhes_log = "MCI - Exclusão permanente de registro: {$registro['nome_cliente']} (CPF/CNPJ: {$registro['numero_cpf_cnpj']})";

                $logger->log('MCI - Exclusão de registro', $detalhes_log, $id);
                $message = 'Registro excluído com sucesso!';
                break;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();

        // Log de erro para sistema centralizado
        $detalhes_erro = "MCI - Erro ao processar ação: " . $error;
        if (isset($_POST['action'])) {
            $detalhes_erro .= " | Ação: " . $_POST['action'];
        }
        if (isset($_POST['id'])) {
            $detalhes_erro .= " | Registro ID: " . $_POST['id'];
        }

        $logger->logFile("Erro ao processar ação: " . $error, 'ERROR');
        $logger->logDatabase('MCI - Erro de sistema', $detalhes_erro);
    }
}

// Filtros e ordenação
$filtro_status = $_GET['status'] ?? '';
$filtro_pa = $_GET['pa'] ?? '';
$filtro_associado = $_GET['associado'] ?? '';
$filtro_funcionario = $_GET['funcionario'] ?? '';
$filtro_tecnico = $_GET['tecnico'] ?? '';
$filtro_mes_renda = $_GET['mes_renda'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Parâmetros de ordenação
$sort_column = $_GET['sort'] ?? 'data_cadastro';
$sort_direction = $_GET['dir'] ?? 'desc';

// Validar coluna de ordenação
$valid_columns = [
    'pa' => 'CAST(r.pa AS UNSIGNED)',
    'nome_cliente' => 'r.nome_cliente',
    'data_ultima_atualizacao_renda' => 'r.data_ultima_atualizacao_renda',
    'funcionario' => 'u3.nome_completo',
    'data_solicitacao_laudo' => 'r.data_solicitacao_laudo',
    'tecnico_responsavel' => 'u4.nome_completo',
    'data_atual_sisbr' => 'r.data_atual_sisbr',
    'status' => 's.nome',
    'data_cadastro' => 'r.data_cadastro'
];

$order_column = $valid_columns[$sort_column] ?? $valid_columns['data_cadastro'];
$order_direction = strtolower($sort_direction) === 'asc' ? 'ASC' : 'DESC';

// Construir query com filtros
$where_conditions = [];
$params = [];

if ($filtro_status) {
    $where_conditions[] = "r.status = ?";
    $params[] = $filtro_status;
}

if ($filtro_pa !== '' && $filtro_pa !== null) {
    $where_conditions[] = "r.pa = ?";
    $params[] = $filtro_pa;
}

if ($filtro_associado) {
    $where_conditions[] = "(r.nome_cliente LIKE ? OR r.numero_cpf_cnpj LIKE ?)";
    $params[] = "%$filtro_associado%";
    $params[] = "%$filtro_associado%";
}

if ($filtro_funcionario) {
    $where_conditions[] = "(r.funcionario = ? OR u3.nome_completo LIKE ?)";
    $params[] = $filtro_funcionario;
    $params[] = "%$filtro_funcionario%";
}

if ($filtro_tecnico) {
    $where_conditions[] = "(r.tecnico_responsavel = ? OR u4.nome_completo LIKE ?)";
    $params[] = $filtro_tecnico;
    $params[] = "%$filtro_tecnico%";
}

if ($filtro_mes_renda) {
    $where_conditions[] = "DATE_FORMAT(r.data_ultima_atualizacao_renda, '%Y-%m') = ?";
    $params[] = $filtro_mes_renda;
}

// Adicionar filtro para excluir registros removidos (exceto quando filtrado especificamente)
if ($filtro_status !== '' && $filtro_status !== null) {
    // Se está filtrando por status específico, não adicionar filtro de removidos
} else {
    // Se não está filtrando por status, excluir registros removidos
    $where_conditions[] = "r.status != (SELECT id FROM cad_status WHERE nome = 'Removido' LIMIT 1)";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Contar total de registros
$count_query = "
    SELECT COUNT(*)
    FROM cad_registros r
    LEFT JOIN cad_status s ON r.status = s.id
    LEFT JOIN sicoob_access_control.usuarios u1 ON r.usuario_cadastro = u1.id
    LEFT JOIN sicoob_access_control.usuarios u2 ON r.usuario_atualizacao = u2.id
    LEFT JOIN sicoob_access_control.usuarios u3 ON r.funcionario = u3.id
    LEFT JOIN sicoob_access_control.usuarios u4 ON r.tecnico_responsavel = u4.id
    LEFT JOIN sicoob_access_control.pontos_atendimento p ON r.pa COLLATE utf8mb4_general_ci = p.numero COLLATE utf8mb4_general_ci
    $where_clause
";
$stmt = $pdo_mci->prepare($count_query);
$stmt->execute($params);
$total_records = $stmt->fetchColumn();
$total_pages = ceil($total_records / $per_page);

// Buscar registros
$query = "
    SELECT r.*, s.nome as status_nome, s.cor as status_cor,
           u1.nome_completo as usuario_cadastro_nome, u2.nome_completo as usuario_atualizacao_nome,
           u3.nome_completo as funcionario_nome, u4.nome_completo as tecnico_nome,
           p.nome as nome_ponto_atendimento
    FROM cad_registros r
    LEFT JOIN cad_status s ON r.status = s.id
    LEFT JOIN sicoob_access_control.usuarios u1 ON r.usuario_cadastro = u1.id
    LEFT JOIN sicoob_access_control.usuarios u2 ON r.usuario_atualizacao = u2.id
    LEFT JOIN sicoob_access_control.usuarios u3 ON r.funcionario = u3.id
    LEFT JOIN sicoob_access_control.usuarios u4 ON r.tecnico_responsavel = u4.id
    LEFT JOIN sicoob_access_control.pontos_atendimento p ON r.pa COLLATE utf8mb4_general_ci = p.numero COLLATE utf8mb4_general_ci
    $where_clause
    ORDER BY $order_column $order_direction
    LIMIT $per_page OFFSET $offset
";

$stmt = $pdo_mci->prepare($query);
$stmt->execute($params);
$registros = $stmt->fetchAll();

// Buscar PAs únicos para filtro com informações dos pontos de atendimento
$stmt = $pdo_mci->prepare("
    SELECT DISTINCT r.pa, p.nome as nome_ponto
    FROM cad_registros r
    LEFT JOIN sicoob_access_control.pontos_atendimento p ON r.pa COLLATE utf8mb4_general_ci = p.numero COLLATE utf8mb4_general_ci
    WHERE r.pa IS NOT NULL AND r.pa != ''
    ORDER BY CAST(r.pa AS UNSIGNED), r.pa
");
$stmt->execute();
$pas_disponiveis = $stmt->fetchAll();

// Buscar funcionários únicos para filtro
$stmt = $pdo_mci->prepare("
    SELECT DISTINCT r.funcionario, u.nome_completo
    FROM cad_registros r
    LEFT JOIN sicoob_access_control.usuarios u ON r.funcionario = u.id
    WHERE r.funcionario IS NOT NULL
    ORDER BY u.nome_completo, r.funcionario
");
$stmt->execute();
$funcionarios_disponiveis = $stmt->fetchAll();

// Buscar técnicos únicos para filtro
$stmt = $pdo_mci->prepare("
    SELECT DISTINCT r.tecnico_responsavel, u.nome_completo
    FROM cad_registros r
    LEFT JOIN sicoob_access_control.usuarios u ON r.tecnico_responsavel = u.id
    WHERE r.tecnico_responsavel IS NOT NULL
    ORDER BY u.nome_completo, r.tecnico_responsavel
");
$stmt->execute();
$tecnicos_disponiveis = $stmt->fetchAll();

// Buscar técnicos do setor 13 para o modal
$stmt_tecnicos_setor = $pdo_sicoob->prepare("
    SELECT u.id, u.nome_completo
    FROM usuarios u
    INNER JOIN usuario_setor us ON u.id = us.usuario_id
    WHERE us.setor_id = 13 AND u.ativo = TRUE
    ORDER BY u.nome_completo
");
$stmt_tecnicos_setor->execute();
$tecnicos_setor = $stmt_tecnicos_setor->fetchAll();

// Buscar funcionários do setor 8 para o modal
$stmt_funcionarios_setor = $pdo_sicoob->prepare("
    SELECT u.id, u.nome_completo
    FROM usuarios u
    INNER JOIN usuario_setor us ON u.id = us.usuario_id
    WHERE us.setor_id = 8 AND u.ativo = TRUE
    ORDER BY u.nome_completo
");
$stmt_funcionarios_setor->execute();
$funcionarios_setor = $stmt_funcionarios_setor->fetchAll();

// Buscar meses disponíveis para filtro de última atualização de renda
$stmt_meses = $pdo_mci->prepare("
    SELECT DISTINCT DATE_FORMAT(data_ultima_atualizacao_renda, '%Y-%m') as mes_ano,
                    DATE_FORMAT(data_ultima_atualizacao_renda, '%m/%Y') as mes_ano_formatado
    FROM cad_registros
    WHERE data_ultima_atualizacao_renda IS NOT NULL
    ORDER BY mes_ano DESC
");
$stmt_meses->execute();
$meses_disponiveis = $stmt_meses->fetchAll();

// Função para gerar links de ordenação
function getSortLink($column, $label, $current_sort, $current_dir, $filters) {
    $new_dir = ($current_sort === $column && $current_dir === 'asc') ? 'desc' : 'asc';
    $icon = '';

    if ($current_sort === $column) {
        $icon = $current_dir === 'asc' ? ' <i class="fas fa-sort-up"></i>' : ' <i class="fas fa-sort-down"></i>';
    } else {
        $icon = ' <i class="fas fa-sort text-muted"></i>';
    }

    $url_params = array_merge($filters, [
        'sort' => $column,
        'dir' => $new_dir,
        'page' => 1 // Reset para primeira página ao ordenar
    ]);

    $url = '?' . http_build_query($url_params);

    return "<a href=\"$url\" class=\"text-decoration-none text-dark\">$label$icon</a>";
}

$current_filters = [
    'status' => $filtro_status,
    'pa' => $filtro_pa,
    'associado' => $filtro_associado,
    'funcionario' => $filtro_funcionario,
    'tecnico' => $filtro_tecnico,
    'mes_renda' => $filtro_mes_renda
];

// Verificar se o usuário logado tem registros atribuídos e calcular métricas pessoais
$metricas_pessoais = null;
$user_id = $_SESSION['user_id'];

// Verificar se o usuário tem registros na coluna funcionario
$stmt_check_user = $pdo_mci->prepare("
    SELECT COUNT(*) as total_registros
    FROM cad_registros r
    WHERE r.funcionario = ?
    AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
");
$stmt_check_user->execute([$user_id]);
$user_has_records = $stmt_check_user->fetchColumn() > 0;

if ($user_has_records) {
    // Buscar porcentagem de meta atual
    $porcentagem_meta = 75.00; // Valor padrão

    try {
        // Verificar se a tabela mci_metas existe
        $stmt_check_table = $pdo_mci->query("SHOW TABLES LIKE 'mci_metas'");

        if ($stmt_check_table->fetch()) {
            // Tabela existe, buscar meta ativa
            $stmt_meta = $pdo_mci->prepare("SELECT porcentagem_meta FROM mci_metas WHERE ativo = TRUE ORDER BY id DESC LIMIT 1");
            $stmt_meta->execute();
            $meta_config = $stmt_meta->fetch();
            $porcentagem_meta = $meta_config ? $meta_config['porcentagem_meta'] : 75.00;
        }
    } catch (PDOException $e) {
        // Em caso de erro, usar valor padrão
        $porcentagem_meta = 75.00;
    }

    // Calcular métricas pessoais usando a mesma lógica da página metas.php
    $query_metricas_pessoais = "
        SELECT
            COUNT(r.id) as total_registros,
            SUM(CASE
                WHEN r.data_solicitacao_laudo IS NOT NULL
                AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                THEN 1 ELSE 0
            END) as atualizados_ano,
            SUM(CASE
                WHEN r.data_ultima_atualizacao_renda IS NOT NULL
                AND DATE_FORMAT(DATE_ADD(r.data_ultima_atualizacao_renda, INTERVAL 11 MONTH), '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                THEN 1 ELSE 0
            END) as registros_vencendo_mes,
            SUM(CASE
                WHEN r.data_solicitacao_laudo IS NOT NULL
                AND DATE_FORMAT(r.data_solicitacao_laudo, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                THEN 1 ELSE 0
            END) as atualizados_mes
        FROM cad_registros r
        WHERE r.funcionario = ?
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";

    $stmt_metricas = $pdo_mci->prepare($query_metricas_pessoais);
    $stmt_metricas->execute([$user_id]);
    $metricas_raw = $stmt_metricas->fetch();

    if ($metricas_raw && $metricas_raw['total_registros'] > 0) {
        $metricas_pessoais = [
            'total_registros' => $metricas_raw['total_registros'],
            'meta_anual' => ceil($metricas_raw['total_registros'] * $porcentagem_meta / 100),
            'atualizados_ano' => $metricas_raw['atualizados_ano'],
            'registros_vencendo_mes' => $metricas_raw['registros_vencendo_mes'],
            'atualizados_mes' => $metricas_raw['atualizados_mes'],
            'meta_mensal' => ceil($metricas_raw['registros_vencendo_mes'] * $porcentagem_meta / 100),
            'progresso_anual' => ceil($metricas_raw['total_registros'] * $porcentagem_meta / 100) > 0 ?
                round(($metricas_raw['atualizados_ano'] / ceil($metricas_raw['total_registros'] * $porcentagem_meta / 100)) * 100, 1) : 0,
            'progresso_mensal' => ceil($metricas_raw['registros_vencendo_mes'] * $porcentagem_meta / 100) > 0 ?
                round(($metricas_raw['atualizados_mes'] / ceil($metricas_raw['registros_vencendo_mes'] * $porcentagem_meta / 100)) * 100, 1) : 0,
            'porcentagem_meta' => $porcentagem_meta
        ];
    }
}

// Função para formatar CPF/CNPJ
function formatarCpfCnpj($numero) {
    if (empty($numero)) {
        return '';
    }

    // Remove caracteres não numéricos
    $numero = preg_replace('/[^0-9]/', '', $numero);

    // Verifica se é CPF (11 dígitos) ou CNPJ (14 dígitos)
    if (strlen($numero) == 11) {
        // Formato CPF: 000.000.000-00
        return substr($numero, 0, 3) . '.' .
               substr($numero, 3, 3) . '.' .
               substr($numero, 6, 3) . '-' .
               substr($numero, 9, 2);
    } elseif (strlen($numero) == 14) {
        // Formato CNPJ: 00.000.000/0000-00
        return substr($numero, 0, 2) . '.' .
               substr($numero, 2, 3) . '.' .
               substr($numero, 5, 3) . '/' .
               substr($numero, 8, 4) . '-' .
               substr($numero, 12, 2);
    } else {
        // Se não for CPF nem CNPJ válido, retorna o número original
        return $numero;
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Registros - <?php echo MCI_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
        }

        body { background-color: #f8f9fa; }
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .btn-primary { background-color: var(--sicoob-verde-escuro); border-color: var(--sicoob-verde-escuro); }
        .btn-primary:hover { background-color: var(--sicoob-turquesa); border-color: var(--sicoob-turquesa); }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 0.35rem 0.75rem;
            font-weight: 600;
            border-radius: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Cores específicas para cada status seguindo identidade Sicoob */
        .status-pendente {
            background-color: #6c757d !important;
            color: white !important;
        }

        .status-solicitado {
            background-color: var(--sicoob-turquesa) !important;
            color: white !important;
        }

        .status-atualizado {
            background-color: var(--sicoob-verde-claro) !important;
            color: var(--sicoob-verde-escuro) !important;
        }

        .status-removido {
            background-color: #dc3545 !important;
            color: white !important;
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .table td {
            font-size: 0.9rem;
            vertical-align: middle;
        }
        
        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .table th a {
            color: inherit !important;
            text-decoration: none !important;
        }

        .table th a:hover {
            color: var(--sicoob-verde-escuro) !important;
        }

        .table th {
            cursor: pointer;
            user-select: none;
        }

        /* Botões personalizados Sicoob */
        .btn-sicoob-primary {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
        }

        .btn-sicoob-primary:hover {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white;
        }

        .btn-sicoob-secondary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white;
        }

        .btn-sicoob-secondary:hover {
            background-color: var(--sicoob-verde-medio);
            border-color: var(--sicoob-verde-medio);
            color: white;
        }

        .btn-sicoob-success {
            background-color: var(--sicoob-verde-claro);
            border-color: var(--sicoob-verde-claro);
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }

        .btn-sicoob-success:hover {
            background-color: #b8c200;
            border-color: #b8c200;
            color: var(--sicoob-verde-escuro);
        }

        .btn-sicoob-danger {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .btn-sicoob-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
            color: white;
        }

        /* Estilos para filtros compactos */
        .form-label.small {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .form-select-sm, .form-control-sm {
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
        }

        .btn-sm {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }

        /* Responsividade para filtros */
        @media (max-width: 1200px) {
            .col-lg-1 { flex: 0 0 auto; width: 10%; }
            .col-lg-2 { flex: 0 0 auto; width: 18%; }
        }

        /* Botão de download Excel - Identidade Visual Sicoob */
        .btn-excel {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-verde-escuro);
            color: white;
            font-weight: 500;
        }

        .btn-excel:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 174, 157, 0.3);
        }

        .btn-excel:focus {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }

        @media (max-width: 992px) {
            .row.g-2 { margin-bottom: 1rem; }
            .col-md-2, .col-md-3 { margin-bottom: 0.5rem; }
        }

        /* Estilos para cards de métricas pessoais */
        .personal-metrics-card {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.2s ease;
        }

        .personal-metrics-card:hover {
            transform: translateY(-2px);
        }

        .personal-metrics-card .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0.5rem 0;
        }

        .personal-metrics-card .metric-label {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .personal-metrics-card .metric-progress {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .personal-metrics-card .progress {
            height: 8px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            margin: 0.5rem 0;
        }

        .personal-metrics-card .progress-bar {
            background-color: var(--sicoob-verde-claro);
            border-radius: 4px;
        }

        .personal-metrics-section {
            margin-bottom: 2rem;
        }

        .personal-metrics-title {
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <img src="../assets/images/logo1.png" alt="Sicoob" height="40">
                <span class="ms-2">MCI - Gerenciar Registros</span>
            </a>
            <div class="navbar-nav ms-auto">
                <!-- Atalhos para Dashboards -->
                <a class="nav-link" href="cadastro/dashboard.php" target="_blank">
                    <i class="fas fa-tv"></i> Dashboard Cadastro
                </a>
                <a class="nav-link" href="tecagricola/dashboard.php" target="_blank">
                    <i class="fas fa-seedling"></i> Dashboard Técnicos
                </a>

                <?php if (in_array($_SESSION['mci_permission_level'], ['gestor', 'administrador'])): ?>
                <a class="nav-link" href="metas.php">
                    <i class="fas fa-chart-line"></i> Metas
                </a>
                <a class="nav-link" href="transferencia_massa.php">
                    <i class="fas fa-exchange-alt"></i> Transferência em Massa
                </a>
                <?php endif; ?>
                <?php if ($_SESSION['mci_permission_level'] === 'administrador'): ?>
                <a class="nav-link" href="permissions.php">
                    <i class="fas fa-shield-alt"></i> Permissões
                </a>
                <?php endif; ?>
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Mensagens -->
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Métricas Pessoais -->
        <?php if ($metricas_pessoais): ?>
        <div class="personal-metrics-section">
            <h5 class="personal-metrics-title">
                <i class="fas fa-user-chart"></i> Minhas Métricas
            </h5>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="personal-metrics-card">
                        <div class="metric-label">
                            <i class="fas fa-calendar-alt"></i> Minha Meta Mensal
                        </div>
                        <div class="metric-value"><?php echo number_format($metricas_pessoais['meta_mensal']); ?></div>
                        <div class="progress">
                            <div class="progress-bar" style="width: <?php echo min(100, $metricas_pessoais['progresso_mensal']); ?>%"></div>
                        </div>
                        <div class="metric-progress">
                            <?php echo $metricas_pessoais['atualizados_mes']; ?>/<?php echo $metricas_pessoais['meta_mensal']; ?>
                            (<?php echo $metricas_pessoais['progresso_mensal']; ?>%)
                        </div>
                        <small class="d-block mt-2" style="opacity: 0.8;">
                            <?php echo number_format($metricas_pessoais['porcentagem_meta'], 1); ?>% de <?php echo $metricas_pessoais['registros_vencendo_mes']; ?> registros que vencem este mês
                        </small>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="personal-metrics-card">
                        <div class="metric-label">
                            <i class="fas fa-calendar-check"></i> Minha Meta Anual
                        </div>
                        <div class="metric-value"><?php echo number_format($metricas_pessoais['meta_anual']); ?></div>
                        <div class="progress">
                            <div class="progress-bar" style="width: <?php echo min(100, $metricas_pessoais['progresso_anual']); ?>%"></div>
                        </div>
                        <div class="metric-progress">
                            <?php echo $metricas_pessoais['atualizados_ano']; ?>/<?php echo $metricas_pessoais['meta_anual']; ?>
                            (<?php echo $metricas_pessoais['progresso_anual']; ?>%)
                        </div>
                        <small class="d-block mt-2" style="opacity: 0.8;">
                            <?php echo number_format($metricas_pessoais['porcentagem_meta'], 1); ?>% de <?php echo $metricas_pessoais['total_registros']; ?> registros totais
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter"></i> Filtros
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-2 align-items-end">
                    <div class="col-lg-1 col-md-2">
                        <label class="form-label small">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <?php
                            // Buscar status disponíveis
                            $stmt_status = $pdo_mci->prepare("SELECT id, nome FROM cad_status WHERE ativo = TRUE ORDER BY id");
                            $stmt_status->execute();
                            $status_disponiveis = $stmt_status->fetchAll();

                            foreach ($status_disponiveis as $status_item):
                            ?>
                            <option value="<?php echo $status_item['id']; ?>" <?php echo $filtro_status == $status_item['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($status_item['nome']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-3">
                        <label class="form-label small">Associado</label>
                        <input type="text" name="associado" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filtro_associado); ?>" placeholder="Nome ou CPF...">
                    </div>
                    <div class="col-lg-1 col-md-2">
                        <label class="form-label small">PA</label>
                        <select name="pa" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <?php foreach ($pas_disponiveis as $pa_info): ?>
                            <option value="<?php echo htmlspecialchars($pa_info['pa']); ?>" <?php echo $filtro_pa == $pa_info['pa'] ? 'selected' : ''; ?>>
                                <?php
                                $pa_display = $pa_info['pa'];
                                if (!empty($pa_info['nome_ponto'])) {
                                    // Truncar nome do ponto para economizar espaço
                                    $nome_truncado = strlen($pa_info['nome_ponto']) > 10 ? substr($pa_info['nome_ponto'], 0, 10) . '...' : $pa_info['nome_ponto'];
                                    $pa_display .= ' - ' . $nome_truncado;
                                }
                                echo htmlspecialchars($pa_display);
                                ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-2">
                        <label class="form-label small">Funcionário</label>
                        <select name="funcionario" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <?php foreach ($funcionarios_disponiveis as $func): ?>
                            <option value="<?php echo htmlspecialchars($func['funcionario']); ?>" <?php echo $filtro_funcionario == $func['funcionario'] ? 'selected' : ''; ?>>
                                <?php
                                $nome_func = $func['nome_completo'] ?? $func['funcionario'];
                                // Mostrar apenas primeiro nome para economizar espaço
                                $primeiro_nome = explode(' ', $nome_func)[0];
                                echo htmlspecialchars($primeiro_nome);
                                ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-2">
                        <label class="form-label small">Técnico</label>
                        <select name="tecnico" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <?php foreach ($tecnicos_disponiveis as $tec): ?>
                            <option value="<?php echo htmlspecialchars($tec['tecnico_responsavel']); ?>" <?php echo $filtro_tecnico == $tec['tecnico_responsavel'] ? 'selected' : ''; ?>>
                                <?php
                                $nome_tec = $tec['nome_completo'] ?? $tec['tecnico_responsavel'];
                                // Mostrar apenas primeiro nome para economizar espaço
                                $primeiro_nome = explode(' ', $nome_tec)[0];
                                echo htmlspecialchars($primeiro_nome);
                                ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-2">
                        <label class="form-label small">Última Atualização de Renda</label>
                        <select name="mes_renda" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <?php foreach ($meses_disponiveis as $mes): ?>
                            <option value="<?php echo htmlspecialchars($mes['mes_ano']); ?>" <?php echo $filtro_mes_renda == $mes['mes_ano'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($mes['mes_ano_formatado']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-3">
                        <button type="submit" class="btn btn-primary btn-sm me-1">
                            <i class="fas fa-search"></i> Filtrar
                        </button>
                        <a href="gerenciar.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times"></i> Limpar
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabela de Registros -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> Registros de Atualizações Cadastrais
                    <span class="badge bg-light text-dark ms-2"><?php echo number_format($total_records); ?> registros</span>
                </h6>
                <div>
                    <button type="button" class="btn btn-excel btn-sm" onclick="downloadExcel()" title="Baixar relatório em Excel com filtros aplicados">
                        <i class="fas fa-file-excel"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th><?php echo getSortLink('pa', 'PA', $sort_column, $sort_direction, $current_filters); ?></th>
                                <th><?php echo getSortLink('nome_cliente', 'Associado', $sort_column, $sort_direction, $current_filters); ?></th>
                                <th><?php echo getSortLink('data_ultima_atualizacao_renda', 'Última Atualização de Renda', $sort_column, $sort_direction, $current_filters); ?></th>
                                <th><?php echo getSortLink('funcionario', 'Funcionário', $sort_column, $sort_direction, $current_filters); ?></th>
                                <th><?php echo getSortLink('data_solicitacao_laudo', 'Solicitação Laudo', $sort_column, $sort_direction, $current_filters); ?></th>
                                <th><?php echo getSortLink('tecnico_responsavel', 'Técnico Responsável', $sort_column, $sort_direction, $current_filters); ?></th>
                                <th><?php echo getSortLink('data_atual_sisbr', 'Atualização SISBR', $sort_column, $sort_direction, $current_filters); ?></th>
                                <th><?php echo getSortLink('status', 'Status', $sort_column, $sort_direction, $current_filters); ?></th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($registros as $registro): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($registro['pa']); ?></strong>
                                    <?php if (!empty($registro['nome_ponto_atendimento'])): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($registro['nome_ponto_atendimento']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($registro['nome_cliente']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars(formatarCpfCnpj($registro['numero_cpf_cnpj'])); ?></small>
                                </td>
                                <td>
                                    <?php
                                    if (!empty($registro['data_ultima_atualizacao_renda'])) {
                                        echo date('d/m/Y', strtotime($registro['data_ultima_atualizacao_renda']));
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    // Mostrar nome do usuário se disponível, senão mostrar texto original
                                    $funcionario_display = $registro['funcionario_nome'] ?? $registro['funcionario'] ?? '-';
                                    echo htmlspecialchars($funcionario_display);
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    if (!empty($registro['data_solicitacao_laudo'])) {
                                        echo date('d/m/Y', strtotime($registro['data_solicitacao_laudo']));
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    // Mostrar nome do usuário se disponível, senão mostrar texto original
                                    $tecnico_display = $registro['tecnico_nome'] ?? $registro['tecnico_responsavel'] ?? '-';
                                    echo htmlspecialchars($tecnico_display);
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    if (!empty($registro['data_atual_sisbr'])) {
                                        echo date('d/m/Y', strtotime($registro['data_atual_sisbr']));
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    $status_nome = $registro['status_nome'] ?? 'Indefinido';
                                    $status_class = 'status-' . strtolower(str_replace(' ', '-', $status_nome));
                                    ?>
                                    <span class="badge status-badge <?php echo $status_class; ?>">
                                        <?php echo htmlspecialchars($status_nome); ?>
                                    </span>
                                </td>
                                <td class="action-buttons">
                                    <button class="btn btn-sicoob-primary btn-sm" onclick="editarRegistro(<?php echo $registro['id']; ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sicoob-secondary btn-sm" onclick="verDetalhes(<?php echo $registro['id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if ($registro['status_nome'] === 'Removido'): ?>
                                        <button class="btn btn-sicoob-success btn-sm" onclick="reativarRegistro(<?php echo $registro['id']; ?>)">
                                            <i class="fas fa-undo"></i> Reativar
                                        </button>
                                    <?php else: ?>
                                        <button class="btn btn-sicoob-danger btn-sm" onclick="removerRegistro(<?php echo $registro['id']; ?>)">
                                            <i class="fas fa-minus-circle"></i>
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Paginação -->
        <?php if ($total_pages > 1): ?>
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $filtro_status; ?>&pa=<?php echo $filtro_pa; ?>&associado=<?php echo $filtro_associado; ?>&funcionario=<?php echo $filtro_funcionario; ?>&tecnico=<?php echo $filtro_tecnico; ?>&mes_renda=<?php echo $filtro_mes_renda; ?>&sort=<?php echo $sort_column; ?>&dir=<?php echo $sort_direction; ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $filtro_status; ?>&pa=<?php echo $filtro_pa; ?>&associado=<?php echo $filtro_associado; ?>&funcionario=<?php echo $filtro_funcionario; ?>&tecnico=<?php echo $filtro_tecnico; ?>&mes_renda=<?php echo $filtro_mes_renda; ?>&sort=<?php echo $sort_column; ?>&dir=<?php echo $sort_direction; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $filtro_status; ?>&pa=<?php echo $filtro_pa; ?>&associado=<?php echo $filtro_associado; ?>&funcionario=<?php echo $filtro_funcionario; ?>&tecnico=<?php echo $filtro_tecnico; ?>&mes_renda=<?php echo $filtro_mes_renda; ?>&sort=<?php echo $sort_column; ?>&dir=<?php echo $sort_direction; ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

    <!-- Modal Editar Registro -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit"></i> Editar Registro
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editForm" onsubmit="prepareFormSubmit()">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_registro">
                        <input type="hidden" name="id" id="edit_id">

                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Funcionário</label>
                                <div class="input-group">
                                    <select name="funcionario" id="edit_funcionario" class="form-select" disabled>
                                        <option value="">Selecione um funcionário...</option>
                                        <?php foreach ($funcionarios_setor as $funcionario): ?>
                                        <option value="<?php echo $funcionario['id']; ?>">
                                            <?php echo htmlspecialchars($funcionario['nome_completo']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if ($_SESSION['mci_permission_level'] !== 'comum'): ?>
                                    <button type="button" class="btn btn-outline-secondary" id="btn_toggle_funcionario" onclick="toggleFuncionario()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                                <?php if ($_SESSION['mci_permission_level'] === 'comum'): ?>
                                <small class="text-muted">Campo bloqueado - apenas gestores e administradores podem alterar</small>
                                <?php else: ?>
                                <small class="text-muted">Clique no botão para alterar o funcionário</small>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Solicitação Laudo</label>
                                <input type="date" name="data_solicitacao_laudo" id="edit_data_solicitacao_laudo" class="form-control">
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">Técnico Responsável</label>
                                <select name="tecnico_responsavel" id="edit_tecnico_responsavel" class="form-select">
                                    <option value="">Selecione um técnico...</option>
                                    <?php foreach ($tecnicos_setor as $tecnico): ?>
                                    <option value="<?php echo $tecnico['id']; ?>">
                                        <?php echo htmlspecialchars($tecnico['nome_completo']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <!-- Espaço reservado para manter layout -->
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">Atualização SISBR</label>
                                <input type="date" name="data_atual_sisbr" id="edit_data_atual_sisbr" class="form-control" disabled>
                                <small class="text-muted">Disponível apenas após preencher Solicitação Laudo e Técnico</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Status</label>
                                <select name="status" id="edit_status" class="form-select" disabled>
                                    <?php foreach ($status_disponiveis as $status_item): ?>
                                    <option value="<?php echo $status_item['id']; ?>">
                                        <?php echo htmlspecialchars($status_item['nome']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="text-muted">Status será atualizado automaticamente</small>
                            </div>
                        </div>

                        <div class="mt-3">
                            <label class="form-label">Observações</label>
                            <textarea name="observacoes" id="edit_observacoes" class="form-control" rows="4" placeholder="Digite observações sobre a atualização..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Detalhes -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye"></i> Detalhes do Registro
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="detailsContent">
                    <!-- Conteúdo carregado via AJAX -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Confirmar Remoção -->
    <div class="modal fade" id="removeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-warning">
                        <i class="fas fa-exclamation-triangle"></i> Confirmar Remoção
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="removeForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="remove">
                        <input type="hidden" name="id" id="remove_id">

                        <p>Tem certeza que deseja remover este registro?</p>
                        <p class="text-muted">O registro será marcado como "Removido" e não aparecerá na listagem padrão.</p>

                        <div class="mt-3">
                            <label class="form-label">Justificativa <span class="text-danger">*</span></label>
                            <textarea name="justificativa" id="remove_justificativa" class="form-control" rows="3" placeholder="Digite a justificativa para a remoção..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-minus-circle"></i> Remover
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Confirmar Reativação -->
    <div class="modal fade" id="reactivateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-success">
                        <i class="fas fa-undo"></i> Confirmar Reativação
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja reativar este registro?</p>
                    <p class="text-muted">O registro voltará para o status "Pendente" e aparecerá na listagem principal.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="reativar">
                        <input type="hidden" name="id" id="reactivate_id">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-undo"></i> Reativar
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Confirmar Exclusão -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle"></i> Confirmar Exclusão
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir este registro?</p>
                    <p class="text-muted">Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="delete_id">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Excluir
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editarRegistro(id) {
            // Buscar dados do registro via AJAX
            fetch(`ajax/get_registro.php?id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('edit_id').value = data.registro.id;
                        document.getElementById('edit_funcionario').value = data.registro.funcionario || '';
                        document.getElementById('edit_data_solicitacao_laudo').value = data.registro.data_solicitacao_laudo || '';
                        document.getElementById('edit_tecnico_responsavel').value = data.registro.tecnico_responsavel || '';
                        document.getElementById('edit_data_atual_sisbr').value = data.registro.data_atual_sisbr || '';
                        document.getElementById('edit_status').value = data.registro.status || '';
                        document.getElementById('edit_observacoes').value = data.registro.observacoes || '';

                        // Resetar estado do campo funcionário
                        resetFuncionarioField();

                        // Verificar se deve habilitar campo SISBR
                        verificarCamposSisbr();

                        new bootstrap.Modal(document.getElementById('editModal')).show();
                    } else {
                        alert('Erro ao carregar dados do registro');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao carregar dados do registro');
                });
        }

        function verificarCamposSisbr() {
            const dataLaudo = document.getElementById('edit_data_solicitacao_laudo').value;
            const tecnico = document.getElementById('edit_tecnico_responsavel').value;
            const dataSisbr = document.getElementById('edit_data_atual_sisbr');
            const status = document.getElementById('edit_status');

            // Habilitar campo SISBR apenas se Laudo e Técnico estiverem preenchidos
            if (dataLaudo && tecnico) {
                dataSisbr.disabled = false;
            } else {
                dataSisbr.disabled = true;
                dataSisbr.value = '';
            }

            // Atualizar status automaticamente baseado nos campos preenchidos
            if (dataSisbr.value) {
                // Se SISBR preenchido, status = Atualizado
                const statusOptions = status.options;
                for (let i = 0; i < statusOptions.length; i++) {
                    if (statusOptions[i].text === 'Atualizado') {
                        status.value = statusOptions[i].value;
                        break;
                    }
                }
            } else if (dataLaudo && tecnico) {
                // Se apenas Laudo e Técnico preenchidos (sem SISBR), status = Solicitado
                const statusOptions = status.options;
                for (let i = 0; i < statusOptions.length; i++) {
                    if (statusOptions[i].text === 'Solicitado') {
                        status.value = statusOptions[i].value;
                        break;
                    }
                }
            } else {
                // Se nenhum campo preenchido, status = Pendente
                const statusOptions = status.options;
                for (let i = 0; i < statusOptions.length; i++) {
                    if (statusOptions[i].text === 'Pendente') {
                        status.value = statusOptions[i].value;
                        break;
                    }
                }
            }
        }

        function toggleFuncionario() {
            const funcionarioField = document.getElementById('edit_funcionario');
            const toggleButton = document.getElementById('btn_toggle_funcionario');

            // Verificar se o botão existe (pode não existir para usuários comuns)
            if (!toggleButton) {
                return;
            }

            if (funcionarioField.disabled) {
                // Habilitar edição
                funcionarioField.disabled = false;
                funcionarioField.focus();
                toggleButton.innerHTML = '<i class="fas fa-lock"></i>';
                toggleButton.classList.remove('btn-outline-secondary');
                toggleButton.classList.add('btn-outline-danger');
                toggleButton.title = 'Bloquear edição do funcionário';
            } else {
                // Desabilitar edição
                funcionarioField.disabled = true;
                toggleButton.innerHTML = '<i class="fas fa-edit"></i>';
                toggleButton.classList.remove('btn-outline-danger');
                toggleButton.classList.add('btn-outline-secondary');
                toggleButton.title = 'Habilitar edição do funcionário';
            }
        }

        function resetFuncionarioField() {
            const funcionarioField = document.getElementById('edit_funcionario');
            const toggleButton = document.getElementById('btn_toggle_funcionario');

            // Sempre iniciar com campo bloqueado
            funcionarioField.disabled = true;

            // Verificar se o botão existe (pode não existir para usuários comuns)
            if (toggleButton) {
                toggleButton.innerHTML = '<i class="fas fa-edit"></i>';
                toggleButton.classList.remove('btn-outline-danger');
                toggleButton.classList.add('btn-outline-secondary');
                toggleButton.title = 'Habilitar edição do funcionário';
            }
        }

        // Função para preparar o formulário antes do envio
        function prepareFormSubmit() {
            // Habilitar o campo funcionário temporariamente para que seja enviado
            const funcionarioField = document.getElementById('edit_funcionario');
            if (funcionarioField && funcionarioField.disabled) {
                funcionarioField.disabled = false;
            }
            return true; // Permitir o envio do formulário
        }

        // Adicionar event listeners para os campos
        document.addEventListener('DOMContentLoaded', function() {
            const dataLaudo = document.getElementById('edit_data_solicitacao_laudo');
            const tecnico = document.getElementById('edit_tecnico_responsavel');
            const dataSisbr = document.getElementById('edit_data_atual_sisbr');

            if (dataLaudo) {
                dataLaudo.addEventListener('change', verificarCamposSisbr);
            }
            if (tecnico) {
                tecnico.addEventListener('change', verificarCamposSisbr);
            }
            if (dataSisbr) {
                dataSisbr.addEventListener('change', verificarCamposSisbr);
            }
        });

        function downloadExcel() {
            try {
                console.log('Iniciando download Excel...');

                // Construir URL com os filtros atuais da interface
                const params = new URLSearchParams();

                // Capturar filtros atuais dos campos da interface (especificamente do formulário de filtros)
                // Usar seletor mais específico para evitar conflito com modal
                const formFiltros = document.querySelector('form[method="GET"].row.g-2.align-items-end');
                const status = formFiltros?.querySelector('select[name="status"]')?.value || '';
                const pa = formFiltros?.querySelector('select[name="pa"]')?.value || '';
                const associado = formFiltros?.querySelector('input[name="associado"]')?.value || '';
                const funcionario = formFiltros?.querySelector('select[name="funcionario"]')?.value || '';
                const tecnico = formFiltros?.querySelector('select[name="tecnico"]')?.value || '';
                const mesRenda = formFiltros?.querySelector('select[name="mes_renda"]')?.value || '';

                // Capturar ordenação atual (das variáveis PHP)
                const sort = '<?php echo htmlspecialchars($sort_column); ?>';
                const dir = '<?php echo htmlspecialchars($sort_direction); ?>';

                console.log('Filtros capturados da interface:', {status, pa, associado, funcionario, tecnico, mesRenda, sort, dir});

                // Debug adicional
                console.log('Formulário de filtros encontrado:', formFiltros);
                console.log('Campos encontrados:', {
                    statusField: formFiltros?.querySelector('select[name="status"]'),
                    paField: formFiltros?.querySelector('select[name="pa"]'),
                    associadoField: formFiltros?.querySelector('input[name="associado"]'),
                    funcionarioField: formFiltros?.querySelector('select[name="funcionario"]'),
                    tecnicoField: formFiltros?.querySelector('select[name="tecnico"]'),
                    mesRendaField: formFiltros?.querySelector('select[name="mes_renda"]')
                });

                if (status) params.append('status', status);
                if (pa) params.append('pa', pa);
                if (associado) params.append('associado', associado);
                if (funcionario) params.append('funcionario', funcionario);
                if (tecnico) params.append('tecnico', tecnico);
                if (mesRenda) params.append('mes_renda', mesRenda);
                if (sort) params.append('sort', sort);
                if (dir) params.append('dir', dir);

                // Adicionar parâmetro para indicar que é download
                params.append('download', 'excel');

                // Construir URL final
                const finalUrl = 'export_excel.php?' + params.toString();
                console.log('URL final:', finalUrl);

                // Mostrar feedback visual
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando...';
                btn.disabled = true;

                // Redirecionar para download (usando export real para evitar problema de collation)
                const realUrl = finalUrl.replace('export_excel.php', 'export_excel_real.php');
                console.log('URL real (sem JOIN):', realUrl);
                window.location.href = realUrl;

                // Restaurar botão após um tempo
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 3000);

            } catch (error) {
                console.error('Erro no download Excel:', error);
                alert('Erro ao gerar relatório. Verifique o console para mais detalhes.');
            }
        }

        function verDetalhes(id) {
            // Carregar detalhes via AJAX
            fetch(`ajax/get_detalhes.php?id=${id}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('detailsContent').innerHTML = html;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                })
                .catch(error => {
                    console.error('Erro:', error);
                    document.getElementById('detailsContent').innerHTML = '<div class="alert alert-danger">Erro ao carregar detalhes</div>';
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                });
        }

        function removerRegistro(id) {
            document.getElementById('remove_id').value = id;
            document.getElementById('remove_justificativa').value = '';
            new bootstrap.Modal(document.getElementById('removeModal')).show();
        }

        function reativarRegistro(id) {
            document.getElementById('reactivate_id').value = id;
            new bootstrap.Modal(document.getElementById('reactivateModal')).show();
        }

        function excluirRegistro(id) {
            document.getElementById('delete_id').value = id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
