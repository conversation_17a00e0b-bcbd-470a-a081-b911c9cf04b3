<?php
/**
 * Configurações específicas do Dashboard de Técnicos Agrícolas
 * 
 * Este arquivo contém todas as configurações personalizáveis
 * para o dashboard dos técnicos agrícolas do sistema MCI.
 */

// Configurações de cores para técnicos
$cores_tecnicos_config = [
    1 => [
        'hex' => '#00AE9D',
        'nome' => 'Turquesa',
        'descricao' => 'Cor principal Sicoob'
    ],
    2 => [
        'hex' => '#003641',
        'nome' => 'Verde Escuro',
        'descricao' => 'Verde institucional Sicoob'
    ],
    3 => [
        'hex' => '#C9D200',
        'nome' => 'Verde Claro',
        'descricao' => 'Verde vibrante Sicoob'
    ],
    4 => [
        'hex' => '#70B86C',
        'nome' => 'Verde Médio',
        'descricao' => 'Verde intermediário'
    ],
    5 => [
        'hex' => '#494790',
        'nome' => 'Roxo',
        'descricao' => 'Roxo diferenciação'
    ],
    6 => [
        'hex' => '#6c757d',
        'nome' => 'Cinza',
        'descricao' => 'Cinza neutro'
    ]
];

// Configurações de tema
$tema_config = [
    'titulo' => 'Dashboard MCI - Técnicos Agrícolas',
    'subtitulo' => 'Técnicos Agrícolas - Atualizações Cadastrais',
    'icone_principal' => 'fas fa-seedling',
    'icone_meta' => 'fas fa-chart-line',
    'gradiente_fundo' => 'linear-gradient(135deg, #003641 0%, #00AE9D 100%)',
    'cor_primaria' => '#00AE9D',
    'cor_secundaria' => '#003641'
];

// Configurações de auto-refresh
$refresh_config = [
    'intervalo_segundos' => 30,
    'pausar_quando_oculto' => true,
    'animacao_antes_reload' => true,
    'delay_animacao_ms' => 1000
];

// Configurações da API Intranet
$api_config = [
    'url_base' => 'https://intranet.sicoobcredilivre.com.br/api',
    'usuario' => 'mci_dashboard',
    'senha' => 'Sicoob@2024#MCI',
    'timeout_segundos' => 5,
    'endpoint_usuario' => '/usuario/'
];

// Configurações de layout
$layout_config = [
    'grid_colunas' => 3,
    'grid_linhas' => 2,
    'max_tecnicos_exibidos' => 6,
    'animacao_entrada' => true,
    'delay_animacao_card_ms' => 100
];

// Configurações de métricas
$metricas_config = [
    'coluna_tecnico' => 'tecnico_responsavel',
    'status_concluido' => 'Atualizado',
    'apenas_usuarios_ativos' => true,
    'minimo_registros_para_exibir' => 1,
    'casas_decimais_percentual' => 1
];

// Configurações de responsividade
$responsividade_config = [
    'usar_clamp' => true,
    'breakpoint_mobile' => '768px',
    'breakpoint_tv_hd' => '1920px',
    'breakpoint_tv_4k' => '3840px',
    'gap_minimo' => '0.5rem',
    'gap_maximo' => '1.2rem'
];

// Função para obter array simples de cores (compatibilidade)
function getCoresTecnicos() {
    global $cores_tecnicos_config;
    return array_column($cores_tecnicos_config, 'hex');
}

// Função para obter configuração específica
function getConfig($secao, $chave = null) {
    global $tema_config, $refresh_config, $api_config, $layout_config, 
           $metricas_config, $responsividade_config, $cores_tecnicos_config;
    
    $configs = [
        'tema' => $tema_config,
        'refresh' => $refresh_config,
        'api' => $api_config,
        'layout' => $layout_config,
        'metricas' => $metricas_config,
        'responsividade' => $responsividade_config,
        'cores' => $cores_tecnicos_config
    ];
    
    if (!isset($configs[$secao])) {
        return null;
    }
    
    if ($chave === null) {
        return $configs[$secao];
    }
    
    return isset($configs[$secao][$chave]) ? $configs[$secao][$chave] : null;
}

// Função para gerar CSS das cores dinamicamente
function gerarCSSCores() {
    global $cores_tecnicos_config;
    
    $css = ":root {\n";
    
    foreach ($cores_tecnicos_config as $indice => $cor) {
        $css .= "    --cor-tecnico-{$indice}: {$cor['hex']};\n";
    }
    
    $css .= "}\n\n";
    
    foreach ($cores_tecnicos_config as $indice => $cor) {
        $css .= ".tecnico-{$indice} { --cor-tecnico: var(--cor-tecnico-{$indice}); }\n";
    }
    
    return $css;
}

// Função para validar configurações
function validarConfiguracoes() {
    $erros = [];
    
    // Validar cores
    global $cores_tecnicos_config;
    foreach ($cores_tecnicos_config as $indice => $cor) {
        if (!isset($cor['hex']) || !preg_match('/^#[0-9A-Fa-f]{6}$/', $cor['hex'])) {
            $erros[] = "Cor do técnico {$indice} inválida: {$cor['hex']}";
        }
    }
    
    // Validar configurações de refresh
    $refresh = getConfig('refresh');
    if ($refresh['intervalo_segundos'] < 10) {
        $erros[] = "Intervalo de refresh muito baixo: {$refresh['intervalo_segundos']}s";
    }
    
    // Validar layout
    $layout = getConfig('layout');
    if ($layout['grid_colunas'] * $layout['grid_linhas'] < $layout['max_tecnicos_exibidos']) {
        $erros[] = "Grid insuficiente para exibir todos os técnicos";
    }
    
    return $erros;
}

// Função para debug das configurações
function debugConfiguracoes() {
    if (!isset($_GET['debug']) || $_GET['debug'] !== 'config') {
        return;
    }
    
    echo "<h2>Debug - Configurações do Dashboard Técnicos Agrícolas</h2>";
    
    $secoes = ['tema', 'refresh', 'api', 'layout', 'metricas', 'responsividade', 'cores'];
    
    foreach ($secoes as $secao) {
        echo "<h3>" . ucfirst($secao) . "</h3>";
        echo "<pre>" . print_r(getConfig($secao), true) . "</pre>";
    }
    
    $erros = validarConfiguracoes();
    if (!empty($erros)) {
        echo "<h3 style='color: red;'>Erros de Configuração</h3>";
        echo "<ul>";
        foreach ($erros as $erro) {
            echo "<li style='color: red;'>{$erro}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ Todas as configurações estão válidas!</p>";
    }
    
    exit;
}

// Executar debug se solicitado
debugConfiguracoes();
?>
