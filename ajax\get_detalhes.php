<?php
require_once '../auth_check.php';

try {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID não fornecido');
    }
    
    $stmt = $pdo_mci->prepare("
        SELECT r.*, s.nome as status_nome, s.cor as status_cor,
               u1.nome_completo as usuario_cadastro_nome, u2.nome_completo as usuario_atualizacao_nome,
               u3.nome_completo as funcionario_nome, u4.nome_completo as tecnico_nome,
               p.nome as nome_ponto_atendimento
        FROM cad_registros r
        LEFT JOIN cad_status s ON r.status = s.id
        LEFT JOIN sicoob_access_control.usuarios u1 ON r.usuario_cadastro = u1.id
        LEFT JOIN sicoob_access_control.usuarios u2 ON r.usuario_atualizacao = u2.id
        LEFT JOIN sicoob_access_control.usuarios u3 ON r.funcionario = u3.id
        LEFT JOIN sicoob_access_control.usuarios u4 ON r.tecnico_responsavel = u4.id
        LEFT JOIN sicoob_access_control.pontos_atendimento p ON r.pa COLLATE utf8mb4_general_ci = p.numero COLLATE utf8mb4_general_ci
        WHERE r.id = ?
    ");
    $stmt->execute([$id]);
    $registro = $stmt->fetch();
    
    if (!$registro) {
        throw new Exception('Registro não encontrado');
    }
    
    // Buscar logs do registro
    $stmt = $pdo_mci->prepare("
        SELECT l.*, u.nome_completo as usuario_nome
        FROM cad_logs l
        LEFT JOIN sicoob_access_control.usuarios u ON l.usuario_id = u.id
        WHERE l.registro_id = ?
        ORDER BY l.data_hora DESC
    ");
    $stmt->execute([$id]);
    $logs = $stmt->fetchAll();
    
    ?>
    <div class="row">
        <div class="col-md-6">
            <h6 class="text-primary">Dados Básicos</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>PA:</strong></td>
                    <td>
                        <?php echo htmlspecialchars($registro['pa']); ?>
                        <?php if (!empty($registro['nome_ponto_atendimento'])): ?>
                            - <?php echo htmlspecialchars($registro['nome_ponto_atendimento']); ?>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td><strong>Nome Cliente:</strong></td>
                    <td><?php echo htmlspecialchars($registro['nome_cliente']); ?></td>
                </tr>
                <tr>
                    <td><strong>CPF/CNPJ:</strong></td>
                    <td><?php echo htmlspecialchars($registro['numero_cpf_cnpj']); ?></td>
                </tr>
                <tr>
                    <td><strong>CNAE:</strong></td>
                    <td><?php echo htmlspecialchars($registro['cnae'] ?? '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Tipo Pessoa:</strong></td>
                    <td><?php echo htmlspecialchars($registro['sigla_tipo_pessoa'] ?? '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Profissão:</strong></td>
                    <td><?php echo htmlspecialchars($registro['profissao'] ?? '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Depósito Total:</strong></td>
                    <td><?php echo $registro['deposito_total'] ? 'R$ ' . number_format($registro['deposito_total'], 2, ',', '.') : '-'; ?></td>
                </tr>
                <tr>
                    <td><strong>Saldo Devedor:</strong></td>
                    <td><?php echo $registro['saldo_devedor'] ? 'R$ ' . number_format($registro['saldo_devedor'], 2, ',', '.') : '-'; ?></td>
                </tr>
            </table>
        </div>
        
        <div class="col-md-6">
            <h6 class="text-primary">Dados Operacionais</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Funcionário:</strong></td>
                    <td><?php
                        $funcionario_display = $registro['funcionario_nome'] ?? $registro['funcionario'] ?? '-';
                        echo htmlspecialchars($funcionario_display);
                    ?></td>
                </tr>
                <tr>
                    <td><strong>Técnico Responsável:</strong></td>
                    <td><?php
                        $tecnico_display = $registro['tecnico_nome'] ?? $registro['tecnico_responsavel'] ?? '-';
                        echo htmlspecialchars($tecnico_display);
                    ?></td>
                </tr>
                <tr>
                    <td><strong>Data Últ. Atualização Renda:</strong></td>
                    <td><?php echo $registro['data_ultima_atualizacao_renda'] ? date('d/m/Y', strtotime($registro['data_ultima_atualizacao_renda'])) : '-'; ?></td>
                </tr>
                <tr>
                    <td><strong>Data Solicitação Laudo:</strong></td>
                    <td><?php echo $registro['data_solicitacao_laudo'] ? date('d/m/Y', strtotime($registro['data_solicitacao_laudo'])) : '-'; ?></td>
                </tr>
                <tr>
                    <td><strong>Data Atual SISBR:</strong></td>
                    <td><?php echo $registro['data_atual_sisbr'] ? date('d/m/Y', strtotime($registro['data_atual_sisbr'])) : '-'; ?></td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        <span class="badge" style="background-color: <?php echo $registro['status_cor'] ?? '#6c757d'; ?>; color: white;">
                            <?php echo htmlspecialchars($registro['status_nome'] ?? 'Indefinido'); ?>
                        </span>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    
    <?php if ($registro['observacoes']): ?>
    <div class="row mt-3">
        <div class="col-12">
            <h6 class="text-primary">Observações</h6>
            <div class="alert alert-info">
                <?php echo nl2br(htmlspecialchars($registro['observacoes'])); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row mt-3">
        <div class="col-md-6">
            <h6 class="text-primary">Informações de Controle</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>Data Cadastro:</strong></td>
                    <td><?php echo date('d/m/Y H:i:s', strtotime($registro['data_cadastro'])); ?></td>
                </tr>
                <tr>
                    <td><strong>Usuário Cadastro:</strong></td>
                    <td><?php echo htmlspecialchars($registro['usuario_cadastro_nome'] ?? 'N/A'); ?></td>
                </tr>
                <?php if ($registro['data_atualizacao'] && $registro['data_atualizacao'] != $registro['data_cadastro']): ?>
                <tr>
                    <td><strong>Data Atualização:</strong></td>
                    <td><?php echo date('d/m/Y H:i:s', strtotime($registro['data_atualizacao'])); ?></td>
                </tr>
                <tr>
                    <td><strong>Usuário Atualização:</strong></td>
                    <td><?php echo htmlspecialchars($registro['usuario_atualizacao_nome'] ?? 'N/A'); ?></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
        
        <div class="col-md-6">
            <h6 class="text-primary">Histórico de Alterações</h6>
            <?php if (!empty($logs)): ?>
            <div style="max-height: 200px; overflow-y: auto;">
                <?php foreach ($logs as $log): ?>
                <div class="border-bottom pb-2 mb-2">
                    <small class="text-muted">
                        <?php echo date('d/m/Y H:i', strtotime($log['data_hora'])); ?> - 
                        <?php echo htmlspecialchars($log['usuario_nome'] ?? 'Sistema'); ?>
                    </small>
                    <div><strong><?php echo htmlspecialchars($log['acao']); ?></strong></div>
                    <div class="text-muted"><?php echo htmlspecialchars($log['detalhes']); ?></div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <p class="text-muted">Nenhuma alteração registrada.</p>
            <?php endif; ?>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Erro ao carregar detalhes: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>
