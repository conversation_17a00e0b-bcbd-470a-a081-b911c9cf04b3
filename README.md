# Sistema MCI - Atualizações Cadastrais

Sistema para gerenciamento e acompanhamento do progresso de atualizações cadastrais baseado em planilhas Excel.

## Características

- **Herança de Configurações**: Herda conexão com banco de dados, autenticação, logs e permissões do projeto principal
- **Banco de Dados**: Utiliza o banco 'mci' ao invés do principal 'sicoob_access_control'
- **Prefixo de Tabelas**: Todas as tabelas utilizam o prefixo 'cad' para identificação
- **Sistema de Logs**: Registra todas as ações tanto no banco principal quanto no banco MCI

## Estrutura do Projeto

```
mci/
├── config/
│   ├── database.php      # Configuração específica do banco MCI
│   └── config.php        # Configurações gerais do projeto
├── classes/
│   └── Logger.php        # Sistema de logs herdado
├── ajax/
│   ├── get_registro.php  # API para buscar dados de registro
│   └── get_detalhes.php  # API para detalhes completos
├── sql/
│   └── create_tables.sql # Estrutura das tabelas
├── logs/                 # Logs específicos do projeto
├── uploads/              # Arquivos de upload temporários
├── index.php            # Página principal
├── importar.php         # Importação de planilhas Excel
├── gerenciar.php        # Gerenciamento de registros
├── dashboard.php        # Dashboard com métricas
└── auth_check.php       # Verificação de autenticação
```

## Funcionalidades

### 1. Importação de Planilhas
- Suporte a arquivos .xlsx e .xls
- Validação de formato e dados obrigatórios
- Processamento em lote com controle de erros
- Log detalhado de importações

### 2. Gerenciamento de Registros
- Listagem com filtros avançados
- Edição de status e observações
- Visualização detalhada de registros
- Exclusão com confirmação
- Paginação automática

### 3. Dashboard de Métricas
- Estatísticas gerais dos registros
- Gráficos de distribuição por status
- Evolução temporal dos cadastros
- Rankings por PA, funcionário e técnico
- Atualização automática

## Formato da Planilha

A planilha deve conter as seguintes colunas na ordem especificada:

1. **PA** (obrigatório)
2. **Nome Cliente** (obrigatório)
3. **Número CPF/CNPJ** (obrigatório)
4. CNAE
5. Data Última Atualização Renda
6. Sigla Tipo Pessoa
7. Profissão
8. Depósito Total
9. **Saldo Devedor** (nova coluna)
10. FUNCIONÁRIO
11. DATA DA SOLICITAÇÃO DO LAUDO
12. TÉCNICO RESPONSÁVEL
13. DATA DA ATUAL. SISBR

**Importante**: A primeira linha deve conter os cabeçalhos e será ignorada durante a importação.

## Banco de Dados

### Tabelas Criadas

- **cad_registros**: Dados principais da planilha
- **cad_logs**: Logs específicos do projeto MCI
- **cad_importacoes**: Controle de importações
- **cad_erros_importacao**: Erros durante importações

### Status dos Registros

- `pendente`: Registro aguardando processamento
- `em_andamento`: Registro sendo processado
- `concluido`: Atualização cadastral finalizada
- `cancelado`: Registro cancelado

## Instalação

1. **Criar o banco de dados MCI**:
```sql
CREATE DATABASE mci;
```

2. **Executar o script de criação das tabelas**:
```bash
mysql -u [usuario] -p mci < sql/create_tables.sql
```

3. **Configurar permissões**:
   - O sistema herda as permissões do projeto principal
   - Usuários autenticados têm acesso básico
   - Administradores e gestores têm acesso completo

## Dependências

- PHP 7.4+
- MySQL 5.7+
- PhpSpreadsheet (via Composer do projeto principal)
- Bootstrap 5.3
- Chart.js
- Font Awesome 6

## Logs

O sistema registra logs em três níveis:

1. **Arquivo**: `logs/mci_YYYY-MM-DD.log`
2. **Banco Principal**: Tabela `logs` do banco `sicoob_access_control`
3. **Banco MCI**: Tabela `cad_logs` do banco `mci`

## Segurança

- Autenticação herdada do projeto principal
- Validação de tipos de arquivo
- Sanitização de dados de entrada
- Proteção contra SQL injection
- Logs de auditoria completos

## Suporte

Para suporte técnico, consulte os logs do sistema ou entre em contato com a equipe de TI.

---

**Versão**: 1.0.0  
**Data**: <?php echo date('d/m/Y'); ?>  
**Desenvolvido para**: Sicoob Credilivre
