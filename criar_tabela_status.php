<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Criar Tabela de Status</h1>";
echo "<hr>";

try {
    echo "<h3>Executando criação da tabela cad_status...</h3>";
    
    // 1. Criar tabela cad_status
    echo "<p>1. Criando tabela cad_status...</p>";
    $sql_create_table = "
        CREATE TABLE IF NOT EXISTS cad_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nome VARCHAR(50) NOT NULL UNIQUE COMMENT 'Nome do status',
            descricao VARCHAR(255) COMMENT 'Descrição do status',
            cor VARCHAR(20) DEFAULT '#6c757d' COMMENT 'Cor para exibição (hex)',
            ativo BOOLEAN DEFAULT TRUE COMMENT 'Status ativo/inativo',
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de criação',
            INDEX idx_nome (nome),
            INDEX idx_ativo (ativo)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela de status dos registros'
    ";
    
    $pdo_mci->exec($sql_create_table);
    echo "<p style='color: green;'>✅ Tabela cad_status criada com sucesso!</p>";
    
    // 2. Inserir status padrão
    echo "<p>2. Inserindo status padrão...</p>";
    $sql_insert_status = "
        INSERT INTO cad_status (nome, descricao, cor) VALUES 
        ('Pendente', 'Registro pendente de atualização', '#ffc107'),
        ('Atualizado', 'Registro atualizado e processado', '#28a745')
        ON DUPLICATE KEY UPDATE 
            descricao = VALUES(descricao),
            cor = VALUES(cor)
    ";
    
    $pdo_mci->exec($sql_insert_status);
    echo "<p style='color: green;'>✅ Status padrão inseridos!</p>";
    
    // 3. Verificar se coluna status existe
    echo "<p>3. Verificando estrutura da tabela cad_registros...</p>";
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros LIKE 'status'");
    $stmt->execute();
    $column_exists = $stmt->rowCount() > 0;
    
    if (!$column_exists) {
        echo "<p>3a. Adicionando coluna status...</p>";
        $sql_add_column = "
            ALTER TABLE cad_registros 
            ADD COLUMN status VARCHAR(50) DEFAULT 'Pendente' COMMENT 'Status do registro' 
            AFTER usuario_cadastro
        ";
        $pdo_mci->exec($sql_add_column);
        echo "<p style='color: green;'>✅ Coluna status adicionada!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Coluna status já existe.</p>";
    }
    
    // 4. Verificar tipo atual da coluna status
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field = 'status'");
    $stmt->execute();
    $column_info = $stmt->fetch();
    
    echo "<p>4. Verificando tipo da coluna status...</p>";
    echo "<p>Tipo atual: <code>{$column_info['Type']}</code></p>";
    
    // Se ainda for VARCHAR, precisamos converter
    if (strpos($column_info['Type'], 'varchar') !== false || strpos($column_info['Type'], 'text') !== false) {
        echo "<p>4a. Atualizando registros existentes...</p>";
        
        // Atualizar registros para usar nomes válidos
        $sql_update_existing = "
            UPDATE cad_registros 
            SET status = 'Pendente' 
            WHERE status IS NULL OR status = '' OR status NOT IN ('Pendente', 'Atualizado')
        ";
        $pdo_mci->exec($sql_update_existing);
        
        echo "<p>4b. Convertendo coluna para usar IDs...</p>";
        
        // Primeiro, criar uma coluna temporária
        $pdo_mci->exec("ALTER TABLE cad_registros ADD COLUMN status_temp INT DEFAULT 1");
        
        // Atualizar valores baseado nos nomes
        $pdo_mci->exec("
            UPDATE cad_registros r
            JOIN cad_status s ON s.nome = r.status
            SET r.status_temp = s.id
        ");
        
        // Remover coluna antiga e renomear a nova
        $pdo_mci->exec("ALTER TABLE cad_registros DROP COLUMN status");
        $pdo_mci->exec("ALTER TABLE cad_registros CHANGE status_temp status INT DEFAULT 1 COMMENT 'ID do status (referência para cad_status)'");
        
        echo "<p style='color: green;'>✅ Coluna convertida para usar IDs!</p>";
    } else {
        echo "<p style='color: green;'>✅ Coluna já está no formato correto (INT).</p>";
        
        // Garantir que registros sem status tenham o padrão
        $pdo_mci->exec("UPDATE cad_registros SET status = 1 WHERE status IS NULL OR status = 0");
    }
    
    // 5. Adicionar chave estrangeira (se não existir)
    echo "<p>5. Verificando chave estrangeira...</p>";
    
    $stmt = $pdo_mci->prepare("
        SELECT CONSTRAINT_NAME 
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = 'mci' 
        AND TABLE_NAME = 'cad_registros' 
        AND COLUMN_NAME = 'status' 
        AND REFERENCED_TABLE_NAME = 'cad_status'
    ");
    $stmt->execute();
    $fk_exists = $stmt->rowCount() > 0;
    
    if (!$fk_exists) {
        echo "<p>5a. Adicionando chave estrangeira...</p>";
        try {
            $pdo_mci->exec("
                ALTER TABLE cad_registros 
                ADD CONSTRAINT fk_registros_status 
                FOREIGN KEY (status) REFERENCES cad_status(id) 
                ON UPDATE CASCADE ON DELETE RESTRICT
            ");
            echo "<p style='color: green;'>✅ Chave estrangeira adicionada!</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Chave estrangeira já existe ou erro: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Chave estrangeira já existe.</p>";
    }
    
    // 6. Adicionar índice
    echo "<p>6. Verificando índice...</p>";
    try {
        $pdo_mci->exec("ALTER TABLE cad_registros ADD INDEX idx_status (status)");
        echo "<p style='color: green;'>✅ Índice adicionado!</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Índice já existe.</p>";
    }
    
    // 7. Mostrar estrutura final
    echo "<h3>Estrutura Final</h3>";
    
    echo "<h4>Tabela cad_status:</h4>";
    $stmt = $pdo_mci->prepare("SELECT * FROM cad_status ORDER BY id");
    $stmt->execute();
    $status_list = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Descrição</th><th>Cor</th><th>Ativo</th><th>Data Criação</th></tr>";
    foreach ($status_list as $status) {
        $cor_preview = "<span style='background-color: {$status['cor']}; color: white; padding: 2px 8px; border-radius: 3px;'>{$status['nome']}</span>";
        echo "<tr>";
        echo "<td>{$status['id']}</td>";
        echo "<td><strong>{$status['nome']}</strong></td>";
        echo "<td>{$status['descricao']}</td>";
        echo "<td>{$cor_preview}</td>";
        echo "<td>" . ($status['ativo'] ? 'Sim' : 'Não') . "</td>";
        echo "<td>{$status['data_criacao']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>Coluna status em cad_registros:</h4>";
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field = 'status'");
    $stmt->execute();
    $column_info = $stmt->fetch();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
    echo "<tr>";
    echo "<td>{$column_info['Field']}</td>";
    echo "<td>{$column_info['Type']}</td>";
    echo "<td>{$column_info['Null']}</td>";
    echo "<td>{$column_info['Key']}</td>";
    echo "<td>" . ($column_info['Default'] ?? 'NULL') . "</td>";
    echo "<td>{$column_info['Extra']}</td>";
    echo "</tr>";
    echo "</table>";
    
    // 8. Mostrar estatísticas
    echo "<h4>Estatísticas por Status:</h4>";
    $stmt = $pdo_mci->prepare("
        SELECT 
            s.id,
            s.nome as status_nome,
            s.descricao,
            s.cor,
            COUNT(r.id) as total_registros
        FROM cad_status s
        LEFT JOIN cad_registros r ON r.status = s.id
        WHERE s.ativo = TRUE
        GROUP BY s.id, s.nome, s.descricao, s.cor
        ORDER BY s.id
    ");
    $stmt->execute();
    $stats = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Status</th><th>Descrição</th><th>Total de Registros</th></tr>";
    foreach ($stats as $stat) {
        $cor_preview = "<span style='background-color: {$stat['cor']}; color: white; padding: 2px 8px; border-radius: 3px;'>{$stat['status_nome']}</span>";
        echo "<tr>";
        echo "<td>$cor_preview</td>";
        echo "<td>{$stat['descricao']}</td>";
        echo "<td><strong>{$stat['total_registros']}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #155724;'>🎉 Tabela de Status Criada com Sucesso!</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li>✅ Tabela cad_status criada</li>";
    echo "<li>✅ Status 'Pendente' e 'Atualizado' inseridos</li>";
    echo "<li>✅ Coluna status convertida para usar referência</li>";
    echo "<li>✅ Chave estrangeira configurada</li>";
    echo "<li>✅ Índices adicionados para performance</li>";
    echo "<li>✅ Registros existentes marcados como 'Pendente'</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Próximos passos:</h3>";
    echo "<ul>";
    echo "<li>Atualizar interface para mostrar status com cores</li>";
    echo "<li>Implementar funcionalidade para alterar status</li>";
    echo "<li>Adicionar filtros por status</li>";
    echo "</ul>";
    
    echo "<p><a href='gerenciar.php'>Ver registros com status</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a criação</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
