<?php
require_once 'auth_check.php';

echo "<h1>🔍 Debug Específico - Filtros Status e PA</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .info { color: #17a2b8; }
    .warning { color: #ffc107; }
    .card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
</style>";

try {
    echo "<div class='card'>";
    echo "<h2 class='info'>1. Verificando Dados Disponíveis</h2>";
    
    // Verificar status disponíveis
    echo "<h3>Status Disponíveis:</h3>";
    $stmt_status = $pdo_mci->query("SELECT id, nome FROM cad_status ORDER BY id");
    $status_list = $stmt_status->fetchAll(PDO::FETCH_ASSOC);
    echo "<table>";
    echo "<tr><th>ID</th><th>Nome</th><th>Registros</th></tr>";
    foreach ($status_list as $status) {
        $stmt_count = $pdo_mci->prepare("SELECT COUNT(*) FROM cad_registros WHERE status = ?");
        $stmt_count->execute([$status['id']]);
        $count = $stmt_count->fetchColumn();
        echo "<tr><td>{$status['id']}</td><td>{$status['nome']}</td><td>$count</td></tr>";
    }
    echo "</table>";
    
    // Verificar PAs disponíveis
    echo "<h3>PAs com Registros:</h3>";
    $stmt_pa = $pdo_mci->query("SELECT pa, COUNT(*) as total FROM cad_registros GROUP BY pa ORDER BY pa");
    $pa_list = $stmt_pa->fetchAll(PDO::FETCH_ASSOC);
    echo "<table>";
    echo "<tr><th>PA</th><th>Registros</th></tr>";
    foreach ($pa_list as $pa) {
        echo "<tr><td>{$pa['pa']}</td><td>{$pa['total']}</td></tr>";
    }
    echo "</table>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>2. Testando Filtros Individualmente</h2>";
    
    // Teste 1: Filtro por Status
    echo "<h3>Teste 1: Filtro por Status = 'Pendente'</h3>";
    
    // Primeiro, buscar ID do status
    $stmt_status_id = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = ?");
    $stmt_status_id->execute(['Pendente']);
    $status_id = $stmt_status_id->fetchColumn();
    echo "<p class='info'>ID do status 'Pendente': $status_id</p>";
    
    if ($status_id) {
        $stmt_test_status = $pdo_mci->prepare("
            SELECT COUNT(*) as total
            FROM cad_registros cr
            WHERE cr.status = ?
        ");
        $stmt_test_status->execute([$status_id]);
        $total_status = $stmt_test_status->fetchColumn();
        echo "<p class='success'>✅ Total de registros com status 'Pendente': $total_status</p>";
        
        // Mostrar alguns registros
        $stmt_sample_status = $pdo_mci->prepare("
            SELECT cr.id, cr.nome_cliente, cr.status
            FROM cad_registros cr
            WHERE cr.status = ?
            LIMIT 5
        ");
        $stmt_sample_status->execute([$status_id]);
        $sample_status = $stmt_sample_status->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Primeiros 5 registros pendentes:</h4>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Cliente</th><th>Status ID</th></tr>";
        foreach ($sample_status as $reg) {
            echo "<tr><td>{$reg['id']}</td><td>{$reg['nome_cliente']}</td><td>{$reg['status']}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ Status 'Pendente' não encontrado!</p>";
    }
    
    // Teste 2: Filtro por PA
    echo "<h3>Teste 2: Filtro por PA = '2'</h3>";
    $stmt_test_pa = $pdo_mci->prepare("
        SELECT COUNT(*) as total
        FROM cad_registros cr
        WHERE cr.pa = ?
    ");
    $stmt_test_pa->execute(['2']);
    $total_pa = $stmt_test_pa->fetchColumn();
    echo "<p class='success'>✅ Total de registros do PA 2: $total_pa</p>";
    
    // Mostrar alguns registros
    $stmt_sample_pa = $pdo_mci->prepare("
        SELECT cr.id, cr.nome_cliente, cr.pa
        FROM cad_registros cr
        WHERE cr.pa = ?
        LIMIT 5
    ");
    $stmt_sample_pa->execute(['2']);
    $sample_pa = $stmt_sample_pa->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h4>Primeiros 5 registros do PA 2:</h4>";
    echo "<table>";
    echo "<tr><th>ID</th><th>Cliente</th><th>PA</th></tr>";
    foreach ($sample_pa as $reg) {
        echo "<tr><td>{$reg['id']}</td><td>{$reg['nome_cliente']}</td><td>{$reg['pa']}</td></tr>";
    }
    echo "</table>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>3. Simulando Export com Filtros</h2>";
    
    // Simular parâmetros GET
    $test_cases = [
        ['status' => 'Pendente', 'desc' => 'Status = Pendente'],
        ['pa' => '2', 'desc' => 'PA = 2'],
        ['status' => 'Pendente', 'pa' => '2', 'desc' => 'Status = Pendente + PA = 2']
    ];
    
    foreach ($test_cases as $index => $test) {
        echo "<h3>Teste " . ($index + 1) . ": {$test['desc']}</h3>";
        
        // Simular lógica do export_excel_real.php
        $where_conditions = [];
        $params = [];
        
        if (isset($test['status'])) {
            $stmt_status_id = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = ?");
            $stmt_status_id->execute([$test['status']]);
            $status_id = $stmt_status_id->fetchColumn();
            echo "<p class='info'>Status '{$test['status']}' → ID: $status_id</p>";
            if ($status_id) {
                $where_conditions[] = "cr.status = ?";
                $params[] = $status_id;
            }
        }
        
        if (isset($test['pa'])) {
            $where_conditions[] = "cr.pa = ?";
            $params[] = $test['pa'];
            echo "<p class='info'>PA: {$test['pa']}</p>";
        }
        
        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }
        
        echo "<p class='info'>WHERE clause: <code>$where_clause</code></p>";
        echo "<p class='info'>Parâmetros: <code>" . implode(', ', $params) . "</code></p>";
        
        $sql = "
            SELECT COUNT(*) as total
            FROM cad_registros cr
            $where_clause
        ";
        
        $stmt = $pdo_mci->prepare($sql);
        $stmt->execute($params);
        $total = $stmt->fetchColumn();
        
        echo "<p class='success'>✅ Total de registros encontrados: $total</p>";
        
        // Gerar URL de teste
        $url_params = [];
        foreach ($test as $key => $value) {
            if ($key !== 'desc') {
                $url_params[] = "$key=" . urlencode($value);
            }
        }
        $url_params[] = "download=excel";
        $test_url = "export_excel_real.php?" . implode('&', $url_params);
        
        echo "<p><strong>URL de teste:</strong> <a href='$test_url' target='_blank'>$test_url</a></p>";
        echo "<hr>";
    }
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>4. Verificando Parâmetros GET</h2>";
    
    echo "<h3>Parâmetros Recebidos nesta Página:</h3>";
    if (!empty($_GET)) {
        echo "<pre>" . print_r($_GET, true) . "</pre>";
        
        // Testar com os parâmetros recebidos
        if (isset($_GET['status']) || isset($_GET['pa'])) {
            echo "<h4>Testando com parâmetros recebidos:</h4>";
            
            $filtro_status = $_GET['status'] ?? '';
            $filtro_pa = $_GET['pa'] ?? '';
            
            $where_conditions = [];
            $params = [];
            
            if (!empty($filtro_status)) {
                $stmt_status_id = $pdo_mci->prepare("SELECT id FROM cad_status WHERE nome = ?");
                $stmt_status_id->execute([$filtro_status]);
                $status_id = $stmt_status_id->fetchColumn();
                if ($status_id) {
                    $where_conditions[] = "cr.status = ?";
                    $params[] = $status_id;
                    echo "<p class='success'>✅ Filtro status aplicado: '$filtro_status' (ID: $status_id)</p>";
                } else {
                    echo "<p class='error'>❌ Status '$filtro_status' não encontrado!</p>";
                }
            }
            
            if (!empty($filtro_pa)) {
                $where_conditions[] = "cr.pa = ?";
                $params[] = $filtro_pa;
                echo "<p class='success'>✅ Filtro PA aplicado: '$filtro_pa'</p>";
            }
            
            if (!empty($where_conditions)) {
                $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
                $sql = "SELECT COUNT(*) as total FROM cad_registros cr $where_clause";
                $stmt = $pdo_mci->prepare($sql);
                $stmt->execute($params);
                $total = $stmt->fetchColumn();
                echo "<p class='success'>✅ Total com filtros aplicados: $total</p>";
            }
        }
    } else {
        echo "<p class='warning'>⚠️ Nenhum parâmetro GET recebido</p>";
        echo "<p>Teste com: <a href='?status=Pendente'>?status=Pendente</a> ou <a href='?pa=2'>?pa=2</a></p>";
    }
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='success'>✅ Links de Teste Direto</h2>";
    echo "<ul>";
    echo "<li><a href='debug_filtros_status_pa.php?status=Pendente' target='_blank'>Testar com status=Pendente</a></li>";
    echo "<li><a href='debug_filtros_status_pa.php?pa=2' target='_blank'>Testar com pa=2</a></li>";
    echo "<li><a href='debug_filtros_status_pa.php?status=Pendente&pa=2' target='_blank'>Testar com status=Pendente&pa=2</a></li>";
    echo "<li><a href='export_excel_real.php?download=excel&status=Pendente' target='_blank'>Download direto status=Pendente</a></li>";
    echo "<li><a href='export_excel_real.php?download=excel&pa=2' target='_blank'>Download direto pa=2</a></li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='card'>";
    echo "<h2 class='error'>❌ Erro no Debug</h2>";
    echo "<p>Erro: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
