-- Script para criar tabela mci_metas
-- Execute este script para implementar o sistema de metas

USE mci;

-- Criar tabela mci_metas
CREATE TABLE IF NOT EXISTS mci_metas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    porcentagem_meta DECIMAL(5,2) NOT NULL COMMENT 'Porcentagem da meta (ex: 75.00 para 75%)',
    ativo BOOLEAN DEFAULT TRUE COMMENT 'Meta ativa/inativa',
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de criação',
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data de atualização',
    usuario_criacao INT COMMENT 'ID do usuário que criou',
    usuario_atualizacao INT COMMENT 'ID do usuário que atualizou',
    
    INDEX idx_ativo (ativo),
    INDEX idx_data_criacao (data_criacao)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela de configuração de metas do MCI';

-- Inserir meta padrão se não existir nenhuma
INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) 
SELECT 75.00, TRUE, 1
WHERE NOT EXISTS (SELECT 1 FROM mci_metas WHERE ativo = TRUE);

-- Verificar se a tabela foi criada
DESCRIBE mci_metas;

-- Mostrar dados inseridos
SELECT * FROM mci_metas;

-- Mensagem de sucesso
SELECT 'Tabela mci_metas criada com sucesso!' as status;
