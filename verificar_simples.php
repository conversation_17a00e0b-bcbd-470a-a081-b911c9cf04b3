<?php
/**
 * Script simplificado de verificação e correção da instalação do MCI
 * Versão compatível com MariaDB/MySQL
 */

require_once 'config/config.php';

echo "<!DOCTYPE html>";
echo "<html lang='pt-BR'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Verificação Simples - MCI</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo ":root { --sicoob-verde-escuro: #003641; --sicoob-turquesa: #00AE9D; }";
echo "body { background-color: #f8f9fa; font-family: 'Segoe UI', sans-serif; }";
echo ".card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }";
echo ".card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }";
echo ".status-ok { color: #28a745; font-weight: bold; }";
echo ".status-error { color: #dc3545; font-weight: bold; }";
echo ".status-warning { color: #ffc107; font-weight: bold; }";
echo ".status-info { color: #17a2b8; font-weight: bold; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-4'>";
echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h3><i class='fas fa-tools'></i> Verificação e Correção - Sistema MCI</h3>";
echo "<p class='mb-0'>Versão simplificada compatível com MariaDB/MySQL</p>";
echo "</div>";
echo "<div class='card-body'>";

$problemas_encontrados = [];
$correcoes_aplicadas = [];

try {
    // 1. Verificar conexão com banco MCI
    echo "<h5>1. Verificando conexão com banco MCI...</h5>";
    
    if (isset($pdo_mci)) {
        echo "<p class='status-ok'>✅ Conexão com banco MCI estabelecida</p>";
        
        // Testar uma query simples
        $test_query = $pdo_mci->query("SELECT 1");
        if ($test_query) {
            echo "<p class='status-ok'>✅ Banco MCI respondendo corretamente</p>";
        } else {
            echo "<p class='status-error'>❌ Banco MCI não está respondendo</p>";
            $problemas_encontrados[] = "Banco MCI não responde a queries";
        }
    } else {
        echo "<p class='status-error'>❌ Erro na conexão com banco MCI</p>";
        $problemas_encontrados[] = "Conexão com banco MCI falhou";
    }
    
    // 2. Verificar e criar tabela mci_metas (principal problema)
    echo "<h5>2. Verificando/Criando tabela de metas...</h5>";
    
    $tabela_metas_existe = false;
    
    try {
        // Tentar fazer uma query na tabela para ver se existe
        $test_metas = $pdo_mci->query("SELECT COUNT(*) FROM mci_metas LIMIT 1");
        if ($test_metas !== false) {
            $tabela_metas_existe = true;
            echo "<p class='status-ok'>✅ Tabela mci_metas encontrada</p>";
            
            // Verificar se tem dados
            $count = $test_metas->fetchColumn();
            if ($count > 0) {
                echo "<p class='status-ok'>✅ Tabela mci_metas tem dados</p>";
                
                // Verificar meta ativa
                $stmt_ativa = $pdo_mci->query("SELECT porcentagem_meta FROM mci_metas WHERE ativo = TRUE ORDER BY id DESC LIMIT 1");
                if ($stmt_ativa && $meta_ativa = $stmt_ativa->fetch()) {
                    echo "<p class='status-ok'>✅ Meta ativa encontrada: {$meta_ativa['porcentagem_meta']}%</p>";
                } else {
                    echo "<p class='status-warning'>⚠️ Nenhuma meta ativa, ativando primeira meta...</p>";
                    $pdo_mci->exec("UPDATE mci_metas SET ativo = TRUE WHERE id = (SELECT id FROM (SELECT id FROM mci_metas ORDER BY id LIMIT 1) as temp)");
                    echo "<p class='status-ok'>✅ Meta ativada</p>";
                    $correcoes_aplicadas[] = "Meta ativada automaticamente";
                }
            } else {
                echo "<p class='status-warning'>⚠️ Tabela mci_metas vazia, inserindo meta padrão...</p>";
                $pdo_mci->exec("INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) VALUES (75.00, TRUE, 1)");
                echo "<p class='status-ok'>✅ Meta padrão de 75% inserida</p>";
                $correcoes_aplicadas[] = "Meta padrão inserida";
            }
        }
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "doesn't exist") !== false || strpos($e->getMessage(), "Table") !== false) {
            $tabela_metas_existe = false;
        } else {
            throw $e; // Re-throw se for outro tipo de erro
        }
    }
    
    if (!$tabela_metas_existe) {
        echo "<p class='status-warning'>⚠️ Tabela mci_metas não encontrada, criando...</p>";
        
        $sql_create_metas = "
            CREATE TABLE mci_metas (
                id INT AUTO_INCREMENT PRIMARY KEY,
                porcentagem_meta DECIMAL(5,2) NOT NULL COMMENT 'Porcentagem da meta (ex: 75.00 para 75%)',
                ativo BOOLEAN DEFAULT TRUE COMMENT 'Meta ativa/inativa',
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de criação',
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data de atualização',
                usuario_criacao INT COMMENT 'ID do usuário que criou',
                usuario_atualizacao INT COMMENT 'ID do usuário que atualizou',
                
                INDEX idx_ativo (ativo),
                INDEX idx_data_criacao (data_criacao)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela de configuração de metas do MCI'
        ";
        
        $pdo_mci->exec($sql_create_metas);
        echo "<p class='status-ok'>✅ Tabela mci_metas criada com sucesso</p>";
        
        // Inserir meta padrão
        $pdo_mci->exec("INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) VALUES (75.00, TRUE, 1)");
        echo "<p class='status-ok'>✅ Meta padrão de 75% inserida</p>";
        
        $correcoes_aplicadas[] = "Tabela mci_metas criada e meta padrão inserida";
    }
    
    // 3. Verificar tabelas principais (método alternativo)
    echo "<h5>3. Verificando tabelas principais...</h5>";
    
    $tabelas_principais = [
        'cad_registros' => 'Tabela principal de registros',
        'cad_status' => 'Tabela de status',
        'cad_logs' => 'Tabela de logs'
    ];
    
    foreach ($tabelas_principais as $tabela => $descricao) {
        try {
            $test_table = $pdo_mci->query("SELECT 1 FROM $tabela LIMIT 1");
            if ($test_table !== false) {
                echo "<p class='status-ok'>✅ $tabela - $descricao</p>";
            } else {
                echo "<p class='status-error'>❌ $tabela - $descricao (PROBLEMA DE ACESSO)</p>";
                $problemas_encontrados[] = "Problema de acesso à tabela $tabela";
            }
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                echo "<p class='status-error'>❌ $tabela - $descricao (NÃO ENCONTRADA)</p>";
                $problemas_encontrados[] = "Tabela $tabela não encontrada";
            } else {
                echo "<p class='status-warning'>⚠️ $tabela - $descricao (ERRO: " . htmlspecialchars($e->getMessage()) . ")</p>";
                $problemas_encontrados[] = "Erro na tabela $tabela";
            }
        }
    }
    
    // 4. Teste do problema específico
    echo "<h5>4. Testando correção do problema original...</h5>";
    
    try {
        // Simular o código que estava causando erro
        $porcentagem_meta = 75.00; // Valor padrão
        
        $stmt_check_table = $pdo_mci->query("SELECT 1 FROM mci_metas LIMIT 1");
        
        if ($stmt_check_table !== false) {
            // Tabela existe, buscar meta ativa
            $stmt_meta = $pdo_mci->prepare("SELECT porcentagem_meta FROM mci_metas WHERE ativo = TRUE ORDER BY id DESC LIMIT 1");
            $stmt_meta->execute();
            $meta_config = $stmt_meta->fetch();
            $porcentagem_meta = $meta_config ? $meta_config['porcentagem_meta'] : 75.00;
            
            echo "<p class='status-ok'>✅ Código de busca de meta funcionando corretamente</p>";
            echo "<p class='status-info'>ℹ️ Meta atual: $porcentagem_meta%</p>";
        } else {
            echo "<p class='status-error'>❌ Ainda há problemas com a tabela mci_metas</p>";
            $problemas_encontrados[] = "Tabela mci_metas ainda com problemas";
        }
    } catch (PDOException $e) {
        echo "<p class='status-error'>❌ Erro no teste: " . htmlspecialchars($e->getMessage()) . "</p>";
        $problemas_encontrados[] = "Erro no teste de correção: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'>❌ Erro geral durante verificação: " . htmlspecialchars($e->getMessage()) . "</p>";
    $problemas_encontrados[] = "Erro geral: " . $e->getMessage();
}

// Resumo
echo "<hr>";
echo "<h5>Resumo da Verificação</h5>";

if (empty($problemas_encontrados)) {
    echo "<div class='alert alert-success'>";
    echo "<h6><i class='fas fa-check-circle'></i> Sistema MCI corrigido e funcionando!</h6>";
    echo "<p>O problema da tabela 'mci_configuracoes' foi resolvido.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h6><i class='fas fa-exclamation-triangle'></i> Alguns problemas ainda existem:</h6>";
    echo "<ul>";
    foreach ($problemas_encontrados as $problema) {
        echo "<li>" . htmlspecialchars($problema) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($correcoes_aplicadas)) {
    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-tools'></i> Correções aplicadas:</h6>";
    echo "<ul>";
    foreach ($correcoes_aplicadas as $correcao) {
        echo "<li>" . htmlspecialchars($correcao) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div class='mt-4'>";
echo "<h6>Teste agora:</h6>";
echo "<div class='d-grid gap-2 d-md-block'>";
echo "<a href='gerenciar.php' class='btn btn-primary'>Testar Gerenciamento</a> ";
echo "<a href='index.php' class='btn btn-success'>Acessar Sistema</a> ";
echo "<a href='test_connection.php' class='btn btn-info'>Teste de Conexão</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
