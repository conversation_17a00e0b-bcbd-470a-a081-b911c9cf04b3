<?php
require_once 'auth_check.php';

// Verificar se usuário tem permissão de administrador
$stmt = $pdo_mci->prepare("
    SELECT nivel_acesso 
    FROM mci_permissions 
    WHERE usuario_id = ? AND ativo = TRUE
");
$stmt->execute([$_SESSION['user_id']]);
$user_permission = $stmt->fetch();

if (!$user_permission || $user_permission['nivel_acesso'] !== 'administrador') {
    header('Location: ../access_denied.php');
    exit;
}

// Processar ações POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_permission':
                $usuario_id = $_POST['usuario_id'];
                $nivel_acesso = $_POST['nivel_acesso'];
                
                // Verificar se já existe permissão para este usuário
                $stmt = $pdo_mci->prepare("SELECT id FROM mci_permissions WHERE usuario_id = ?");
                $stmt->execute([$usuario_id]);
                $existing = $stmt->fetch();
                
                // Buscar nome do usuário para log
                $stmt_user = $pdo_sicoob->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
                $stmt_user->execute([$usuario_id]);
                $nome_usuario = $stmt_user->fetchColumn();

                if ($existing) {
                    // Atualizar permissão existente
                    $stmt = $pdo_mci->prepare("
                        UPDATE mci_permissions
                        SET nivel_acesso = ?, usuario_atualizacao = ?, data_atualizacao = NOW()
                        WHERE usuario_id = ?
                    ");
                    $stmt->execute([$nivel_acesso, $_SESSION['user_id'], $usuario_id]);

                    // Log para sistema centralizado
                    require_once 'classes/Logger.php';
                    $logger = new MciLogger();
                    $detalhes_log = "MCI - Atualização de permissão: Usuário '$nome_usuario' (ID: $usuario_id) teve permissão alterada para '$nivel_acesso'";
                    $logger->log('MCI - Atualização de permissão', $detalhes_log);
                } else {
                    // Criar nova permissão
                    $stmt = $pdo_mci->prepare("
                        INSERT INTO mci_permissions (usuario_id, nivel_acesso, ativo, usuario_criacao)
                        VALUES (?, ?, TRUE, ?)
                    ");
                    $stmt->execute([$usuario_id, $nivel_acesso, $_SESSION['user_id']]);

                    // Log para sistema centralizado
                    require_once 'classes/Logger.php';
                    $logger = new MciLogger();
                    $detalhes_log = "MCI - Nova permissão: Usuário '$nome_usuario' (ID: $usuario_id) recebeu permissão '$nivel_acesso'";
                    $logger->log('MCI - Criação de permissão', $detalhes_log);
                }

                $message = 'Permissão atualizada com sucesso!';
                break;
                
            case 'remove_permission':
                $usuario_id = $_POST['usuario_id'];

                // Buscar nome do usuário para log
                $stmt_user = $pdo_sicoob->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
                $stmt_user->execute([$usuario_id]);
                $nome_usuario = $stmt_user->fetchColumn();

                $stmt = $pdo_mci->prepare("
                    UPDATE mci_permissions
                    SET ativo = FALSE, usuario_atualizacao = ?, data_atualizacao = NOW()
                    WHERE usuario_id = ?
                ");
                $stmt->execute([$_SESSION['user_id'], $usuario_id]);

                // Log para sistema centralizado
                require_once 'classes/Logger.php';
                $logger = new MciLogger();
                $detalhes_log = "MCI - Remoção de permissão: Usuário '$nome_usuario' (ID: $usuario_id) teve permissão de acesso removida";
                $logger->log('MCI - Remoção de permissão', $detalhes_log);

                $message = 'Permissão removida com sucesso!';
                break;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Filtros
$filtro_nome = $_GET['nome'] ?? '';
$filtro_setor = $_GET['setor'] ?? '';
$filtro_nivel = $_GET['nivel'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Construir query com filtros
$where_conditions = ["u.ativo = TRUE"];
$params = [];

if ($filtro_nome) {
    $where_conditions[] = "(u.nome_completo LIKE ? OR u.username LIKE ?)";
    $params[] = "%$filtro_nome%";
    $params[] = "%$filtro_nome%";
}

if ($filtro_setor) {
    $where_conditions[] = "s.id = ?";
    $params[] = $filtro_setor;
}

if ($filtro_nivel) {
    if ($filtro_nivel === 'sem_permissao') {
        $where_conditions[] = "p.nivel_acesso IS NULL";
    } else {
        $where_conditions[] = "p.nivel_acesso = ? AND p.ativo = TRUE";
        $params[] = $filtro_nivel;
    }
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Contar total de registros
$count_query = "
    SELECT COUNT(DISTINCT u.id)
    FROM sicoob_access_control.usuarios u
    LEFT JOIN sicoob_access_control.usuario_setor us ON u.id = us.usuario_id
    LEFT JOIN sicoob_access_control.setores s ON us.setor_id = s.id
    LEFT JOIN mci_permissions p ON u.id = p.usuario_id AND p.ativo = TRUE
    $where_clause
";
$stmt = $pdo_mci->prepare($count_query);
$stmt->execute($params);
$total_records = $stmt->fetchColumn();
$total_pages = ceil($total_records / $per_page);

// Buscar usuários
$query = "
    SELECT u.id, u.username, u.nome_completo, u.email,
           GROUP_CONCAT(DISTINCT s.nome ORDER BY s.nome SEPARATOR ', ') as setores,
           p.nivel_acesso, p.data_criacao as permissao_criada,
           uc.nome_completo as criado_por, ua.nome_completo as atualizado_por
    FROM sicoob_access_control.usuarios u
    LEFT JOIN sicoob_access_control.usuario_setor us ON u.id = us.usuario_id
    LEFT JOIN sicoob_access_control.setores s ON us.setor_id = s.id
    LEFT JOIN mci_permissions p ON u.id = p.usuario_id AND p.ativo = TRUE
    LEFT JOIN sicoob_access_control.usuarios uc ON p.usuario_criacao = uc.id
    LEFT JOIN sicoob_access_control.usuarios ua ON p.usuario_atualizacao = ua.id
    $where_clause
    GROUP BY u.id, u.username, u.nome_completo, u.email, p.nivel_acesso, p.data_criacao, uc.nome_completo, ua.nome_completo
    ORDER BY u.nome_completo
    LIMIT $per_page OFFSET $offset
";

$stmt = $pdo_mci->prepare($query);
$stmt->execute($params);
$usuarios = $stmt->fetchAll();

// Buscar setores para filtro
$stmt = $pdo_sicoob->prepare("SELECT id, nome FROM setores WHERE ativo = TRUE ORDER BY nome");
$stmt->execute();
$setores_disponiveis = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Permissões - <?php echo MCI_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
        }

        body { background-color: #f8f9fa; }
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .btn-primary { background-color: var(--sicoob-verde-escuro); border-color: var(--sicoob-verde-escuro); }
        .btn-primary:hover { background-color: var(--sicoob-turquesa); border-color: var(--sicoob-turquesa); }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        
        .permission-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        
        .badge-comum { background-color: #6c757d; }
        .badge-gestor { background-color: #fd7e14; }
        .badge-administrador { background-color: #dc3545; }
        .badge-sem-permissao { background-color: #e9ecef; color: #6c757d; }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .table td {
            font-size: 0.9rem;
            vertical-align: middle;
        }
        
        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        /* Estilos para filtros compactos */
        .form-label.small {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .form-select-sm, .form-control-sm {
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
        }
        
        .btn-sm {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt"></i> <?php echo MCI_PROJECT_NAME; ?> - Permissões
            </a>
            <div class="navbar-nav ms-auto">
                <!-- Atalhos para Dashboards -->
                <a class="nav-link" href="cadastro/dashboard.php" target="_blank">
                    <i class="fas fa-tv"></i> Dashboard Cadastro
                </a>
                <a class="nav-link" href="tecagricola/dashboard.php" target="_blank">
                    <i class="fas fa-seedling"></i> Dashboard Técnicos
                </a>

                <a class="nav-link" href="gerenciar.php">
                    <i class="fas fa-arrow-left"></i> Voltar para Registros
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Alertas -->
        <?php if (isset($message)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter"></i> Filtros
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-2 align-items-end">
                    <div class="col-lg-3 col-md-4">
                        <label class="form-label small">Nome ou Usuário</label>
                        <input type="text" name="nome" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filtro_nome); ?>" placeholder="Digite nome ou usuário...">
                    </div>
                    <div class="col-lg-2 col-md-3">
                        <label class="form-label small">Setor</label>
                        <select name="setor" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <?php foreach ($setores_disponiveis as $setor): ?>
                            <option value="<?php echo $setor['id']; ?>" <?php echo $filtro_setor == $setor['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($setor['nome']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-3">
                        <label class="form-label small">Nível de Acesso</label>
                        <select name="nivel" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <option value="administrador" <?php echo $filtro_nivel == 'administrador' ? 'selected' : ''; ?>>Administrador</option>
                            <option value="gestor" <?php echo $filtro_nivel == 'gestor' ? 'selected' : ''; ?>>Gestor</option>
                            <option value="comum" <?php echo $filtro_nivel == 'comum' ? 'selected' : ''; ?>>Comum</option>
                            <option value="sem_permissao" <?php echo $filtro_nivel == 'sem_permissao' ? 'selected' : ''; ?>>Sem Permissão</option>
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-4">
                        <button type="submit" class="btn btn-primary btn-sm me-1">
                            <i class="fas fa-search"></i> Filtrar
                        </button>
                        <a href="permissions.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times"></i> Limpar
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabela de Usuários -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-users"></i> Usuários e Permissões
                </h6>
                <span class="badge bg-light text-dark"><?php echo $total_records; ?> usuários encontrados</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Usuário</th>
                                <th>Nome Completo</th>
                                <th>Email</th>
                                <th>Setores</th>
                                <th>Nível de Acesso</th>
                                <th>Permissão Criada</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($usuarios as $usuario): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($usuario['username']); ?></strong>
                                    <br><small class="text-muted">ID: <?php echo $usuario['id']; ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($usuario['nome_completo']); ?></td>
                                <td>
                                    <?php if ($usuario['email']): ?>
                                        <a href="mailto:<?php echo htmlspecialchars($usuario['email']); ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($usuario['email']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($usuario['setores']): ?>
                                        <small><?php echo htmlspecialchars($usuario['setores']); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($usuario['nivel_acesso']): ?>
                                        <span class="badge permission-badge badge-<?php echo $usuario['nivel_acesso']; ?>">
                                            <?php echo ucfirst($usuario['nivel_acesso']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge permission-badge badge-sem-permissao">
                                            Sem Permissão
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($usuario['permissao_criada']): ?>
                                        <br><small class="text-muted">
                                            Criada em: <?php echo date('d/m/Y', strtotime($usuario['permissao_criada'])); ?>
                                            <?php if ($usuario['criado_por']): ?>
                                                por <?php echo htmlspecialchars($usuario['criado_por']); ?>
                                            <?php endif; ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($usuario['permissao_criada']): ?>
                                        <?php echo date('d/m/Y H:i', strtotime($usuario['permissao_criada'])); ?>
                                        <?php if ($usuario['atualizado_por']): ?>
                                            <br><small class="text-muted">
                                                Atualizado por: <?php echo htmlspecialchars($usuario['atualizado_por']); ?>
                                            </small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="action-buttons">
                                    <button class="btn btn-primary btn-sm" onclick="editarPermissao(<?php echo $usuario['id']; ?>, '<?php echo htmlspecialchars($usuario['nome_completo']); ?>', '<?php echo $usuario['nivel_acesso'] ?? ''; ?>')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php if ($usuario['nivel_acesso']): ?>
                                        <button class="btn btn-danger btn-sm" onclick="removerPermissao(<?php echo $usuario['id']; ?>, '<?php echo htmlspecialchars($usuario['nome_completo']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Paginação -->
        <?php if ($total_pages > 1): ?>
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&nome=<?php echo $filtro_nome; ?>&setor=<?php echo $filtro_setor; ?>&nivel=<?php echo $filtro_nivel; ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&nome=<?php echo $filtro_nome; ?>&setor=<?php echo $filtro_setor; ?>&nivel=<?php echo $filtro_nivel; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&nome=<?php echo $filtro_nome; ?>&setor=<?php echo $filtro_setor; ?>&nivel=<?php echo $filtro_nivel; ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

    <!-- Modal Editar Permissão -->
    <div class="modal fade" id="editPermissionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-shield"></i> Editar Permissão
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editPermissionForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_permission">
                        <input type="hidden" name="usuario_id" id="edit_usuario_id">

                        <div class="mb-3">
                            <label class="form-label">Usuário</label>
                            <input type="text" id="edit_usuario_nome" class="form-control" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Nível de Acesso</label>
                            <select name="nivel_acesso" id="edit_nivel_acesso" class="form-select" required>
                                <option value="">Selecione o nível...</option>
                                <option value="comum">Comum</option>
                                <option value="gestor">Gestor</option>
                                <option value="administrador">Administrador</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Níveis de Acesso:</h6>
                            <ul class="mb-0">
                                <li><strong>Comum:</strong> Acesso básico ao sistema</li>
                                <li><strong>Gestor:</strong> Acesso de supervisão e relatórios</li>
                                <li><strong>Administrador:</strong> Acesso total, incluindo gerenciamento de permissões</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Salvar Permissão
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Remover Permissão -->
    <div class="modal fade" id="removePermissionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle"></i> Remover Permissão
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="removePermissionForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="remove_permission">
                        <input type="hidden" name="usuario_id" id="remove_usuario_id">

                        <p>Tem certeza que deseja remover a permissão de acesso do usuário:</p>
                        <p><strong id="remove_usuario_nome"></strong></p>
                        <p class="text-muted">O usuário perderá o acesso ao sistema MCI.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Remover Permissão
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editarPermissao(usuarioId, nomeUsuario, nivelAtual) {
            document.getElementById('edit_usuario_id').value = usuarioId;
            document.getElementById('edit_usuario_nome').value = nomeUsuario;
            document.getElementById('edit_nivel_acesso').value = nivelAtual;

            new bootstrap.Modal(document.getElementById('editPermissionModal')).show();
        }

        function removerPermissao(usuarioId, nomeUsuario) {
            document.getElementById('remove_usuario_id').value = usuarioId;
            document.getElementById('remove_usuario_nome').textContent = nomeUsuario;

            new bootstrap.Modal(document.getElementById('removePermissionModal')).show();
        }

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
