<?php
// <PERSON><PERSON> as configurações do projeto principal
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/database.php';

// Configurações específicas do projeto MCI
define('MCI_PROJECT_NAME', 'Sistema de Atualizações Cadastrais');
define('MCI_PROJECT_VERSION', '1.0.0');

// Configurações de Timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurações de Debug
define('MCI_DEBUG_MODE', true);

// Configurações de Log
define('MCI_LOG_PATH', __DIR__ . '/../logs/');

// Configurações de Upload
define('MCI_UPLOAD_PATH', __DIR__ . '/../uploads/');
define('MCI_ALLOWED_EXTENSIONS', ['xlsx', 'xls']);
define('MCI_MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB

// Criar diretórios se não existirem
if (!file_exists(MCI_LOG_PATH)) {
    mkdir(MCI_LOG_PATH, 0777, true);
}

if (!file_exists(MCI_UPLOAD_PATH)) {
    mkdir(MCI_UPLOAD_PATH, 0777, true);
}
?>
