<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Corrigir Status dos Registros</h1>";
echo "<hr>";

try {
    echo "<h3>1. Verificando estrutura atual...</h3>";
    
    // Verificar se a tabela cad_status existe e tem dados
    $stmt = $pdo_mci->prepare("SELECT * FROM cad_status ORDER BY id");
    $stmt->execute();
    $status_disponiveis = $stmt->fetchAll();
    
    if (empty($status_disponiveis)) {
        echo "<p style='color: red;'>❌ Tabela cad_status não encontrada ou vazia!</p>";
        echo "<p><a href='criar_tabela_status.php'>Criar tabela de status primeiro</a></p>";
        exit;
    }
    
    echo "<h4>Status disponíveis na tabela cad_status:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Descrição</th><th>Cor</th></tr>";
    foreach ($status_disponiveis as $status) {
        $cor_preview = "<span style='background-color: {$status['cor']}; color: white; padding: 2px 8px; border-radius: 3px;'>{$status['nome']}</span>";
        echo "<tr>";
        echo "<td><strong>{$status['id']}</strong></td>";
        echo "<td>$cor_preview</td>";
        echo "<td>{$status['descricao']}</td>";
        echo "<td>{$status['cor']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar estrutura da coluna status em cad_registros
    echo "<h3>2. Verificando coluna status em cad_registros...</h3>";
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field = 'status'");
    $stmt->execute();
    $column_info = $stmt->fetch();
    
    if (!$column_info) {
        echo "<p style='color: red;'>❌ Coluna status não encontrada na tabela cad_registros!</p>";
        echo "<p><a href='criar_tabela_status.php'>Executar criação da estrutura</a></p>";
        exit;
    }
    
    echo "<p><strong>Tipo da coluna status:</strong> <code>{$column_info['Type']}</code></p>";
    echo "<p><strong>Padrão:</strong> <code>" . ($column_info['Default'] ?? 'NULL') . "</code></p>";
    
    // Verificar dados atuais
    echo "<h3>3. Analisando dados atuais...</h3>";
    
    $stmt = $pdo_mci->prepare("
        SELECT 
            r.status,
            COUNT(*) as total,
            MIN(r.id) as primeiro_id,
            MAX(r.id) as ultimo_id
        FROM cad_registros r 
        GROUP BY r.status 
        ORDER BY r.status
    ");
    $stmt->execute();
    $status_atuais = $stmt->fetchAll();
    
    echo "<h4>Distribuição atual dos valores na coluna status:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Valor Atual</th><th>Tipo</th><th>Total Registros</th><th>Primeiro ID</th><th>Último ID</th><th>Status</th></tr>";
    
    $problemas_encontrados = 0;
    
    foreach ($status_atuais as $status_atual) {
        $valor = $status_atual['status'];
        $tipo = is_numeric($valor) ? 'Numérico' : 'Texto';
        $status_info = '';
        
        // Verificar se o valor corresponde a um ID válido
        if (is_numeric($valor)) {
            $status_encontrado = array_filter($status_disponiveis, function($s) use ($valor) {
                return $s['id'] == $valor;
            });
            
            if (!empty($status_encontrado)) {
                $status_obj = array_values($status_encontrado)[0];
                $status_info = "✅ Válido: {$status_obj['nome']}";
            } else {
                $status_info = "❌ ID inválido";
                $problemas_encontrados++;
            }
        } else {
            // Verificar se é um nome de status válido
            $status_encontrado = array_filter($status_disponiveis, function($s) use ($valor) {
                return strtolower($s['nome']) == strtolower($valor);
            });
            
            if (!empty($status_encontrado)) {
                $status_info = "⚠️ Nome válido, mas precisa converter para ID";
                $problemas_encontrados++;
            } else {
                $status_info = "❌ Valor inválido";
                $problemas_encontrados++;
            }
        }
        
        echo "<tr>";
        echo "<td><code>" . htmlspecialchars($valor ?? 'NULL') . "</code></td>";
        echo "<td>$tipo</td>";
        echo "<td><strong>{$status_atual['total']}</strong></td>";
        echo "<td>{$status_atual['primeiro_id']}</td>";
        echo "<td>{$status_atual['ultimo_id']}</td>";
        echo "<td>$status_info</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>4. Resumo da Análise</h3>";
    
    if ($problemas_encontrados == 0) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #155724;'>🎉 Todos os registros estão corretos!</h4>";
        echo "<p style='color: #155724;'>Todos os valores na coluna status correspondem a IDs válidos da tabela cad_status.</p>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #856404;'>⚠️ Problemas encontrados: $problemas_encontrados</h4>";
        echo "<p style='color: #856404;'>Alguns registros precisam ser corrigidos para usar os IDs corretos.</p>";
        echo "</div>";
        
        echo "<h3>5. Correção Automática</h3>";
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['corrigir_status'])) {
            echo "<h4>Executando correções...</h4>";
            
            $corrigidos = 0;
            $erros = 0;
            
            // Corrigir valores de texto para IDs
            foreach ($status_disponiveis as $status) {
                try {
                    $stmt = $pdo_mci->prepare("
                        UPDATE cad_registros 
                        SET status = ? 
                        WHERE status = ? OR LOWER(status) = LOWER(?)
                    ");
                    $stmt->execute([$status['id'], $status['nome'], $status['nome']]);
                    $affected = $stmt->rowCount();
                    
                    if ($affected > 0) {
                        echo "<p>✅ Convertidos $affected registros de '{$status['nome']}' para ID {$status['id']}</p>";
                        $corrigidos += $affected;
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Erro ao converter '{$status['nome']}': " . htmlspecialchars($e->getMessage()) . "</p>";
                    $erros++;
                }
            }
            
            // Corrigir valores NULL ou vazios para "Pendente" (ID 1)
            try {
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros 
                    SET status = 1 
                    WHERE status IS NULL OR status = '' OR status = '0'
                ");
                $stmt->execute();
                $affected = $stmt->rowCount();
                
                if ($affected > 0) {
                    echo "<p>✅ Definidos $affected registros sem status como 'Pendente' (ID 1)</p>";
                    $corrigidos += $affected;
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro ao corrigir registros vazios: " . htmlspecialchars($e->getMessage()) . "</p>";
                $erros++;
            }
            
            // Corrigir valores inválidos para "Pendente" (ID 1)
            $ids_validos = array_column($status_disponiveis, 'id');
            $ids_validos_str = implode(',', $ids_validos);
            
            try {
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros 
                    SET status = 1 
                    WHERE status NOT IN ($ids_validos_str)
                ");
                $stmt->execute();
                $affected = $stmt->rowCount();
                
                if ($affected > 0) {
                    echo "<p>✅ Corrigidos $affected registros com valores inválidos para 'Pendente' (ID 1)</p>";
                    $corrigidos += $affected;
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro ao corrigir valores inválidos: " . htmlspecialchars($e->getMessage()) . "</p>";
                $erros++;
            }
            
            echo "<hr>";
            echo "<h4>Resultado da Correção:</h4>";
            echo "<p><strong>Registros corrigidos:</strong> $corrigidos</p>";
            echo "<p><strong>Erros:</strong> $erros</p>";
            
            if ($erros == 0) {
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
                echo "<h4 style='color: #155724;'>🎉 Correção concluída com sucesso!</h4>";
                echo "<p style='color: #155724;'>Todos os registros agora usam IDs válidos da tabela cad_status.</p>";
                echo "</div>";
            }
            
            // Mostrar estatísticas finais
            echo "<h4>Estatísticas Finais:</h4>";
            $stmt = $pdo_mci->prepare("
                SELECT 
                    s.id,
                    s.nome,
                    s.cor,
                    COUNT(r.id) as total_registros
                FROM cad_status s
                LEFT JOIN cad_registros r ON r.status = s.id
                WHERE s.ativo = TRUE
                GROUP BY s.id, s.nome, s.cor
                ORDER BY s.id
            ");
            $stmt->execute();
            $stats_finais = $stmt->fetchAll();
            
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Status</th><th>Total de Registros</th></tr>";
            foreach ($stats_finais as $stat) {
                $cor_preview = "<span style='background-color: {$stat['cor']}; color: white; padding: 2px 8px; border-radius: 3px;'>{$stat['nome']}</span>";
                echo "<tr>";
                echo "<td>$cor_preview</td>";
                echo "<td><strong>{$stat['total_registros']}</strong></td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            // Mostrar formulário de correção
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4 style='color: #721c24;'>🔧 Correção Necessária</h4>";
            echo "<p style='color: #721c24;'>Esta ação irá:</p>";
            echo "<ul style='color: #721c24;'>";
            echo "<li>Converter nomes de status para IDs correspondentes</li>";
            echo "<li>Definir registros sem status como 'Pendente' (ID 1)</li>";
            echo "<li>Corrigir valores inválidos para 'Pendente' (ID 1)</li>";
            echo "<li>Garantir integridade referencial com cad_status</li>";
            echo "</ul>";
            echo "</div>";
            
            echo "<form method='POST'>";
            echo "<div style='margin: 20px 0;'>";
            echo "<label>";
            echo "<input type='checkbox' name='corrigir_status' required> ";
            echo "<strong>Eu confirmo que desejo corrigir os valores de status</strong>";
            echo "</label>";
            echo "</div>";
            
            echo "<button type='submit' style='background-color: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold;'>🔧 CORRIGIR STATUS</button>";
            echo " ";
            echo "<a href='gerenciar.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>❌ Cancelar</a>";
            echo "</form>";
        }
    }
    
    echo "<hr>";
    echo "<h3>Mapeamento de Status</h3>";
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px;'>";
    echo "<h4>📋 Como deve funcionar:</h4>";
    echo "<ul>";
    echo "<li><strong>ID 1:</strong> Pendente - Registros aguardando atualização</li>";
    echo "<li><strong>ID 2:</strong> Atualizado - Registros já processados</li>";
    echo "</ul>";
    echo "<p><strong>Importante:</strong> A coluna status deve sempre conter apenas IDs numéricos que existem na tabela cad_status.</p>";
    echo "</div>";
    
    echo "<p><a href='gerenciar.php'>Ver registros</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a verificação</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
