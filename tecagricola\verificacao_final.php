<?php
require_once '../auth_check.php';

echo "<h2>✅ Verificação Final - Dashboard Técnicos Agrícolas</h2>";

// Executar exatamente a mesma consulta do dashboard
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

echo "<h3>📊 Resultado da Consulta</h3>";
echo "<p><strong>Data/Hora:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Total de técnicos:</strong> " . count($tecnicos_dados) . "</p>";

echo "<h3>🎯 Técnicos que DEVEM aparecer no dashboard:</h3>";

if (count($tecnicos_dados) > 0) {
    echo "<div style='display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin: 20px 0;'>";
    
    $tecnico_index = 1;
    foreach ($tecnicos_dados as $tecnico) {
        $cores = ['#00AE9D', '#003641', '#C9D200', '#70B86C', '#494790', '#6c757d'];
        $cor = $cores[($tecnico_index - 1) % count($cores)];
        
        echo "<div style='border: 3px solid $cor; border-radius: 10px; padding: 15px; text-align: center; background: white;'>";
        echo "<h4 style='margin: 0 0 10px 0; color: #003641;'>Card $tecnico_index</h4>";
        echo "<div style='width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #003641, #00AE9D); margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 24px;'>";
        echo strtoupper(substr($tecnico['nome'], 0, 1));
        echo "</div>";
        echo "<strong>" . htmlspecialchars($tecnico['nome']) . "</strong><br>";
        echo "<small>ID: " . $tecnico['id'] . "</small><br>";
        echo "<div style='margin: 10px 0;'>";
        echo "<div style='color: #003641; font-weight: bold;'>Meta Total</div>";
        echo "<div style='color: $cor; font-size: 18px; font-weight: bold;'>" . $tecnico['atualizados_total'] . "/" . $tecnico['meta_total'] . "</div>";
        echo "<div style='color: #003641; font-weight: bold;'>" . $tecnico['progresso_total'] . "%</div>";
        echo "</div>";
        echo "</div>";
        
        $tecnico_index++;
    }
    
    echo "</div>";
    
    echo "<h3>📋 Lista Detalhada:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th>Posição</th><th>ID</th><th>Nome Completo</th><th>Email</th><th>Meta</th><th>Atualizados</th><th>Progresso</th></tr>";
    
    $posicao = 1;
    foreach ($tecnicos_dados as $tecnico) {
        echo "<tr>";
        echo "<td style='text-align: center; font-weight: bold;'>$posicao</td>";
        echo "<td style='text-align: center;'>" . $tecnico['id'] . "</td>";
        echo "<td>" . htmlspecialchars($tecnico['nome']) . "</td>";
        echo "<td>" . htmlspecialchars($tecnico['email']) . "</td>";
        echo "<td style='text-align: center;'>" . $tecnico['meta_total'] . "</td>";
        echo "<td style='text-align: center;'>" . $tecnico['atualizados_total'] . "</td>";
        echo "<td style='text-align: center;'>" . $tecnico['progresso_total'] . "%</td>";
        echo "</tr>";
        $posicao++;
    }
    echo "</table>";
    
} else {
    echo "<p style='color: red;'>❌ <strong>Nenhum técnico encontrado!</strong></p>";
}

echo "<h3>🔍 Verificações:</h3>";

// Verificar se Felipe está presente
$felipe_presente = false;
$sidnei_presente = false;
$maycon_presente = false;

foreach ($tecnicos_dados as $tecnico) {
    if (stripos($tecnico['nome'], 'Felipe') !== false) {
        $felipe_presente = true;
    }
    if (stripos($tecnico['nome'], 'Sidnei') !== false) {
        $sidnei_presente = true;
    }
    if (stripos($tecnico['nome'], 'Maycon') !== false) {
        $maycon_presente = true;
    }
}

echo "<ul>";
echo "<li>" . ($felipe_presente ? "✅" : "❌") . " <strong>Felipe Jorge Dutra</strong> " . ($felipe_presente ? "está presente" : "NÃO está presente") . "</li>";
echo "<li>" . ($sidnei_presente ? "✅" : "❌") . " <strong>Sidnei Gama</strong> " . ($sidnei_presente ? "está presente" : "NÃO está presente") . "</li>";
echo "<li>" . ($maycon_presente ? "✅" : "❌") . " <strong>Maycon de Souza Dias</strong> " . ($maycon_presente ? "está presente" : "NÃO está presente") . "</li>";
echo "</ul>";

// Verificar duplicações
$nomes = array_column($tecnicos_dados, 'nome');
$nomes_unicos = array_unique($nomes);

if (count($nomes) === count($nomes_unicos)) {
    echo "<p style='color: green;'>✅ <strong>Nenhuma duplicação encontrada</strong></p>";
} else {
    echo "<p style='color: red;'>❌ <strong>Duplicações encontradas!</strong></p>";
    $duplicados = array_diff_assoc($nomes, $nomes_unicos);
    foreach ($duplicados as $nome) {
        echo "<p style='color: red;'>- " . htmlspecialchars($nome) . "</p>";
    }
}

echo "<h3>🚀 Ações:</h3>";
echo "<p><a href='dashboard.php' target='_blank' style='background: #00AE9D; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📊 Abrir Dashboard</a></p>";
echo "<p><a href='clear_cache.php' style='background: #003641; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧹 Limpar Cache</a></p>";

echo "<hr>";
echo "<p><em>Esta página mostra exatamente como o dashboard deveria aparecer com base nos dados do banco.</em></p>";
?>
