<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

echo "<h1>Teste de Importação - Sistema MCI</h1>";
echo "<hr>";

// Função para converter datas do Excel
function convertExcelDate($value) {
    if (empty($value)) {
        return null;
    }

    // Se for uma string, tentar converter
    if (is_string($value)) {
        $value = trim($value);
        if (empty($value)) {
            return null;
        }

        // FORMATO BRASILEIRO: dd/mm/yyyy (PRIORIDADE MÁXIMA)
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $value, $matches)) {
            $dia = intval($matches[1]);
            $mes = intval($matches[2]);
            $ano = intval($matches[3]);

            // Validar se a data é válida (dia, mês, ano)
            if ($dia >= 1 && $dia <= 31 && $mes >= 1 && $mes <= 12 && $ano >= 1900 && $ano <= 2100) {
                // Verificar se a data é realmente válida
                if (checkdate($mes, $dia, $ano)) {
                    // Formatar com zeros à esquerda
                    $dia_fmt = str_pad($dia, 2, '0', STR_PAD_LEFT);
                    $mes_fmt = str_pad($mes, 2, '0', STR_PAD_LEFT);

                    return "$ano-$mes_fmt-$dia_fmt";
                }
            }
        }

        // FORMATO BRASILEIRO: dd-mm-yyyy
        if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $value, $matches)) {
            $dia = intval($matches[1]);
            $mes = intval($matches[2]);
            $ano = intval($matches[3]);

            if ($dia >= 1 && $dia <= 31 && $mes >= 1 && $mes <= 12 && $ano >= 1900 && $ano <= 2100) {
                if (checkdate($mes, $dia, $ano)) {
                    $dia_fmt = str_pad($dia, 2, '0', STR_PAD_LEFT);
                    $mes_fmt = str_pad($mes, 2, '0', STR_PAD_LEFT);

                    return "$ano-$mes_fmt-$dia_fmt";
                }
            }
        }

        // FORMATO ISO: yyyy-mm-dd (já no formato correto)
        if (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $value, $matches)) {
            $ano = intval($matches[1]);
            $mes = intval($matches[2]);
            $dia = intval($matches[3]);

            if (checkdate($mes, $dia, $ano)) {
                $dia_fmt = str_pad($dia, 2, '0', STR_PAD_LEFT);
                $mes_fmt = str_pad($mes, 2, '0', STR_PAD_LEFT);

                return "$ano-$mes_fmt-$dia_fmt";
            }
        }
    }

    return null;
}

// Função para limpar valores monetários
function cleanMoneyValue($value) {
    if (empty($value)) {
        return null;
    }

    // Se for numérico, retornar como float
    if (is_numeric($value)) {
        return floatval($value);
    }

    // Converter para string e limpar
    $value = trim((string)$value);

    if (empty($value)) {
        return null;
    }

    // Remover símbolos de moeda e espaços
    $cleaned = preg_replace('/[R$\s]/', '', $value);

    // Verificar se tem formato brasileiro (1.234,56)
    if (preg_match('/^\d{1,3}(\.\d{3})*,\d{2}$/', $cleaned)) {
        // Formato brasileiro: remover pontos e trocar vírgula por ponto
        $cleaned = str_replace('.', '', $cleaned);
        $cleaned = str_replace(',', '.', $cleaned);
    }
    // Verificar se tem formato americano (1,234.56)
    elseif (preg_match('/^\d{1,3}(,\d{3})*\.\d{2}$/', $cleaned)) {
        // Formato americano: remover vírgulas
        $cleaned = str_replace(',', '', $cleaned);
    }
    // Verificar se tem apenas vírgula como separador decimal (123,45)
    elseif (preg_match('/^\d+,\d{1,2}$/', $cleaned)) {
        // Trocar vírgula por ponto
        $cleaned = str_replace(',', '.', $cleaned);
    }
    // Verificar se tem apenas ponto como separador decimal (123.45)
    elseif (preg_match('/^\d+\.\d{1,2}$/', $cleaned)) {
        // Já está no formato correto
    }
    // Se for apenas números, assumir que está em centavos se for muito grande
    elseif (preg_match('/^\d+$/', $cleaned)) {
        $numero = intval($cleaned);
        // Se for maior que 100000, assumir que está em centavos
        if ($numero > 100000) {
            $cleaned = number_format($numero / 100, 2, '.', '');
        }
    }
    else {
        // Limpar caracteres não numéricos exceto vírgula e ponto
        $cleaned = preg_replace('/[^\d,.-]/', '', $cleaned);

        // Se ainda tem vírgula e ponto, assumir formato brasileiro
        if (strpos($cleaned, ',') !== false && strpos($cleaned, '.') !== false) {
            $cleaned = str_replace('.', '', $cleaned);
            $cleaned = str_replace(',', '.', $cleaned);
        }
        // Se tem apenas vírgula, trocar por ponto
        elseif (strpos($cleaned, ',') !== false) {
            $cleaned = str_replace(',', '.', $cleaned);
        }
    }

    return is_numeric($cleaned) ? floatval($cleaned) : null;
}

// Função para tratar CPF/CNPJ
function formatCpfCnpj($value) {
    if (empty($value)) {
        return '';
    }

    // Limpar caracteres não numéricos
    $cleaned = preg_replace('/[^0-9]/', '', $value);

    if (empty($cleaned)) {
        return '';
    }

    $length = strlen($cleaned);

    // Se tem 11 dígitos ou menos, assumir que é CPF e completar com zeros à esquerda até 11 dígitos
    if ($length <= 11) {
        return str_pad($cleaned, 11, '0', STR_PAD_LEFT);
    }
    // Se tem 12-14 dígitos, assumir que é CNPJ e completar com zeros à esquerda até 14 dígitos
    elseif ($length <= 14) {
        return str_pad($cleaned, 14, '0', STR_PAD_LEFT);
    }
    // Se tem mais de 14 dígitos, retornar apenas os primeiros 14
    else {
        return substr($cleaned, 0, 14);
    }
}

// Criar dados de teste com diferentes formatos de CPF/CNPJ
$dados_teste = [
    ['PA', 'Nome Cliente', 'Número CPF/CNPJ', 'CNAE', 'Data Última Atualização Renda', 'Sigla Tipo Pessoa', 'Profissão', 'Depósito Total', 'Saldo Devedor', 'FUNCIONÁRIO', 'DATA DA SOLICITAÇÃO DO LAUDO', 'TÉCNICO RESPONSÁVEL', 'DATA DA ATUAL. SISBR'],
    ['0', 'Cliente PA Zero', '123456789', '1234-5/67', '02/01/2024', 'PF', 'Comerciante', '15.000,50', '2.300,75', 'Maria Oliveira', '10/01/2024', 'Carlos Pereira', '20/01/2024'],
    ['001', 'João Silva Santos', '123.456.789-01', '1234-5/67', '15/01/2024', 'PF', 'Comerciante', '15.000,50', '2.300,75', 'Maria Oliveira', '10/01/2024', 'Carlos Pereira', '20/01/2024'],
    ['002', 'Empresa ABC Ltda', '12.345.678/0001-23', '5678-9/01', '10/02/2024', 'PJ', 'Comércio', '50.000,00', '8.750,00', 'Ana Costa', '05/02/2024', 'Roberto Lima', '15/02/2024'],
    ['003', 'Pedro Almeida', '9876543210', '2345-6/78', '25/02/2024', 'PF', 'Autônomo', '8.500,75', '950,25', 'José Santos', '20/02/2024', 'Fernanda Silva', '01/03/2024'],
    ['004', 'Loja XYZ ME', '987654320001', '4567-8/90', '12/03/2024', 'PJ', 'Varejo', '25.000,00', '4.200,00', 'Lucia Ferreira', '15/03/2024', 'Roberto Silva', '18/03/2024'],
    ['005', 'Maria Conceição', '111.222.333-44', '9876-5/43', '05/04/2024', 'PF', 'Professora', '12.000,00', '1.800,00', 'Paulo Rodrigues', '01/04/2024', 'Sandra Martins', '10/04/2024']
];

try {
    $logger = new MciLogger();
    
    echo "<h3>1. Testando processamento dos dados...</h3>";
    
    // Simular o processamento sem inserir no banco
    $header = array_shift($dados_teste);
    echo "<p><strong>Cabeçalho encontrado:</strong> " . implode(' | ', $header) . "</p>";
    
    echo "<h4>Dados processados:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Linha</th><th>PA</th><th>Nome</th><th>CPF/CNPJ</th><th>Status Validação</th></tr>";
    
    foreach ($dados_teste as $index => $row) {
        $linha_num = $index + 2; // +2 porque removemos o cabeçalho e começamos do 1

        try {
            // Pular linhas vazias
            if (empty(array_filter($row))) {
                continue;
            }
            
            // Mapear colunas da planilha
            $data = [
                'pa' => trim($row[0] ?? ''),
                'nome_cliente' => trim($row[1] ?? ''),
                'numero_cpf_cnpj' => formatCpfCnpj($row[2] ?? ''),
                'cnae' => trim($row[3] ?? ''),
                'data_ultima_atualizacao_renda' => convertExcelDate($row[4] ?? ''),
                'sigla_tipo_pessoa' => trim($row[5] ?? ''),
                'profissao' => trim($row[6] ?? ''),
                'deposito_total' => cleanMoneyValue($row[7] ?? ''),
                'saldo_devedor' => cleanMoneyValue($row[8] ?? ''),
                'funcionario' => trim($row[9] ?? ''),
                'data_solicitacao_laudo' => convertExcelDate($row[10] ?? ''),
                'tecnico_responsavel' => trim($row[11] ?? ''),
                'data_atual_sisbr' => convertExcelDate($row[12] ?? ''),
                'usuario_cadastro' => $_SESSION['user_id']
            ];
            
            // Validações obrigatórias
            $erros = [];
            if (empty($data['pa'])) {
                $erros[] = 'PA é obrigatório';
            }
            if (empty($data['nome_cliente'])) {
                $erros[] = 'Nome Cliente é obrigatório';
            }
            if (empty($data['numero_cpf_cnpj'])) {
                $erros[] = 'CPF/CNPJ é obrigatório';
            }
            
            // Validar tamanho dos campos
            if (strlen($data['pa']) > 10) {
                $erros[] = 'PA muito longo';
            }
            if (strlen($data['nome_cliente']) > 255) {
                $erros[] = 'Nome muito longo';
            }
            if (strlen($data['numero_cpf_cnpj']) > 20) {
                $erros[] = 'CPF/CNPJ muito longo';
            }
            
            $status = empty($erros) ? '✅ OK' : '❌ ' . implode(', ', $erros);
            
            echo "<tr>";
            echo "<td>$linha_num</td>";
            echo "<td>" . htmlspecialchars($data['pa']) . "</td>";
            echo "<td>" . htmlspecialchars($data['nome_cliente']) . "</td>";
            echo "<td>" . htmlspecialchars($data['numero_cpf_cnpj']) . "</td>";
            echo "<td>$status</td>";
            echo "</tr>";
            
        } catch (Exception $e) {
            echo "<tr>";
            echo "<td>$linha_num</td>";
            echo "<td colspan='3'>" . htmlspecialchars(implode(' | ', array_slice($row, 0, 3))) . "</td>";
            echo "<td>❌ Erro: " . htmlspecialchars($e->getMessage()) . "</td>";
            echo "</tr>";
        }
    }
    
    echo "</table>";
    
    // Teste de inserção real
    echo "<h3>2. Teste de inserção no banco (1 registro)...</h3>";
    
    try {
        $data_teste = [
            'pa' => '999',
            'nome_cliente' => 'Teste Sistema MCI',
            'numero_cpf_cnpj' => '12345678901',
            'cnae' => '1234-5/67',
            'data_ultima_atualizacao_renda' => '2024-01-15',
            'sigla_tipo_pessoa' => 'PF',
            'profissao' => 'Teste',
            'deposito_total' => 1000.00,
            'saldo_devedor' => 150.00,
            'funcionario' => 'Sistema',
            'data_solicitacao_laudo' => '2024-01-10',
            'tecnico_responsavel' => 'Teste',
            'data_atual_sisbr' => '2024-01-20',
            'usuario_cadastro' => $_SESSION['user_id'],
            'status' => 1 // Status "Pendente"
        ];
        
        // Verificar se já existe
        $stmt = $pdo_mci->prepare("SELECT id FROM cad_registros WHERE pa = ? AND nome_cliente = ?");
        $stmt->execute([$data_teste['pa'], $data_teste['nome_cliente']]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p>⚠️ Registro de teste já existe no banco.</p>";
        } else {
            // Inserir registro de teste
            $stmt = $pdo_mci->prepare("
                INSERT INTO cad_registros (
                    pa, nome_cliente, numero_cpf_cnpj, cnae, data_ultima_atualizacao_renda,
                    sigla_tipo_pessoa, profissao, deposito_total, saldo_devedor, funcionario,
                    data_solicitacao_laudo, tecnico_responsavel, data_atual_sisbr, usuario_cadastro, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $data_teste['pa'], $data_teste['nome_cliente'], $data_teste['numero_cpf_cnpj'],
                $data_teste['cnae'], $data_teste['data_ultima_atualizacao_renda'],
                $data_teste['sigla_tipo_pessoa'], $data_teste['profissao'], $data_teste['deposito_total'],
                $data_teste['saldo_devedor'], $data_teste['funcionario'], $data_teste['data_solicitacao_laudo'],
                $data_teste['tecnico_responsavel'], $data_teste['data_atual_sisbr'],
                $data_teste['usuario_cadastro'], $data_teste['status']
            ]);
            
            echo "<p>✅ Registro de teste inserido com sucesso! ID: " . $pdo_mci->lastInsertId() . "</p>";
            
            // Log da inserção
            $logger->log('Teste de inserção', 'Registro de teste inserido com sucesso');
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Erro na inserção: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Verificar total de registros
    echo "<h3>3. Estatísticas atuais...</h3>";
    $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_registros");
    $stmt->execute();
    $total = $stmt->fetchColumn();
    echo "<p><strong>Total de registros na tabela:</strong> $total</p>";
    
    // Últimas importações
    $stmt = $pdo_mci->prepare("SELECT * FROM cad_importacoes ORDER BY data_importacao DESC LIMIT 3");
    $stmt->execute();
    $importacoes = $stmt->fetchAll();
    
    if (!empty($importacoes)) {
        echo "<h4>Últimas importações:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Arquivo</th><th>Total</th><th>Importados</th><th>Erros</th><th>Status</th><th>Data</th></tr>";
        
        foreach ($importacoes as $imp) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($imp['nome_arquivo']) . "</td>";
            echo "<td>{$imp['total_registros']}</td>";
            echo "<td>{$imp['registros_importados']}</td>";
            echo "<td>{$imp['registros_erro']}</td>";
            echo "<td>{$imp['status']}</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($imp['data_importacao'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro no teste: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>Próximos passos:</h3>";
echo "<ul>";
echo "<li><a href='debug_erros.php'>Ver erros detalhados da última importação</a></li>";
echo "<li><a href='importar.php'>Tentar nova importação</a></li>";
echo "<li><a href='gerenciar.php'>Ver registros existentes</a></li>";
echo "<li><a href='index.php'>Voltar ao início</a></li>";
echo "</ul>";
?>
