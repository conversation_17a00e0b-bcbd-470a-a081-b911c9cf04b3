<?php
require_once '../auth_check.php';

echo "<h2>🔍 Debug - Técnicos Agrícolas</h2>";

// 1. Verificar estrutura da tabela
echo "<h3>1. Estrutura da tabela cad_registros</h3>";
try {
    $stmt = $pdo_mci->prepare("DESCRIBE cad_registros");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th><PERSON><PERSON><PERSON></th></tr>";
    
    $has_tecnico = false;
    foreach ($columns as $col) {
        if ($col['Field'] === 'tecnico_responsavel') {
            $has_tecnico = true;
            echo "<tr style='background-color: #d4edda;'>";
        } else {
            echo "<tr>";
        }
        echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($has_tecnico) {
        echo "<p>✅ Coluna 'tecnico_responsavel' encontrada!</p>";
    } else {
        echo "<p>❌ Coluna 'tecnico_responsavel' NÃO encontrada!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 2. Verificar dados na coluna tecnico_responsavel
echo "<h3>2. Dados na coluna tecnico_responsavel</h3>";
try {
    $stmt = $pdo_mci->prepare("
        SELECT 
            COUNT(*) as total_registros,
            COUNT(CASE WHEN tecnico_responsavel IS NOT NULL AND tecnico_responsavel != '' THEN 1 END) as com_tecnico,
            COUNT(CASE WHEN tecnico_responsavel IS NULL OR tecnico_responsavel = '' THEN 1 END) as sem_tecnico
        FROM cad_registros
    ");
    $stmt->execute();
    $stats = $stmt->fetch();
    
    echo "<p><strong>Total de registros:</strong> " . $stats['total_registros'] . "</p>";
    echo "<p><strong>Com técnico responsável:</strong> " . $stats['com_tecnico'] . "</p>";
    echo "<p><strong>Sem técnico responsável:</strong> " . $stats['sem_tecnico'] . "</p>";
    
    if ($stats['com_tecnico'] > 0) {
        echo "<p>✅ Existem registros com técnico responsável!</p>";
        
        // Mostrar amostra dos técnicos
        $stmt = $pdo_mci->prepare("
            SELECT tecnico_responsavel, COUNT(*) as total
            FROM cad_registros 
            WHERE tecnico_responsavel IS NOT NULL AND tecnico_responsavel != ''
            GROUP BY tecnico_responsavel
            ORDER BY total DESC
            LIMIT 10
        ");
        $stmt->execute();
        $tecnicos = $stmt->fetchAll();
        
        echo "<h4>Técnicos encontrados (top 10):</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Técnico Responsável</th><th>Qtd Registros</th><th>Tipo</th></tr>";
        
        foreach ($tecnicos as $tec) {
            $tipo = is_numeric($tec['tecnico_responsavel']) ? 'ID (numérico)' : 'Nome (texto)';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($tec['tecnico_responsavel']) . "</td>";
            echo "<td>" . $tec['total'] . "</td>";
            echo "<td>" . $tipo . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p>❌ Nenhum registro possui técnico responsável!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 3. Verificar se os técnicos são IDs ou nomes
echo "<h3>3. Análise do tipo de dados</h3>";
try {
    $stmt = $pdo_mci->prepare("
        SELECT tecnico_responsavel
        FROM cad_registros 
        WHERE tecnico_responsavel IS NOT NULL AND tecnico_responsavel != ''
        LIMIT 5
    ");
    $stmt->execute();
    $amostras = $stmt->fetchAll();
    
    if (!empty($amostras)) {
        echo "<p><strong>Amostras de valores:</strong></p>";
        echo "<ul>";
        foreach ($amostras as $amostra) {
            $valor = $amostra['tecnico_responsavel'];
            $tipo = is_numeric($valor) ? 'Numérico (provavelmente ID)' : 'Texto (provavelmente nome)';
            echo "<li>" . htmlspecialchars($valor) . " - <em>$tipo</em></li>";
        }
        echo "</ul>";
        
        // Se for numérico, tentar relacionar com usuários
        $primeiro_valor = $amostras[0]['tecnico_responsavel'];
        if (is_numeric($primeiro_valor)) {
            echo "<h4>Verificando relação com tabela usuarios:</h4>";
            $stmt = $pdo_mci->prepare("
                SELECT 
                    r.tecnico_responsavel,
                    u.nome_completo,
                    u.email,
                    COUNT(r.id) as total_registros
                FROM cad_registros r
                LEFT JOIN sicoob_access_control.usuarios u ON r.tecnico_responsavel = u.id
                WHERE r.tecnico_responsavel IS NOT NULL AND r.tecnico_responsavel != ''
                GROUP BY r.tecnico_responsavel, u.nome_completo, u.email
                ORDER BY total_registros DESC
                LIMIT 10
            ");
            $stmt->execute();
            $relacao = $stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID Técnico</th><th>Nome Completo</th><th>Email</th><th>Registros</th><th>Status</th></tr>";
            
            foreach ($relacao as $rel) {
                $status = !empty($rel['nome_completo']) ? '✅ Encontrado' : '❌ Não encontrado';
                $cor = !empty($rel['nome_completo']) ? 'background-color: #d4edda;' : 'background-color: #f8d7da;';
                
                echo "<tr style='$cor'>";
                echo "<td>" . htmlspecialchars($rel['tecnico_responsavel']) . "</td>";
                echo "<td>" . htmlspecialchars($rel['nome_completo'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($rel['email'] ?? 'N/A') . "</td>";
                echo "<td>" . $rel['total_registros'] . "</td>";
                echo "<td>$status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 4. Testar consulta do dashboard
echo "<h3>4. Teste da consulta do dashboard</h3>";
try {
    $stmt = $pdo_mci->prepare("
        SELECT 
            u.id,
            u.nome_completo as nome,
            u.email,
            COUNT(r.id) as meta_total,
            COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
            ROUND(
                (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
            ) as progresso_total
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
        GROUP BY u.id, u.nome_completo, u.email
        HAVING meta_total > 0
        ORDER BY u.nome_completo
    ");
    $stmt->execute();
    $resultado = $stmt->fetchAll();
    
    echo "<p><strong>Técnicos encontrados pela consulta:</strong> " . count($resultado) . "</p>";
    
    if (count($resultado) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Meta Total</th><th>Atualizados</th><th>Progresso</th></tr>";
        
        foreach ($resultado as $tec) {
            echo "<tr>";
            echo "<td>" . $tec['id'] . "</td>";
            echo "<td>" . htmlspecialchars($tec['nome']) . "</td>";
            echo "<td>" . htmlspecialchars($tec['email']) . "</td>";
            echo "<td>" . $tec['meta_total'] . "</td>";
            echo "<td>" . $tec['atualizados_total'] . "</td>";
            echo "<td>" . $tec['progresso_total'] . "%</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p>✅ Consulta funcionando! <a href='dashboard.php'>Ir para Dashboard</a></p>";
    } else {
        echo "<p>❌ Nenhum técnico encontrado pela consulta do dashboard.</p>";
        echo "<p>💡 Possíveis causas:</p>";
        echo "<ul>";
        echo "<li>Técnicos não estão relacionados corretamente com usuários</li>";
        echo "<li>Usuários não estão ativos</li>";
        echo "<li>Não há registros com status 'Atualizado'</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro na consulta: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='test_connection.php'>🔧 Teste de Conexão</a> | <a href='dashboard.php'>🚀 Dashboard</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
