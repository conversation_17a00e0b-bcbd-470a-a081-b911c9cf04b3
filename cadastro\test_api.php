<?php
/**
 * Teste de conectividade com a API da Intranet
 * Arquivo para testar e debugar a integração com a API
 */

require_once '../auth_check.php';
require_once '../classes/Logger.php';
require_once 'config_api.php';

$logger = new MciLogger();
$intranetAPI = getIntranetAPI($logger);

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API Intranet - MCI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        
        body { background-color: #f8f9fa; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .test-result { margin: 1rem 0; padding: 1rem; border-radius: 8px; }
        .test-success { background-color: #d1edff; border-left: 4px solid #0d6efd; }
        .test-error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .test-warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .user-card { margin: 0.5rem 0; padding: 1rem; background: white; border-radius: 8px; border: 1px solid #dee2e6; }
        .user-avatar { width: 50px; height: 50px; border-radius: 50%; margin-right: 1rem; object-fit: cover; }
        .user-avatar.placeholder { background: var(--sicoob-turquesa); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-flask"></i> Teste de Conectividade - API Intranet
                        </h5>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        echo "<h6><i class='fas fa-info-circle'></i> Informações de Configuração</h6>";
                        echo "<div class='test-result test-success'>";
                        echo "<strong>URL da API:</strong> " . API_INTRANET_URL . "<br>";
                        echo "<strong>Usuário API:</strong> " . API_INTRANET_USER . "<br>";
                        echo "<strong>Token:</strong> " . substr(API_INTRANET_TOKEN, 0, 10) . "... (oculto por segurança)<br>";
                        echo "<strong>Timeout:</strong> " . API_INTRANET_TIMEOUT . " segundos<br>";
                        echo "<strong>URL Base Fotos:</strong> " . API_INTRANET_FOTO_URL;
                        echo "</div>";
                        
                        echo "<h6><i class='fas fa-network-wired'></i> Teste de Conectividade</h6>";
                        
                        $start_time = microtime(true);
                        
                        try {
                            echo "<div class='test-result test-warning'>";
                            echo "<i class='fas fa-spinner fa-spin'></i> Testando conectividade com a API...";
                            echo "</div>";
                            
                            $usuarios = $intranetAPI->buscarUsuarios();
                            $end_time = microtime(true);
                            $execution_time = round(($end_time - $start_time) * 1000, 2);
                            
                            if (empty($usuarios)) {
                                echo "<div class='test-result test-error'>";
                                echo "<i class='fas fa-exclamation-triangle'></i> <strong>Erro:</strong> Nenhum usuário retornado pela API";
                                echo "</div>";
                            } else {
                                echo "<div class='test-result test-success'>";
                                echo "<i class='fas fa-check-circle'></i> <strong>Sucesso!</strong> API respondeu em {$execution_time}ms<br>";
                                echo "<strong>Total de usuários ativos:</strong> " . count($usuarios);
                                echo "</div>";
                                
                                // Estatísticas dos usuários
                                $usuarios_com_foto = 0;
                                $usuarios_com_email = 0;
                                $setores = [];
                                
                                foreach ($usuarios as $usuario) {
                                    if (!empty($usuario['foto_url'])) $usuarios_com_foto++;
                                    if (!empty($usuario['email'])) $usuarios_com_email++;
                                    if (!empty($usuario['setor_nome'])) {
                                        $setores[$usuario['setor_nome']] = ($setores[$usuario['setor_nome']] ?? 0) + 1;
                                    }
                                }
                                
                                echo "<h6><i class='fas fa-chart-bar'></i> Estatísticas dos Usuários</h6>";
                                echo "<div class='test-result test-success'>";
                                echo "<strong>Usuários com foto:</strong> {$usuarios_com_foto} (" . round(($usuarios_com_foto / count($usuarios)) * 100, 1) . "%)<br>";
                                echo "<strong>Usuários com email:</strong> {$usuarios_com_email} (" . round(($usuarios_com_email / count($usuarios)) * 100, 1) . "%)<br>";
                                echo "<strong>Setores únicos:</strong> " . count($setores);
                                echo "</div>";
                                
                                // Teste específico dos funcionários do MCI
                                echo "<h6><i class='fas fa-users'></i> Teste dos Funcionários MCI</h6>";
                                
                                $funcionarios_mci = [17, 18, 19, 20, 21, 22];
                                $funcionarios_encontrados = 0;
                                
                                foreach ($funcionarios_mci as $funcionario_id) {
                                    $query = "SELECT u.id, u.nome_completo, u.email FROM sicoob_access_control.usuarios u WHERE u.id = ? AND u.ativo = TRUE";
                                    $stmt = $pdo_mci->prepare($query);
                                    $stmt->execute([$funcionario_id]);
                                    $funcionario = $stmt->fetch();
                                    
                                    if ($funcionario) {
                                        $funcionarios_encontrados++;
                                        $usuario_intranet = null;
                                        
                                        if (!empty($funcionario['email'])) {
                                            $usuario_intranet = $intranetAPI->buscarUsuarioPorEmail($funcionario['email'], $usuarios);
                                        }
                                        
                                        echo "<div class='user-card d-flex align-items-center'>";
                                        
                                        if ($usuario_intranet && !empty($usuario_intranet['foto_url'])) {
                                            echo "<img src='{$usuario_intranet['foto_url']}' alt='Foto' class='user-avatar'>";
                                        } else {
                                            echo "<div class='user-avatar placeholder'>" . strtoupper(substr($funcionario['nome_completo'], 0, 1)) . "</div>";
                                        }
                                        
                                        echo "<div class='flex-grow-1'>";
                                        echo "<strong>{$funcionario['nome_completo']}</strong> (ID: {$funcionario_id})<br>";
                                        echo "<small class='text-muted'>Email: " . ($funcionario['email'] ?: 'Não informado') . "</small><br>";
                                        
                                        if ($usuario_intranet) {
                                            echo "<small class='text-success'><i class='fas fa-check'></i> Encontrado na Intranet</small><br>";
                                            echo "<small>Setor: " . ($usuario_intranet['setor_nome'] ?: 'Não informado') . "</small><br>";
                                            echo "<small>Função: " . ($usuario_intranet['funcao_nome'] ?: 'Não informado') . "</small>";
                                        } else {
                                            echo "<small class='text-warning'><i class='fas fa-exclamation-triangle'></i> Não encontrado na Intranet</small>";
                                        }
                                        
                                        echo "</div></div>";
                                    } else {
                                        echo "<div class='user-card'>";
                                        echo "<span class='text-danger'><i class='fas fa-times'></i> Funcionário ID {$funcionario_id} não encontrado no banco local</span>";
                                        echo "</div>";
                                    }
                                }
                                
                                echo "<div class='test-result test-success'>";
                                echo "<strong>Funcionários MCI encontrados:</strong> {$funcionarios_encontrados}/" . count($funcionarios_mci);
                                echo "</div>";
                                
                                // Teste de performance
                                echo "<h6><i class='fas fa-tachometer-alt'></i> Teste de Performance</h6>";
                                
                                $performance_tests = [];
                                for ($i = 0; $i < 3; $i++) {
                                    $start = microtime(true);
                                    $test_usuarios = $intranetAPI->buscarUsuarios();
                                    $end = microtime(true);
                                    $performance_tests[] = round(($end - $start) * 1000, 2);
                                }
                                
                                $avg_time = round(array_sum($performance_tests) / count($performance_tests), 2);
                                $min_time = min($performance_tests);
                                $max_time = max($performance_tests);
                                
                                echo "<div class='test-result test-success'>";
                                echo "<strong>Tempo médio de resposta:</strong> {$avg_time}ms<br>";
                                echo "<strong>Tempo mínimo:</strong> {$min_time}ms<br>";
                                echo "<strong>Tempo máximo:</strong> {$max_time}ms<br>";
                                echo "<strong>Testes realizados:</strong> " . count($performance_tests);
                                echo "</div>";
                            }
                            
                        } catch (Exception $e) {
                            $end_time = microtime(true);
                            $execution_time = round(($end_time - $start_time) * 1000, 2);
                            
                            echo "<div class='test-result test-error'>";
                            echo "<i class='fas fa-times-circle'></i> <strong>Erro na API:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
                            echo "<strong>Tempo até erro:</strong> {$execution_time}ms";
                            echo "</div>";
                        }
                        ?>
                        
                        <div class="mt-4">
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="fas fa-chart-line"></i> Ir para Dashboard
                            </a>
                            <a href="../index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Voltar ao MCI
                            </a>
                            <button onclick="location.reload()" class="btn btn-outline-primary">
                                <i class="fas fa-sync-alt"></i> Executar Teste Novamente
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
