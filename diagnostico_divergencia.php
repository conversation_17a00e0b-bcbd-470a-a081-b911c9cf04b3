<?php
/**
 * Script de Diagnóstico - Divergência entre Cadastro e Técnicos Agrícolas
 * Identifica por que há diferença entre 1.108 (cadastro) e 1.206 (técnicos)
 */

require_once 'config/config.php';
require_once 'classes/Logger.php';

$logger = new MciLogger();
$logger->log('Diagnóstico de divergência', 'Análise da diferença entre métricas de cadastro e técnicos');

echo "<h1>🔍 Diagnóstico de Divergência - Cadastro vs Técnicos Agrícolas</h1>";
echo "<hr>";

try {
    // 1. ANÁLISE DA EQUIPE DE CADASTRO
    echo "<h2>📊 1. Análise da Equipe de Cadastro</h2>";
    
    $funcionarios_ids = [17, 18, 19, 20, 21, 22];
    $total_cadastro = 0;
    $total_atualizados_cadastro = 0;
    
    foreach ($funcionarios_ids as $funcionario_id) {
        $query_cadastro = "
            SELECT
                COUNT(r.id) as total_registros,
                SUM(CASE
                    WHEN r.data_solicitacao_laudo IS NOT NULL
                    AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                    THEN 1 ELSE 0
                END) as atualizados_ano
            FROM cad_registros r
            WHERE r.funcionario = ?
            AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        ";
        
        $stmt = $pdo_mci->prepare($query_cadastro);
        $stmt->execute([$funcionario_id]);
        $dados = $stmt->fetch();
        
        if ($dados['total_registros'] > 0) {
            echo "<p><strong>Funcionário ID $funcionario_id:</strong> ";
            echo "Total: " . number_format($dados['total_registros']) . " | ";
            echo "Atualizados no ano: " . number_format($dados['atualizados_ano']) . "</p>";
            
            $total_cadastro += $dados['total_registros'];
            $total_atualizados_cadastro += $dados['atualizados_ano'];
        }
    }
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📈 Resumo Cadastro:</h3>";
    echo "<p><strong>Total de Registros:</strong> " . number_format($total_cadastro) . "</p>";
    echo "<p><strong>Total Atualizados no Ano:</strong> " . number_format($total_atualizados_cadastro) . "</p>";
    echo "</div>";
    
    // 2. ANÁLISE DOS TÉCNICOS AGRÍCOLAS
    echo "<h2>🌱 2. Análise dos Técnicos Agrícolas</h2>";
    
    $query_tecnicos = "
        SELECT
            u.id,
            u.nome_completo,
            COUNT(r.id) as total_registros,
            COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
        GROUP BY u.id, u.nome_completo
        HAVING total_registros > 0
        ORDER BY total_registros DESC
    ";
    
    $stmt = $pdo_mci->prepare($query_tecnicos);
    $stmt->execute();
    $tecnicos = $stmt->fetchAll();
    
    $total_tecnicos = 0;
    $total_atualizados_tecnicos = 0;
    
    foreach ($tecnicos as $tecnico) {
        echo "<p><strong>{$tecnico['nome_completo']} (ID: {$tecnico['id']}):</strong> ";
        echo "Total: " . number_format($tecnico['total_registros']) . " | ";
        echo "Atualizados: " . number_format($tecnico['atualizados_total']) . "</p>";
        
        $total_tecnicos += $tecnico['total_registros'];
        $total_atualizados_tecnicos += $tecnico['atualizados_total'];
    }
    
    echo "<div style='background: #f3e5f5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📈 Resumo Técnicos:</h3>";
    echo "<p><strong>Total de Registros Atribuídos:</strong> " . number_format($total_tecnicos) . "</p>";
    echo "<p><strong>Total Atualizados:</strong> " . number_format($total_atualizados_tecnicos) . "</p>";
    echo "</div>";
    
    // 3. ANÁLISE DA DIVERGÊNCIA
    echo "<h2>⚠️ 3. Análise da Divergência</h2>";
    
    $diferenca_total = $total_tecnicos - $total_cadastro;
    $diferenca_atualizados = $total_atualizados_tecnicos - $total_atualizados_cadastro;
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🔍 Identificação do Problema:</h3>";
    echo "<p><strong>Diferença Total de Registros:</strong> " . number_format($diferenca_total) . "</p>";
    echo "<p><strong>Diferença de Atualizados:</strong> " . number_format($diferenca_atualizados) . "</p>";
    echo "</div>";
    
    // 4. INVESTIGAÇÃO DETALHADA
    echo "<h2>🔬 4. Investigação Detalhada</h2>";
    
    // 4.1 Verificar registros sem funcionário mas com técnico
    $query_sem_funcionario = "
        SELECT COUNT(*) as total
        FROM cad_registros r
        WHERE r.funcionario IS NULL 
        AND r.tecnico_responsavel IS NOT NULL
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_sem_funcionario);
    $stmt->execute();
    $sem_funcionario = $stmt->fetch();
    
    echo "<p><strong>Registros SEM funcionário mas COM técnico:</strong> " . number_format($sem_funcionario['total']) . "</p>";
    
    // 4.2 Verificar registros com funcionário mas sem técnico
    $query_sem_tecnico = "
        SELECT COUNT(*) as total
        FROM cad_registros r
        WHERE r.funcionario IS NOT NULL 
        AND r.tecnico_responsavel IS NULL
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_sem_tecnico);
    $stmt->execute();
    $sem_tecnico = $stmt->fetch();
    
    echo "<p><strong>Registros COM funcionário mas SEM técnico:</strong> " . number_format($sem_tecnico['total']) . "</p>";
    
    // 4.3 Verificar registros com ambos preenchidos
    $query_ambos = "
        SELECT COUNT(*) as total
        FROM cad_registros r
        WHERE r.funcionario IS NOT NULL 
        AND r.tecnico_responsavel IS NOT NULL
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_ambos);
    $stmt->execute();
    $ambos = $stmt->fetch();
    
    echo "<p><strong>Registros COM funcionário E técnico:</strong> " . number_format($ambos['total']) . "</p>";
    
    // 4.4 Verificar registros sem nenhum dos dois
    $query_nenhum = "
        SELECT COUNT(*) as total
        FROM cad_registros r
        WHERE r.funcionario IS NULL 
        AND r.tecnico_responsavel IS NULL
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
    ";
    
    $stmt = $pdo_mci->prepare($query_nenhum);
    $stmt->execute();
    $nenhum = $stmt->fetch();
    
    echo "<p><strong>Registros SEM funcionário E SEM técnico:</strong> " . number_format($nenhum['total']) . "</p>";
    
    // 5. CONCLUSÃO
    echo "<h2>💡 5. Conclusão e Solução</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🎯 Causa da Divergência:</h3>";
    
    if ($sem_funcionario['total'] > 0) {
        echo "<p><strong>✅ PRINCIPAL CAUSA IDENTIFICADA:</strong></p>";
        echo "<p>Existem <strong>" . number_format($sem_funcionario['total']) . " registros</strong> que foram atribuídos a técnicos agrícolas ";
        echo "mas <strong>NÃO foram atribuídos a funcionários de cadastro</strong>.</p>";
        echo "<p>Estes registros aparecem na contagem dos técnicos (1.206) mas não na contagem do cadastro (1.108).</p>";
    }
    
    if ($sem_tecnico['total'] > 0) {
        echo "<p><strong>⚠️ CAUSA SECUNDÁRIA:</strong></p>";
        echo "<p>Existem <strong>" . number_format($sem_tecnico['total']) . " registros</strong> que foram atribuídos a funcionários de cadastro ";
        echo "mas <strong>NÃO foram atribuídos a técnicos agrícolas</strong>.</p>";
    }
    
    echo "<p><strong>📊 Resumo:</strong></p>";
    echo "<ul>";
    echo "<li>Cadastro conta apenas registros com <code>funcionario</code> preenchido</li>";
    echo "<li>Técnicos contam todos os registros com <code>tecnico_responsavel</code> preenchido</li>";
    echo "<li>A diferença são registros que têm técnico mas não têm funcionário</li>";
    echo "</ul>";
    echo "</div>";
    
    // 6. RECOMENDAÇÕES
    echo "<h2>🚀 6. Recomendações</h2>";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🔧 Ações Recomendadas:</h3>";
    echo "<ol>";
    echo "<li><strong>Auditoria de Dados:</strong> Verificar por que alguns registros têm técnico mas não funcionário</li>";
    echo "<li><strong>Padronização:</strong> Garantir que todos os registros tenham tanto funcionário quanto técnico</li>";
    echo "<li><strong>Validação:</strong> Implementar regras de negócio para evitar essa inconsistência</li>";
    echo "<li><strong>Relatório:</strong> Criar relatório mensal para monitorar essas divergências</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ Erro durante o diagnóstico:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    $logger->logFile("Erro no diagnóstico de divergência: " . $e->getMessage(), 'ERROR');
}

echo "<hr>";
echo "<p><strong>Data do diagnóstico:</strong> " . date('d/m/Y H:i:s') . "</p>";
echo "<p><a href='metas.php'>← Voltar para Metas</a></p>";
?>
