<?php
require_once '../auth_check.php';

header('Content-Type: application/json');

try {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        throw new Exception('ID não fornecido');
    }
    
    $stmt = $pdo_mci->prepare("
        SELECT r.*, s.nome as status_nome
        FROM cad_registros r
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE r.id = ?
    ");
    $stmt->execute([$id]);
    $registro = $stmt->fetch();

    if (!$registro) {
        throw new Exception('Registro não encontrado');
    }

    echo json_encode([
        'success' => true,
        'registro' => $registro
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
