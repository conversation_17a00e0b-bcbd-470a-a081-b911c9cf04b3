<?php
require_once 'auth_check.php';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajuda - <?php echo MCI_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        body { background-color: #f8f9fa; }
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); margin-bottom: 20px; }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        .faq-item { margin-bottom: 15px; }
        .faq-question { font-weight: bold; color: var(--sicoob-verde-escuro); cursor: pointer; }
        .faq-answer { margin-top: 10px; padding-left: 20px; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="../assets/images/logo1.png" alt="Sicoob" height="40">
                <span class="ms-2">MCI - Ajuda</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-primary">
                    <i class="fas fa-question-circle"></i> 
                    Central de Ajuda - Sistema MCI
                </h1>
                <p class="text-muted">Encontre respostas para as principais dúvidas sobre o sistema</p>
            </div>
        </div>

        <!-- Menu Rápido -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-excel fa-2x text-primary mb-2"></i>
                        <h6><a href="#importacao" class="text-decoration-none">Importação</a></h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-edit fa-2x text-primary mb-2"></i>
                        <h6><a href="#gerenciamento" class="text-decoration-none">Gerenciamento</a></h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-bar fa-2x text-primary mb-2"></i>
                        <h6><a href="#dashboard" class="text-decoration-none">Dashboard</a></h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-bug fa-2x text-primary mb-2"></i>
                        <h6><a href="#problemas" class="text-decoration-none">Problemas</a></h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Importação -->
        <div class="card" id="importacao">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-excel"></i> Importação de Planilhas
                </h5>
            </div>
            <div class="card-body">
                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq1">
                        <i class="fas fa-chevron-right"></i> Qual formato de planilha é aceito?
                    </div>
                    <div class="collapse faq-answer" id="faq1">
                        O sistema aceita arquivos nos formatos .xlsx e .xls (Excel). O tamanho máximo é de 10MB.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq2">
                        <i class="fas fa-chevron-right"></i> Como deve estar organizada a planilha?
                    </div>
                    <div class="collapse faq-answer" id="faq2">
                        A planilha deve ter exatamente 13 colunas na seguinte ordem:
                        <ol>
                            <li>PA (obrigatório)</li>
                            <li>Nome Cliente (obrigatório)</li>
                            <li>Número CPF/CNPJ (obrigatório)</li>
                            <li>CNAE</li>
                            <li>Data Última Atualização Renda</li>
                            <li>Sigla Tipo Pessoa</li>
                            <li>Profissão</li>
                            <li>Depósito Total</li>
                            <li><strong>Saldo Devedor</strong> (nova coluna)</li>
                            <li>FUNCIONÁRIO</li>
                            <li>DATA DA SOLICITAÇÃO DO LAUDO</li>
                            <li>TÉCNICO RESPONSÁVEL</li>
                            <li>DATA DA ATUAL. SISBR</li>
                        </ol>
                        A primeira linha deve conter os cabeçalhos.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq3">
                        <i class="fas fa-chevron-right"></i> Por que minha importação falhou?
                    </div>
                    <div class="collapse faq-answer" id="faq3">
                        As principais causas de falha são:
                        <ul>
                            <li>Campos obrigatórios vazios (PA, Nome Cliente, CPF/CNPJ)</li>
                            <li>Formato de data inválido</li>
                            <li>Valores monetários com formato incorreto</li>
                            <li>Campos muito longos</li>
                        </ul>
                        <strong>Nota sobre PA 0:</strong> O sistema aceita PA com valor "0" como válido. Se você teve erros com PA 0,
                        use a ferramenta <a href="reprocessar_pa_zero.php">Reprocessar PA Zero</a> para corrigir automaticamente.
                        <br><br>
                        <strong>Formatação automática de CPF/CNPJ:</strong> O sistema automaticamente:
                        <ul>
                            <li>Remove formatação (pontos, hífen, barra)</li>
                            <li>Adiciona zeros à esquerda quando necessário</li>
                            <li>CPF: completa até 11 dígitos (ex: 123456789 → 00123456789)</li>
                            <li>CNPJ: completa até 14 dígitos (ex: 123456780001 → 00123456780001)</li>
                        </ul>
                        Use a página <a href="debug_erros.php">Debug de Erros</a> para ver detalhes específicos.
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Gerenciamento -->
        <div class="card" id="gerenciamento">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit"></i> Gerenciamento de Registros
                </h5>
            </div>
            <div class="card-body">
                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq4">
                        <i class="fas fa-chevron-right"></i> Como alterar o status de um registro?
                    </div>
                    <div class="collapse faq-answer" id="faq4">
                        Na página de gerenciamento, clique no botão azul (ícone de edição) ao lado do registro. 
                        Selecione o novo status e adicione observações se necessário.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq5">
                        <i class="fas fa-chevron-right"></i> Quais são os status disponíveis?
                    </div>
                    <div class="collapse faq-answer" id="faq5">
                        <ul>
                            <li><strong>Pendente:</strong> Registro aguardando processamento</li>
                            <li><strong>Em Andamento:</strong> Atualização cadastral em processo</li>
                            <li><strong>Concluído:</strong> Atualização finalizada com sucesso</li>
                            <li><strong>Cancelado:</strong> Processo cancelado</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq6">
                        <i class="fas fa-chevron-right"></i> Como usar os filtros?
                    </div>
                    <div class="collapse faq-answer" id="faq6">
                        Use os filtros na parte superior da página para encontrar registros específicos:
                        <ul>
                            <li>Filtre por status para ver apenas registros em determinada situação</li>
                            <li>Selecione um PA específico</li>
                            <li>Digite parte do nome do cliente</li>
                            <li>Digite parte do CPF/CNPJ</li>
                        </ul>
                        Clique em "Filtrar" para aplicar ou "Limpar" para remover os filtros.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq6b">
                        <i class="fas fa-chevron-right"></i> Como funciona o sistema de status?
                    </div>
                    <div class="collapse faq-answer" id="faq6b">
                        O sistema possui dois status principais:
                        <ul>
                            <li><span class="badge" style="background-color: #ffc107; color: black;">Pendente</span> - Registros recém-importados, aguardando processamento</li>
                            <li><span class="badge" style="background-color: #28a745; color: white;">Atualizado</span> - Registros já processados e atualizados</li>
                        </ul>
                        <strong>Como usar:</strong>
                        <ul>
                            <li>Novos registros importados recebem automaticamente status "Pendente"</li>
                            <li>Use a página de gerenciamento para alterar o status</li>
                            <li>Filtre registros por status para organizar o trabalho</li>
                            <li>Use <a href="validar_status_sistema.php">Validar Sistema de Status</a> para verificar integridade</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Dashboard -->
        <div class="card" id="dashboard">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Dashboard e Relatórios
                </h5>
            </div>
            <div class="card-body">
                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq7">
                        <i class="fas fa-chevron-right"></i> Como interpretar os gráficos?
                    </div>
                    <div class="collapse faq-answer" id="faq7">
                        <ul>
                            <li><strong>Gráfico de Pizza:</strong> Mostra a distribuição percentual dos registros por status</li>
                            <li><strong>Gráfico de Linha:</strong> Mostra a evolução dos cadastros ao longo dos últimos 6 meses</li>
                            <li><strong>Rankings:</strong> Listam os top 10 PAs, funcionários e técnicos com mais registros</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq8">
                        <i class="fas fa-chevron-right"></i> Com que frequência os dados são atualizados?
                    </div>
                    <div class="collapse faq-answer" id="faq8">
                        Os dados do dashboard são atualizados automaticamente a cada 5 minutos. 
                        Você também pode atualizar manualmente recarregando a página.
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Problemas -->
        <div class="card" id="problemas">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bug"></i> Solução de Problemas
                </h5>
            </div>
            <div class="card-body">
                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq9">
                        <i class="fas fa-chevron-right"></i> O que fazer se o upload falhar?
                    </div>
                    <div class="collapse faq-answer" id="faq9">
                        <ul>
                            <li>Verifique se o arquivo não excede 10MB</li>
                            <li>Certifique-se de que é um arquivo .xlsx ou .xls</li>
                            <li>Tente fechar e reabrir o arquivo no Excel antes de fazer upload</li>
                            <li>Verifique sua conexão com a internet</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq10">
                        <i class="fas fa-chevron-right"></i> Como reportar um problema?
                    </div>
                    <div class="collapse faq-answer" id="faq10">
                        Para reportar problemas:
                        <ol>
                            <li>Anote a data e hora do problema</li>
                            <li>Descreva o que estava fazendo quando o erro ocorreu</li>
                            <li>Faça uma captura de tela se possível</li>
                            <li>Entre em contato com a equipe de TI</li>
                        </ol>
                        Os logs do sistema ajudam na investigação de problemas.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-bs-toggle="collapse" data-bs-target="#faq11">
                        <i class="fas fa-chevron-right"></i> Páginas úteis para diagnóstico
                    </div>
                    <div class="collapse faq-answer" id="faq11">
                        <ul>
                            <li><a href="test_connection.php">Teste de Conexão</a> - Verifica se o sistema está funcionando</li>
                            <li><a href="debug_erros.php">Debug de Erros</a> - Mostra erros detalhados de importação</li>
                            <li><a href="teste_importacao.php">Teste de Importação</a> - Testa o processamento de dados</li>
                            <li><a href="monitor.php">Monitor</a> - API de monitoramento em tempo real</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Links Úteis -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-link"></i> Links Úteis
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Ferramentas do Sistema</h6>
                        <ul class="list-unstyled">
                            <li><a href="criar_planilha_exemplo.php"><i class="fas fa-download"></i> Baixar Planilha de Exemplo</a></li>
                            <li><a href="test_connection.php"><i class="fas fa-plug"></i> Teste de Conexão</a></li>
                            <li><a href="debug_erros.php"><i class="fas fa-bug"></i> Debug de Erros</a></li>
                            <li><a href="teste_importacao.php"><i class="fas fa-vial"></i> Teste de Importação</a></li>
                            <li><a href="teste_conversoes.php"><i class="fas fa-calendar-alt"></i> Teste de Conversões</a></li>
                            <li><a href="teste_cpf_cnpj.php"><i class="fas fa-id-card"></i> Teste CPF/CNPJ</a></li>
                            <li><a href="validar_status_sistema.php"><i class="fas fa-check-circle"></i> Validar Sistema de Status</a></li>
                            <li><a href="corrigir_estrutura_status.php"><i class="fas fa-database"></i> Corrigir Estrutura Status</a></li>
                            <li><a href="corrigir_status_registros.php"><i class="fas fa-tools"></i> Corrigir Status</a></li>
                            <li><a href="restaurar_dados_usuarios.php"><i class="fas fa-undo"></i> Restaurar Dados Usuários</a></li>
                            <li><a href="mapear_usuarios_gradual.php"><i class="fas fa-user-cog"></i> Mapear Usuários Gradual</a></li>
                            <li><a href="gerenciar_chaves_estrangeiras.php"><i class="fas fa-key"></i> Gerenciar Chaves Estrangeiras</a></li>
                            <li><a href="relacionar_pontos_atendimento.php"><i class="fas fa-map-marker-alt"></i> Relacionar Pontos de Atendimento</a></li>
                            <li><a href="reprocessar_pa_zero.php"><i class="fas fa-redo"></i> Reprocessar PA Zero</a></li>
                            <li><a href="reset_sistema.php"><i class="fas fa-trash-alt"></i> Reset do Sistema</a></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Documentação</h6>
                        <ul class="list-unstyled">
                            <li><a href="README.md" target="_blank"><i class="fas fa-book"></i> Manual do Sistema</a></li>
                            <li><a href="INSTALL.md" target="_blank"><i class="fas fa-cog"></i> Guia de Instalação</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Animar ícones das perguntas
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', function() {
                const icon = this.querySelector('i');
                const target = this.getAttribute('data-bs-target');
                const collapse = document.querySelector(target);
                
                collapse.addEventListener('shown.bs.collapse', () => {
                    icon.classList.remove('fa-chevron-right');
                    icon.classList.add('fa-chevron-down');
                });
                
                collapse.addEventListener('hidden.bs.collapse', () => {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-right');
                });
            });
        });
    </script>
</body>
</html>
