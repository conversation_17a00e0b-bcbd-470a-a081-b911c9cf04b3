<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

echo "<h1>Teste Específico - PA Zero</h1>";
echo "<hr>";

// Incluir as funções de importação
require_once '../vendor/autoload.php';
use PhpOffice\PhpSpreadsheet\IOFactory;

// Função para converter datas do Excel
function convertExcelDate($value) {
    if (empty($value)) {
        return null;
    }
    
    // Se for um número (data do Excel)
    if (is_numeric($value)) {
        try {
            $date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($value);
            return $date->format('Y-m-d');
        } catch (Exception $e) {
            return null;
        }
    }
    
    // Se for uma string, tentar converter
    if (is_string($value)) {
        $value = trim($value);
        if (empty($value)) {
            return null;
        }
        
        // Tentar diferentes formatos de data
        $formats = ['Y-m-d', 'd/m/Y', 'd-m-Y', 'm/d/Y', 'Y/m/d'];
        foreach ($formats as $format) {
            $date = DateTime::createFromFormat($format, $value);
            if ($date !== false) {
                return $date->format('Y-m-d');
            }
        }
        
        // Tentar strtotime como último recurso
        $timestamp = strtotime($value);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }
    }
    
    return null;
}

// Função para limpar valores monetários
function cleanMoneyValue($value) {
    if (empty($value)) {
        return null;
    }
    
    // Se for numérico, retornar como float
    if (is_numeric($value)) {
        return floatval($value);
    }
    
    // Limpar caracteres não numéricos exceto vírgula e ponto
    $cleaned = preg_replace('/[^\d,.-]/', '', $value);
    
    // Converter vírgula para ponto (formato brasileiro)
    $cleaned = str_replace(',', '.', $cleaned);
    
    return is_numeric($cleaned) ? floatval($cleaned) : null;
}

echo "<h3>Testando diferentes valores de PA:</h3>";

$test_cases = [
    ['valor' => '0', 'descricao' => 'String "0"'],
    ['valor' => 0, 'descricao' => 'Número 0'],
    ['valor' => '001', 'descricao' => 'String "001"'],
    ['valor' => 1, 'descricao' => 'Número 1'],
    ['valor' => '', 'descricao' => 'String vazia'],
    ['valor' => null, 'descricao' => 'NULL'],
    ['valor' => '   ', 'descricao' => 'String com espaços'],
    ['valor' => '000', 'descricao' => 'String "000"'],
];

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Valor Original</th><th>Tipo</th><th>Descrição</th><th>Processado</th><th>Validação</th></tr>";

foreach ($test_cases as $test) {
    $valor_original = $test['valor'];
    $tipo = gettype($valor_original);
    $descricao = $test['descricao'];
    
    // Simular o processamento do PA
    $pa_processado = isset($valor_original) ? trim((string)$valor_original) : '';
    
    // Simular a validação
    $validacao_resultado = '';
    if ($pa_processado === '' || $pa_processado === null || trim($pa_processado) === '') {
        $validacao_resultado = '❌ PA é obrigatório';
    } else {
        $validacao_resultado = '✅ PA válido';
    }
    
    echo "<tr>";
    echo "<td>" . var_export($valor_original, true) . "</td>";
    echo "<td>$tipo</td>";
    echo "<td>$descricao</td>";
    echo "<td>'" . htmlspecialchars($pa_processado) . "'</td>";
    echo "<td>$validacao_resultado</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Teste com dados reais da planilha:</h3>";

// Simular uma linha da planilha com PA 0
$linha_teste = [0, 'WELLITON LAZARO DIAS FERREIRA', '11817922661', 'CULTIVO DE CAFÉ', '1/15/2024', 'PF', 'Agricultor', '5000.00', 'João Silva', '1/10/2024', 'Maria Santos', '1/20/2024'];

echo "<p><strong>Linha de teste:</strong> " . implode(' | ', $linha_teste) . "</p>";

try {
    // Processar como no código de importação
    $data = [
        'pa' => isset($linha_teste[0]) ? trim((string)$linha_teste[0]) : '',
        'nome_cliente' => trim($linha_teste[1] ?? ''),
        'numero_cpf_cnpj' => preg_replace('/[^0-9]/', '', $linha_teste[2] ?? ''),
        'cnae' => trim($linha_teste[3] ?? ''),
        'data_ultima_atualizacao_renda' => convertExcelDate($linha_teste[4] ?? ''),
        'sigla_tipo_pessoa' => trim($linha_teste[5] ?? ''),
        'profissao' => trim($linha_teste[6] ?? ''),
        'deposito_total' => cleanMoneyValue($linha_teste[7] ?? ''),
        'funcionario' => trim($linha_teste[8] ?? ''),
        'data_solicitacao_laudo' => convertExcelDate($linha_teste[9] ?? ''),
        'tecnico_responsavel' => trim($linha_teste[10] ?? ''),
        'data_atual_sisbr' => convertExcelDate($linha_teste[11] ?? ''),
        'usuario_cadastro' => $_SESSION['user_id']
    ];
    
    echo "<h4>Dados processados:</h4>";
    echo "<table border='1' cellpadding='5'>";
    foreach ($data as $campo => $valor) {
        echo "<tr><td><strong>$campo</strong></td><td>" . var_export($valor, true) . "</td></tr>";
    }
    echo "</table>";
    
    // Validações
    echo "<h4>Validações:</h4>";
    $erros = [];
    
    // Validação do PA
    if ($data['pa'] === '' || $data['pa'] === null || trim($data['pa']) === '') {
        $erros[] = 'PA é obrigatório';
    }
    if (empty($data['nome_cliente'])) {
        $erros[] = 'Nome Cliente é obrigatório';
    }
    if (empty($data['numero_cpf_cnpj'])) {
        $erros[] = 'CPF/CNPJ é obrigatório';
    }
    
    if (empty($erros)) {
        echo "<p style='color: green;'>✅ Todos os campos obrigatórios estão preenchidos!</p>";
        
        // Testar inserção no banco
        echo "<h4>Teste de inserção no banco:</h4>";
        try {
            $stmt = $pdo_mci->prepare("
                INSERT INTO cad_registros (
                    pa, nome_cliente, numero_cpf_cnpj, cnae, data_ultima_atualizacao_renda,
                    sigla_tipo_pessoa, profissao, deposito_total, funcionario,
                    data_solicitacao_laudo, tecnico_responsavel, data_atual_sisbr, usuario_cadastro
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['pa'], $data['nome_cliente'], $data['numero_cpf_cnpj'], $data['cnae'],
                $data['data_ultima_atualizacao_renda'], $data['sigla_tipo_pessoa'], $data['profissao'],
                $data['deposito_total'], $data['funcionario'], $data['data_solicitacao_laudo'],
                $data['tecnico_responsavel'], $data['data_atual_sisbr'], $data['usuario_cadastro']
            ]);
            
            $id_inserido = $pdo_mci->lastInsertId();
            echo "<p style='color: green;'>✅ Registro inserido com sucesso! ID: $id_inserido</p>";
            
            // Verificar se foi inserido corretamente
            $stmt = $pdo_mci->prepare("SELECT * FROM cad_registros WHERE id = ?");
            $stmt->execute([$id_inserido]);
            $registro_inserido = $stmt->fetch();
            
            echo "<h5>Registro inserido no banco:</h5>";
            echo "<table border='1' cellpadding='5'>";
            foreach ($registro_inserido as $campo => $valor) {
                echo "<tr><td><strong>$campo</strong></td><td>" . htmlspecialchars($valor ?? 'NULL') . "</td></tr>";
            }
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro na inserção: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Erros encontrados:</p>";
        echo "<ul>";
        foreach ($erros as $erro) {
            echo "<li>$erro</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro no processamento: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>Conclusão:</h3>";
echo "<p>Com as correções implementadas, o PA '0' agora deve ser aceito como um valor válido.</p>";
echo "<p><a href='importar.php'>Testar nova importação</a> | <a href='index.php'>Voltar ao sistema</a></p>";
?>
