<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

// Verificar se é admin
checkMciAccess('admin');

$logger = new MciLogger();

echo "<h1>Reprocessar Registros com PA 0</h1>";
echo "<hr>";
echo "<div style='color: orange; font-weight: bold;'>⚠️ Esta ferramenta irá tentar reprocessar registros que falharam devido ao PA 0</div>";
echo "<hr>";

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirmar_reprocessamento'])) {
    try {
        echo "<h3>Executando reprocessamento...</h3>";
        
        // Log da ação
        $logger->log('Reprocessamento PA 0', 'Usuário iniciou reprocessamento de registros com PA 0');
        
        // Buscar erros relacionados ao PA obrigatório
        $stmt = $pdo_mci->prepare("
            SELECT DISTINCT importacao_id, linha, valor 
            FROM cad_erros_importacao 
            WHERE erro = 'PA é obrigatório' 
            AND valor LIKE '0 |%'
            ORDER BY importacao_id, linha
        ");
        $stmt->execute();
        $erros_pa_zero = $stmt->fetchAll();
        
        if (empty($erros_pa_zero)) {
            echo "<p>Nenhum erro relacionado ao PA 0 encontrado.</p>";
        } else {
            echo "<p>Encontrados " . count($erros_pa_zero) . " registros para reprocessar.</p>";
            
            $reprocessados = 0;
            $falhas = 0;
            
            foreach ($erros_pa_zero as $erro) {
                try {
                    // Extrair dados do campo valor
                    $dados_linha = explode(' | ', $erro['valor']);
                    
                    if (count($dados_linha) >= 3) {
                        // Mapear dados básicos
                        $data = [
                            'pa' => '0', // Forçar PA como 0
                            'nome_cliente' => trim($dados_linha[1] ?? ''),
                            'numero_cpf_cnpj' => preg_replace('/[^0-9]/', '', $dados_linha[2] ?? ''),
                            'cnae' => trim($dados_linha[3] ?? ''),
                            'data_ultima_atualizacao_renda' => !empty($dados_linha[4]) ? date('Y-m-d', strtotime($dados_linha[4])) : null,
                            'sigla_tipo_pessoa' => 'PF', // Assumir PF para cultivo de café
                            'profissao' => 'Agricultor',
                            'deposito_total' => null,
                            'funcionario' => '',
                            'data_solicitacao_laudo' => null,
                            'tecnico_responsavel' => '',
                            'data_atual_sisbr' => null,
                            'usuario_cadastro' => $_SESSION['user_id']
                        ];
                        
                        // Validar dados básicos
                        if (!empty($data['nome_cliente']) && !empty($data['numero_cpf_cnpj'])) {
                            // Verificar se já existe
                            $stmt_check = $pdo_mci->prepare("
                                SELECT id FROM cad_registros 
                                WHERE pa = ? AND nome_cliente = ? AND numero_cpf_cnpj = ?
                            ");
                            $stmt_check->execute([$data['pa'], $data['nome_cliente'], $data['numero_cpf_cnpj']]);
                            
                            if ($stmt_check->rowCount() == 0) {
                                // Inserir registro
                                $stmt_insert = $pdo_mci->prepare("
                                    INSERT INTO cad_registros (
                                        pa, nome_cliente, numero_cpf_cnpj, cnae, data_ultima_atualizacao_renda,
                                        sigla_tipo_pessoa, profissao, deposito_total, funcionario,
                                        data_solicitacao_laudo, tecnico_responsavel, data_atual_sisbr, usuario_cadastro
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ");
                                
                                $stmt_insert->execute([
                                    $data['pa'], $data['nome_cliente'], $data['numero_cpf_cnpj'], $data['cnae'],
                                    $data['data_ultima_atualizacao_renda'], $data['sigla_tipo_pessoa'], $data['profissao'],
                                    $data['deposito_total'], $data['funcionario'], $data['data_solicitacao_laudo'],
                                    $data['tecnico_responsavel'], $data['data_atual_sisbr'], $data['usuario_cadastro']
                                ]);
                                
                                $reprocessados++;
                                echo "<p>✅ Linha {$erro['linha']}: {$data['nome_cliente']} - Reprocessado com sucesso</p>";
                                
                                // Remover erro da tabela
                                $stmt_del = $pdo_mci->prepare("
                                    DELETE FROM cad_erros_importacao 
                                    WHERE importacao_id = ? AND linha = ? AND erro = 'PA é obrigatório'
                                ");
                                $stmt_del->execute([$erro['importacao_id'], $erro['linha']]);
                                
                            } else {
                                echo "<p>⚠️ Linha {$erro['linha']}: {$data['nome_cliente']} - Já existe no banco</p>";
                            }
                        } else {
                            $falhas++;
                            echo "<p>❌ Linha {$erro['linha']}: Dados insuficientes para reprocessar</p>";
                        }
                    } else {
                        $falhas++;
                        echo "<p>❌ Linha {$erro['linha']}: Formato de dados inválido</p>";
                    }
                    
                } catch (Exception $e) {
                    $falhas++;
                    echo "<p>❌ Linha {$erro['linha']}: Erro - " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
            
            // Atualizar estatísticas das importações
            $stmt_update = $pdo_mci->prepare("
                UPDATE cad_importacoes 
                SET registros_importados = registros_importados + ?, 
                    registros_erro = registros_erro - ? 
                WHERE id IN (
                    SELECT DISTINCT importacao_id 
                    FROM cad_erros_importacao 
                    WHERE erro = 'PA é obrigatório' AND valor LIKE '0 |%'
                )
            ");
            $stmt_update->execute([$reprocessados, $reprocessados]);
            
            echo "<hr>";
            echo "<h3>Resultado do Reprocessamento:</h3>";
            echo "<p><strong>Registros reprocessados com sucesso:</strong> $reprocessados</p>";
            echo "<p><strong>Falhas:</strong> $falhas</p>";
            
            // Log final
            $logger->log('Reprocessamento PA 0 concluído', "Reprocessados: $reprocessados, Falhas: $falhas");
        }
        
        echo "<p><a href='gerenciar.php'>Ver registros</a> | <a href='debug_erros.php'>Ver erros restantes</a></p>";
        
    } catch (Exception $e) {
        echo "<h3 style='color: red;'>❌ Erro durante o reprocessamento</h3>";
        echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
        $logger->logFile("Erro durante reprocessamento PA 0: " . $e->getMessage(), "ERROR");
    }
    
} else {
    // Mostrar estatísticas dos erros
    try {
        echo "<h3>Análise dos Erros</h3>";
        
        $stmt = $pdo_mci->prepare("
            SELECT COUNT(*) as total 
            FROM cad_erros_importacao 
            WHERE erro = 'PA é obrigatório'
        ");
        $stmt->execute();
        $total_erros_pa = $stmt->fetchColumn();
        
        $stmt = $pdo_mci->prepare("
            SELECT COUNT(*) as total 
            FROM cad_erros_importacao 
            WHERE erro = 'PA é obrigatório' AND valor LIKE '0 |%'
        ");
        $stmt->execute();
        $erros_pa_zero = $stmt->fetchColumn();
        
        echo "<table border='1' cellpadding='10'>";
        echo "<tr><th>Tipo de Erro</th><th>Quantidade</th></tr>";
        echo "<tr><td>Total de erros 'PA é obrigatório'</td><td>$total_erros_pa</td></tr>";
        echo "<tr><td>Erros com PA = 0</td><td>$erros_pa_zero</td></tr>";
        echo "</table>";
        
        if ($erros_pa_zero > 0) {
            echo "<h4>Exemplos de registros com PA 0:</h4>";
            $stmt = $pdo_mci->prepare("
                SELECT linha, valor 
                FROM cad_erros_importacao 
                WHERE erro = 'PA é obrigatório' AND valor LIKE '0 |%'
                LIMIT 5
            ");
            $stmt->execute();
            $exemplos = $stmt->fetchAll();
            
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Linha</th><th>Dados</th></tr>";
            foreach ($exemplos as $exemplo) {
                echo "<tr>";
                echo "<td>{$exemplo['linha']}</td>";
                echo "<td>" . htmlspecialchars($exemplo['valor']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Erro ao buscar estatísticas: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Formulário de confirmação
    if ($erros_pa_zero > 0) {
        echo "<hr>";
        echo "<h3>Reprocessar Registros</h3>";
        echo "<form method='POST'>";
        echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ Esta ação irá:</h4>";
        echo "<ul>";
        echo "<li>Reprocessar $erros_pa_zero registros com PA = 0</li>";
        echo "<li>Inserir os registros válidos na tabela principal</li>";
        echo "<li>Remover os erros correspondentes</li>";
        echo "<li>Atualizar as estatísticas de importação</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='margin: 20px 0;'>";
        echo "<label>";
        echo "<input type='checkbox' name='confirmar_reprocessamento' required> ";
        echo "<strong>Eu confirmo que desejo reprocessar os registros com PA 0</strong>";
        echo "</label>";
        echo "</div>";
        
        echo "<button type='submit' style='background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold;'>🔄 REPROCESSAR REGISTROS</button>";
        echo " ";
        echo "<a href='index.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>❌ Cancelar</a>";
        echo "</form>";
    } else {
        echo "<p>Não há registros com PA 0 para reprocessar.</p>";
        echo "<p><a href='index.php'>Voltar ao sistema</a></p>";
    }
}

echo "<hr>";
echo "<p><strong>Nota:</strong> Apenas administradores podem executar esta ação.</p>";
?>
