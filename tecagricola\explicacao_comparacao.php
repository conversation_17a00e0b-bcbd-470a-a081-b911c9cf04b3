<?php
require_once '../auth_check.php';

echo "<h2>📋 Explicação Detalhada - Como Funciona a Comparação de Emails</h2>";

echo "<h3>🔍 1. ORIGEM DOS DADOS</h3>";

echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 10px 0; background: #e7f3ff;'>";
echo "<h4>📊 Banco de Dados MCI (sicoob_access_control.usuarios)</h4>";
echo "<p><strong>Consulta SQL:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "SELECT u.id, u.nome_completo as nome, u.email
FROM sicoob_access_control.usuarios u
INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL";
echo "</pre>";
echo "<p><strong>Campo usado:</strong> <code>u.email</code> (coluna email da tabela usuarios)</p>";
echo "</div>";

echo "<div style='border: 2px solid #28a745; padding: 15px; margin: 10px 0; background: #d4edda;'>";
echo "<h4>🌐 API da Intranet Sicoob</h4>";
echo "<p><strong>URL:</strong> <code>https://intranet.sicoobcredilivre.com.br/api</code></p>";
echo "<p><strong>Módulo:</strong> <code>Usuarios</code></p>";
echo "<p><strong>Ação:</strong> <code>listarUsuarios</code></p>";
echo "<p><strong>Campo usado:</strong> <code>email</code> (campo email retornado pela API)</p>";
echo "<p><strong>Filtros aplicados:</strong></p>";
echo "<ul>";
echo "<li>status = 1 (usuário ativo)</li>";
echo "<li>bloqueado = 0 (usuário não bloqueado)</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔄 2. PROCESSO DE COMPARAÇÃO</h3>";

echo "<div style='border: 2px solid #ffc107; padding: 15px; margin: 10px 0; background: #fff3cd;'>";
echo "<h4>⚙️ Etapas do Processamento</h4>";
echo "<ol>";
echo "<li><strong>Buscar técnicos no banco MCI:</strong> Consulta SQL retorna lista de técnicos com seus emails</li>";
echo "<li><strong>Carregar usuários da API:</strong> Chama API da Intranet e cria mapa indexado por email</li>";
echo "<li><strong>Normalização:</strong> Ambos os emails são convertidos para minúsculo e removidos espaços</li>";
echo "<li><strong>Comparação:</strong> Busca exata do email normalizado no mapa da API</li>";
echo "<li><strong>Resultado:</strong> Se encontrado, adiciona foto_url, setor e função ao técnico</li>";
echo "</ol>";
echo "</div>";

echo "<h3>💻 3. CÓDIGO DA COMPARAÇÃO</h3>";

echo "<div style='border: 2px solid #6f42c1; padding: 15px; margin: 10px 0; background: #f8f9fa;'>";
echo "<h4>🔧 Código PHP Atual</h4>";
echo "<pre style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('
// 1. Buscar técnicos no banco MCI
$stmt = $pdo_mci->prepare("SELECT u.email FROM sicoob_access_control.usuarios u...");
$tecnicos_dados = $stmt->fetchAll();

// 2. Carregar usuários da API Intranet
$intranetAPI = getIntranetAPI($logger);
$usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();

// 3. Para cada técnico, tentar encontrar na API
foreach ($tecnicos_dados as $tecnico) {
    if (!empty($tecnico[\'email\'])) {
        // NORMALIZAÇÃO
        $email_key = strtolower(trim($tecnico[\'email\']));
        
        // COMPARAÇÃO EXATA
        $usuario_intranet = $usuarios_intranet[$email_key] ?? null;
        
        if ($usuario_intranet) {
            // ENCONTRADO: adicionar dados da API
            $tecnico[\'foto_url\'] = $usuario_intranet[\'foto_url\'] ?? null;
            $tecnico[\'setor\'] = $usuario_intranet[\'setor_nome\'] ?? null;
            $tecnico[\'funcao\'] = $usuario_intranet[\'funcao_nome\'] ?? null;
        } else {
            // NÃO ENCONTRADO: usar placeholder
            $tecnico[\'foto_url\'] = null;
        }
    }
}
');
echo "</pre>";
echo "</div>";

echo "<h3>📧 4. EXEMPLO PRÁTICO - MAYCON</h3>";

// Buscar dados reais do Maycon
$stmt = $pdo_mci->prepare("
    SELECT u.id, u.nome_completo as nome, u.email
    FROM sicoob_access_control.usuarios u
    WHERE u.id = 58
");
$stmt->execute();
$maycon = $stmt->fetch();

echo "<div style='border: 2px solid #dc3545; padding: 15px; margin: 10px 0; background: #f8d7da;'>";
echo "<h4>❌ Caso do Maycon (ID 58)</h4>";

if ($maycon) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Fonte</th><th>Email</th><th>Email Normalizado</th></tr>";
    
    $email_banco = $maycon['email'];
    $email_normalizado = strtolower(trim($email_banco));
    
    echo "<tr>";
    echo "<td><strong>Banco MCI</strong></td>";
    echo "<td><code>" . htmlspecialchars($email_banco) . "</code></td>";
    echo "<td><code>" . htmlspecialchars($email_normalizado) . "</code></td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>API Intranet</strong></td>";
    echo "<td colspan='2'><em>❌ Email não encontrado na API</em></td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<p><strong>Resultado:</strong> Como o email <code>" . htmlspecialchars($email_normalizado) . "</code> não existe na API da Intranet, o Maycon não recebe foto e usa o placeholder.</p>";
} else {
    echo "<p style='color: red;'>❌ Maycon não encontrado no banco!</p>";
}

echo "</div>";

echo "<h3>✅ 5. RESUMO DA COMPARAÇÃO</h3>";

echo "<div style='border: 2px solid #17a2b8; padding: 15px; margin: 10px 0; background: #d1ecf1;'>";
echo "<h4>📝 Pontos Importantes</h4>";
echo "<ul>";
echo "<li><strong>Comparação:</strong> Email do banco MCI ↔ Email da API Intranet</li>";
echo "<li><strong>Método:</strong> Busca exata (case-insensitive)</li>";
echo "<li><strong>Normalização:</strong> <code>strtolower(trim(\$email))</code></li>";
echo "<li><strong>Estrutura:</strong> Array associativo <code>[\$email_normalizado => \$dados_usuario]</code></li>";
echo "<li><strong>Fallback:</strong> Se não encontrado, usa placeholder (iniciais do nome)</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔧 6. POSSÍVEIS SOLUÇÕES</h3>";

echo "<div style='border: 2px solid #28a745; padding: 15px; margin: 10px 0; background: #d4edda;'>";
echo "<h4>💡 Opções para Resolver</h4>";
echo "<ol>";
echo "<li><strong>Verificar email correto na API:</strong> Usar script de busca para encontrar variações</li>";
echo "<li><strong>Atualizar email no banco:</strong> Corrigir email do técnico na tabela usuarios</li>";
echo "<li><strong>Solicitar correção na Intranet:</strong> Pedir ao TI para adicionar/corrigir email</li>";
echo "<li><strong>Implementar busca por nome:</strong> Fallback para buscar por nome se email não encontrado</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🛠️ 7. FERRAMENTAS DE DEBUG</h3>";

echo "<div style='border: 2px solid #6c757d; padding: 15px; margin: 10px 0; background: #f8f9fa;'>";
echo "<h4>🔍 Scripts Disponíveis</h4>";
echo "<ul>";
echo "<li><a href='buscar_maycon_api.php'>🎯 Buscar Maycon na API</a> - Procura variações do email</li>";
echo "<li><a href='debug_api_intranet.php'>🌐 Debug API Intranet</a> - Testa todos os técnicos</li>";
echo "<li><a href='debug_maycon.php'>👤 Debug Específico Maycon</a> - Análise detalhada</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><a href='dashboard.php'>🚀 Dashboard</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
