<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

// Verificar se usuário tem permissão de administrador
if ($_SESSION['mci_permission_level'] !== 'administrador') {
    header('Location: access_denied.php');
    exit;
}

$logger = new MciLogger();
$logger->log('Acesso ao módulo MCI', 'Usuário acessou a página principal do módulo MCI');

// Buscar estatísticas básicas
try {
    // Total de registros
    $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_registros");
    $stmt->execute();
    $total_registros = $stmt->fetchColumn();

    // Registros por status
    $stmt = $pdo_mci->prepare("
        SELECT status, COUNT(*) as quantidade 
        FROM cad_registros 
        GROUP BY status
    ");
    $stmt->execute();
    $registros_por_status = $stmt->fetchAll();

    // Últimas importações
    $stmt = $pdo_mci->prepare("
        SELECT * FROM cad_importacoes 
        ORDER BY data_importacao DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $ultimas_importacoes = $stmt->fetchAll();

} catch (Exception $e) {
    $logger->logFile("Erro ao buscar estatísticas: " . $e->getMessage(), 'ERROR');
    $total_registros = 0;
    $registros_por_status = [];
    $ultimas_importacoes = [];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo MCI_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-roxo: #49479D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body {
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: var(--sicoob-verde-escuro) !important;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.2);
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: white;
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .btn-primary:hover {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-success {
            background-color: var(--sicoob-verde-claro);
            border-color: var(--sicoob-verde-claro);
            color: var(--sicoob-verde-escuro);
        }

        .text-primary {
            color: var(--sicoob-verde-escuro) !important;
        }

        .stats-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--sicoob-verde-escuro);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <img src="../assets/images/logo1.png" alt="Sicoob" height="40">
                <span class="ms-2"><?php echo MCI_PROJECT_NAME; ?></span>
            </a>
            <div class="navbar-nav ms-auto">
                <!-- Atalhos para Dashboards -->
                <a class="nav-link" href="cadastro/dashboard.php" target="_blank">
                    <i class="fas fa-tv"></i> Dashboard Cadastro
                </a>
                <a class="nav-link" href="tecagricola/dashboard.php" target="_blank">
                    <i class="fas fa-seedling"></i> Dashboard Técnicos
                </a>

                <a class="nav-link" href="ajuda.php">
                    <i class="fas fa-question-circle"></i> Ajuda
                </a>
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-arrow-left"></i> Voltar ao Dashboard Principal
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-primary">
                    <i class="fas fa-user-edit"></i> 
                    Sistema de Atualizações Cadastrais
                </h1>
                <p class="text-muted">Gerencie e acompanhe o progresso das atualizações cadastrais</p>
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($total_registros); ?></div>
                        <div class="text-muted">Total de Registros</div>
                    </div>
                </div>
            </div>
            <?php foreach ($registros_por_status as $status): ?>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($status['quantidade']); ?></div>
                        <div class="text-muted"><?php echo ucfirst($status['status']); ?></div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Menu Principal -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload"></i> Importar Dados
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Importe a planilha Excel com os dados de atualizações cadastrais.</p>
                        <a href="importar.php" class="btn btn-primary">
                            <i class="fas fa-file-excel"></i> Importar Planilha
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> Gerenciar Registros
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Visualize, edite e gerencie os registros de atualizações cadastrais.</p>
                        <a href="gerenciar.php" class="btn btn-success">
                            <i class="fas fa-edit"></i> Gerenciar
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar"></i> Dashboard
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Visualize métricas e relatórios detalhados dos registros.</p>
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="fas fa-chart-line"></i> Ver Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Últimas Importações -->
        <?php if (!empty($ultimas_importacoes)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history"></i> Últimas Importações
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Arquivo</th>
                                        <th>Data</th>
                                        <th>Registros</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ultimas_importacoes as $importacao): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($importacao['nome_arquivo']); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($importacao['data_importacao'])); ?></td>
                                        <td><?php echo number_format($importacao['total_registros']); ?></td>
                                        <td>
                                            <?php
                                            $badge_class = '';
                                            switch ($importacao['status']) {
                                                case 'concluido':
                                                    $badge_class = 'bg-success';
                                                    break;
                                                case 'processando':
                                                    $badge_class = 'bg-warning';
                                                    break;
                                                case 'erro':
                                                    $badge_class = 'bg-danger';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $badge_class; ?>">
                                                <?php echo ucfirst($importacao['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Monitor em tempo real
        function atualizarEstatisticas() {
            fetch('monitor.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Atualizar contadores se existirem
                        const totalElement = document.querySelector('.stats-number');
                        if (totalElement && data.total_registros !== undefined) {
                            totalElement.textContent = new Intl.NumberFormat().format(data.total_registros);
                        }

                        // Atualizar timestamp
                        let timestampElement = document.getElementById('last-update');
                        if (!timestampElement) {
                            timestampElement = document.createElement('small');
                            timestampElement.id = 'last-update';
                            timestampElement.className = 'text-muted';
                            timestampElement.style.position = 'fixed';
                            timestampElement.style.bottom = '10px';
                            timestampElement.style.right = '10px';
                            timestampElement.style.backgroundColor = 'rgba(255,255,255,0.9)';
                            timestampElement.style.padding = '5px 10px';
                            timestampElement.style.borderRadius = '5px';
                            timestampElement.style.fontSize = '12px';
                            document.body.appendChild(timestampElement);
                        }
                        timestampElement.textContent = `Última atualização: ${new Date(data.timestamp).toLocaleTimeString()}`;
                    }
                })
                .catch(error => {
                    console.error('Erro ao atualizar estatísticas:', error);
                });
        }

        // Atualizar a cada 30 segundos
        setInterval(atualizarEstatisticas, 30000);

        // Primeira atualização após 5 segundos
        setTimeout(atualizarEstatisticas, 5000);
    </script>
</body>
</html>
