<?php
// Versão de debug do export Excel
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log de debug
error_log("Export Excel Debug - Iniciado em " . date('Y-m-d H:i:s'));
error_log("GET params: " . print_r($_GET, true));

// Verificar se é uma requisição de download
if (!isset($_GET['download']) || $_GET['download'] !== 'excel') {
    error_log("Export Excel Debug - Parâmetro download não encontrado ou inválido");
    echo "Erro: Parâmetro 'download=excel' não encontrado.";
    echo "<br>Parâmetros recebidos: " . print_r($_GET, true);
    exit;
}

// Tentar incluir auth_check.php
try {
    require_once 'auth_check.php';
    error_log("Export Excel Debug - Auth check OK");
} catch (Exception $e) {
    error_log("Export Excel Debug - Erro no auth_check: " . $e->getMessage());
    echo "Erro na autenticação: " . $e->getMessage();
    exit;
}

// Função para formatar CPF/CNPJ
function formatarCpfCnpj($numero) {
    $numero = preg_replace('/\D/', '', $numero);
    
    if (strlen($numero) == 11) {
        // CPF
        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $numero);
    } elseif (strlen($numero) == 14) {
        // CNPJ
        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $numero);
    }
    
    return $numero;
}

try {
    error_log("Export Excel Debug - Iniciando processamento");
    
    // Receber filtros da URL
    $filtro_status = $_GET['status'] ?? '';
    $filtro_pa = $_GET['pa'] ?? '';
    $filtro_associado = $_GET['associado'] ?? '';
    $filtro_funcionario = $_GET['funcionario'] ?? '';
    $filtro_tecnico = $_GET['tecnico'] ?? '';
    $filtro_mes_renda = $_GET['mes_renda'] ?? '';
    $sort_column = $_GET['sort'] ?? 'data_cadastro';
    $sort_direction = $_GET['dir'] ?? 'desc';

    error_log("Export Excel Debug - Filtros: status=$filtro_status, pa=$filtro_pa, sort=$sort_column");

    // Validar colunas de ordenação
    $valid_columns = [
        'pa' => 'cr.pa',
        'nome_cliente' => 'cr.nome_cliente',
        'data_ultima_atualizacao_renda' => 'cr.data_ultima_atualizacao_renda',
        'funcionario' => 'u_func.nome_completo',
        'data_solicitacao_laudo' => 'cr.data_solicitacao_laudo',
        'tecnico_responsavel' => 'u_tec.nome_completo',
        'data_atual_sisbr' => 'cr.data_atual_sisbr',
        'status' => 'cs.nome',
        'data_cadastro' => 'cr.data_cadastro'
    ];

    $order_column = $valid_columns[$sort_column] ?? $valid_columns['data_cadastro'];
    $order_direction = strtolower($sort_direction) === 'asc' ? 'ASC' : 'DESC';

    // Construir query com filtros
    $where_conditions = [];
    $params = [];

    if (!empty($filtro_status)) {
        $where_conditions[] = "cs.nome = ?";
        $params[] = $filtro_status;
    }

    if (!empty($filtro_pa)) {
        $where_conditions[] = "cr.pa = ?";
        $params[] = $filtro_pa;
    }

    if (!empty($filtro_associado)) {
        $where_conditions[] = "cr.nome_cliente LIKE ?";
        $params[] = "%$filtro_associado%";
    }

    if (!empty($filtro_funcionario)) {
        $where_conditions[] = "cr.funcionario = ?";
        $params[] = $filtro_funcionario;
    }

    if (!empty($filtro_tecnico)) {
        $where_conditions[] = "cr.tecnico_responsavel = ?";
        $params[] = $filtro_tecnico;
    }

    if (!empty($filtro_mes_renda)) {
        $where_conditions[] = "DATE_FORMAT(cr.data_ultima_atualizacao_renda, '%Y-%m') = ?";
        $params[] = $filtro_mes_renda;
    }

    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }

    error_log("Export Excel Debug - Where clause: $where_clause");
    error_log("Export Excel Debug - Params: " . print_r($params, true));

    // Query principal
    $sql = "
        SELECT 
            cr.id,
            cr.pa,
            pa.nome as nome_ponto_atendimento,
            cr.nome_cliente,
            cr.numero_cpf_cnpj,
            cr.data_ultima_atualizacao_renda,
            cr.funcionario,
            u_func.nome_completo as funcionario_nome,
            cr.data_solicitacao_laudo,
            cr.tecnico_responsavel,
            u_tec.nome_completo as tecnico_nome,
            cr.data_atual_sisbr,
            cr.status,
            cs.nome as status_nome,
            cr.data_cadastro,
            cr.observacoes
        FROM cad_registros cr
        LEFT JOIN sicoob_access_control.pontos_atendimento pa ON cr.pa = pa.numero
        LEFT JOIN sicoob_access_control.usuarios u_func ON cr.funcionario = u_func.id
        LEFT JOIN sicoob_access_control.usuarios u_tec ON cr.tecnico_responsavel = u_tec.id
        INNER JOIN cad_status cs ON cr.status = cs.id
        $where_clause
        ORDER BY $order_column $order_direction
    ";

    error_log("Export Excel Debug - Executando query");
    $stmt = $pdo_mci->prepare($sql);
    $stmt->execute($params);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    error_log("Export Excel Debug - Registros encontrados: " . count($registros));

    if (count($registros) == 0) {
        error_log("Export Excel Debug - Nenhum registro encontrado");
        echo "Nenhum registro encontrado com os filtros aplicados.";
        exit;
    }

    // Configurar headers para download
    $filename = 'relatorio_mci_debug_' . date('Y-m-d_H-i-s') . '.csv';
    
    error_log("Export Excel Debug - Configurando headers para download: $filename");
    
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    // Abrir output
    $output = fopen('php://output', 'w');
    
    // BOM para UTF-8
    fwrite($output, "\xEF\xBB\xBF");

    // Cabeçalhos
    $headers = [
        'ID',
        'PA',
        'Nome do PA',
        'Nome do Cliente',
        'CPF/CNPJ',
        'Última Atualização Renda',
        'Funcionário',
        'Data Solicitação Laudo',
        'Técnico Responsável',
        'Data Atualização SISBR',
        'Status',
        'Data Cadastro',
        'Observações'
    ];

    fputcsv($output, $headers, ';');

    // Dados
    foreach ($registros as $registro) {
        $row = [
            $registro['id'],
            $registro['pa'],
            $registro['nome_ponto_atendimento'] ?? '',
            $registro['nome_cliente'],
            formatarCpfCnpj($registro['numero_cpf_cnpj']),
            !empty($registro['data_ultima_atualizacao_renda']) ? date('d/m/Y', strtotime($registro['data_ultima_atualizacao_renda'])) : '',
            $registro['funcionario_nome'] ?? $registro['funcionario'] ?? '',
            !empty($registro['data_solicitacao_laudo']) ? date('d/m/Y', strtotime($registro['data_solicitacao_laudo'])) : '',
            $registro['tecnico_nome'] ?? $registro['tecnico_responsavel'] ?? '',
            !empty($registro['data_atual_sisbr']) ? date('d/m/Y', strtotime($registro['data_atual_sisbr'])) : '',
            $registro['status_nome'] ?? '',
            !empty($registro['data_cadastro']) ? date('d/m/Y H:i:s', strtotime($registro['data_cadastro'])) : '',
            $registro['observacoes'] ?? ''
        ];

        fputcsv($output, $row, ';');
    }

    fclose($output);
    error_log("Export Excel Debug - Download concluído com sucesso");
    exit;

} catch (Exception $e) {
    error_log("Export Excel Debug - Erro: " . $e->getMessage());
    error_log("Export Excel Debug - Stack trace: " . $e->getTraceAsString());
    
    // Não redirecionar, mostrar erro
    echo "Erro no export: " . $e->getMessage();
    echo "<br>Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    exit;
}
?>
