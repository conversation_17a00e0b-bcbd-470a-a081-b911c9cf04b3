<?php
// Herda o sistema de autenticação do projeto principal
session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/Permissions.php';
require_once __DIR__ . '/config/config.php';

// Criar alias para compatibilidade com código que usa $pdo_sicoob
$pdo_sicoob = $pdo;

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Criar instância de Permissions usando o banco principal
$permissions = new Permissions($pdo, $_SESSION['user_id']);

// Função para verificar acesso às páginas MCI
function checkMciAccess($required_level = 'user') {
    global $permissions;
    
    switch ($required_level) {
        case 'admin':
            if (!$permissions->isAdmin()) {
                header('Location: ../access_denied.php');
                exit;
            }
            break;
        case 'gestor':
            if (!$permissions->isGestor() && !$permissions->isAdmin()) {
                header('Location: ../access_denied.php');
                exit;
            }
            break;
        case 'user':
            // Todos os usuários autenticados podem acessar
            break;
    }
}

// Função para verificar se o usuário tem permissão específica para MCI
function checkMciPermission() {
    global $pdo_mci;

    // Verificar se o usuário tem permissão para acessar o módulo MCI
    $stmt = $pdo_mci->prepare("
        SELECT nivel_acesso
        FROM mci_permissions
        WHERE usuario_id = ? AND ativo = TRUE
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $permission = $stmt->fetch();

    return $permission ? $permission['nivel_acesso'] : false;
}

// Função para verificar nível específico de acesso MCI
function checkMciLevel($required_level) {
    $user_level = checkMciPermission();

    if (!$user_level) {
        return false;
    }

    $levels = ['comum' => 1, 'gestor' => 2, 'administrador' => 3];
    $user_level_num = $levels[$user_level] ?? 0;
    $required_level_num = $levels[$required_level] ?? 0;

    return $user_level_num >= $required_level_num;
}

// Verificar permissão básica para MCI
$user_permission = checkMciPermission();
if (!$user_permission) {
    header('Location: access_denied.php');
    exit;
}

// Disponibilizar nível de permissão globalmente
$_SESSION['mci_permission_level'] = $user_permission;
?>
