<?php
require_once '../auth_check.php';

echo "<h2>Teste de Conexão - Dashboard Técnicos Agrícolas</h2>";

// Testar conexão com banco MCI
try {
    echo "<h3>✅ Conexão com banco MCI: OK</h3>";
    
    // Testar consulta de técnicos
    $stmt = $pdo_mci->prepare("
        SELECT 
            u.id,
            u.nome_completo as nome,
            u.email,
            COUNT(r.id) as meta_total,
            COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
        GROUP BY u.id, u.nome_completo, u.email
        HAVING meta_total > 0
        ORDER BY u.nome_completo
        LIMIT 5
    ");
    $stmt->execute();
    $tecnicos = $stmt->fetchAll();
    
    echo "<h3>📊 Técnicos encontrados: " . count($tecnicos) . "</h3>";
    
    if (count($tecnicos) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Meta Total</th><th>Atualizados</th></tr>";
        
        foreach ($tecnicos as $tecnico) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($tecnico['id']) . "</td>";
            echo "<td>" . htmlspecialchars($tecnico['nome']) . "</td>";
            echo "<td>" . htmlspecialchars($tecnico['email']) . "</td>";
            echo "<td>" . htmlspecialchars($tecnico['meta_total']) . "</td>";
            echo "<td>" . htmlspecialchars($tecnico['atualizados_total']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>⚠️ Nenhum técnico encontrado com registros atribuídos.</p>";
        
        // Verificar se existem registros com tecnico_responsavel
        $stmt_check = $pdo_mci->prepare("
            SELECT COUNT(*) as total, 
                   COUNT(CASE WHEN tecnico_responsavel IS NOT NULL THEN 1 END) as com_tecnico
            FROM cad_registros
        ");
        $stmt_check->execute();
        $check_result = $stmt_check->fetch();
        
        echo "<p>📋 Total de registros: " . $check_result['total'] . "</p>";
        echo "<p>👨‍🌾 Registros com técnico responsável: " . $check_result['com_tecnico'] . "</p>";
        
        if ($check_result['com_tecnico'] == 0) {
            echo "<p>❌ Nenhum registro possui técnico responsável atribuído.</p>";
            echo "<p>💡 Verifique se a coluna 'tecnico_responsavel' está sendo preenchida corretamente.</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<h3>❌ Erro na consulta: " . htmlspecialchars($e->getMessage()) . "</h3>";
}

// Testar estrutura das tabelas
echo "<h3>🔍 Verificação de Estrutura</h3>";

try {
    // Verificar tabela cad_registros
    $stmt = $pdo_mci->prepare("DESCRIBE cad_registros");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $has_tecnico_responsavel = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'tecnico_responsavel') {
            $has_tecnico_responsavel = true;
            break;
        }
    }
    
    if ($has_tecnico_responsavel) {
        echo "<p>✅ Coluna 'tecnico_responsavel' existe na tabela cad_registros</p>";
    } else {
        echo "<p>❌ Coluna 'tecnico_responsavel' NÃO existe na tabela cad_registros</p>";
        echo "<p>📋 Colunas disponíveis:</p>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>" . htmlspecialchars($column['Field']) . " (" . htmlspecialchars($column['Type']) . ")</li>";
        }
        echo "</ul>";
    }
    
    // Verificar tabela cad_status
    $stmt = $pdo_mci->prepare("SELECT * FROM cad_status");
    $stmt->execute();
    $status_list = $stmt->fetchAll();
    
    echo "<p>📊 Status disponíveis:</p>";
    echo "<ul>";
    foreach ($status_list as $status) {
        echo "<li>ID: " . $status['id'] . " - Nome: " . htmlspecialchars($status['nome']) . "</li>";
    }
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p>❌ Erro ao verificar estrutura: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='dashboard.php'>🚀 Ir para Dashboard</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
