<?php
/**
 * Página de navegação do módulo Dashboard MCI
 */

require_once '../auth_check.php';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard MCI - Módulo de Cadastro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
        }

        body { 
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .card { 
            border: none; 
            border-radius: 15px; 
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }
        
        .card-header { 
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); 
            color: white; 
            border-radius: 15px 15px 0 0 !important;
        }
        
        .feature-card {
            text-align: center;
            padding: 2rem;
            height: 100%;
        }
        
        .feature-icon {
            font-size: 3rem;
            color: var(--sicoob-turquesa);
            margin-bottom: 1rem;
        }
        
        .feature-title {
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .btn-feature {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-feature:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 174, 157, 0.3);
            color: white;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-online { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-offline { background-color: #dc3545; }
        
        .hero-section {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: white;
            padding: 3rem 0;
            margin-bottom: 3rem;
            border-radius: 0 0 30px 30px;
        }
        
        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <img src="../../assets/images/logo1.png" alt="Sicoob" height="40">
                <span class="ms-2">MCI - Dashboard</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.php">
                    <i class="fas fa-arrow-left"></i> Voltar ao MCI
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container text-center">
            <h1 class="hero-title">
                <i class="fas fa-chart-line"></i> Dashboard MCI
            </h1>
            <p class="hero-subtitle">
                Monitoramento em tempo real das atualizações cadastrais
            </p>
        </div>
    </div>

    <div class="container">
        <div class="row g-4">
            <!-- Dashboard Principal -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-body feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <h4 class="feature-title">Dashboard para TV</h4>
                        <p class="text-muted mb-4">
                            Dashboard otimizado para exibição em televisões e monitores grandes. 
                            Mostra métricas em tempo real com gráficos modernos e fotos dos funcionários.
                        </p>
                        <div class="mb-3">
                            <span class="status-indicator status-online"></span>
                            <small class="text-muted">Sistema Online</small>
                        </div>
                        <a href="dashboard.php" class="btn btn-feature">
                            <i class="fas fa-external-link-alt"></i> Abrir Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Teste de API -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-body feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h4 class="feature-title">Teste de API</h4>
                        <p class="text-muted mb-4">
                            Ferramenta para testar a conectividade com a API da Intranet, 
                            verificar dados dos funcionários e diagnosticar problemas.
                        </p>
                        <div class="mb-3">
                            <span class="status-indicator status-warning"></span>
                            <small class="text-muted">Teste Recomendado</small>
                        </div>
                        <a href="test_api.php" class="btn btn-feature">
                            <i class="fas fa-vial"></i> Executar Teste
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações Técnicas -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> Informações Técnicas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-cogs"></i> Características</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Auto-refresh a cada 5 minutos</li>
                                    <li><i class="fas fa-check text-success"></i> Integração com API da Intranet</li>
                                    <li><i class="fas fa-check text-success"></i> Gráficos interativos Chart.js</li>
                                    <li><i class="fas fa-check text-success"></i> Design responsivo para TV</li>
                                    <li><i class="fas fa-check text-success"></i> Fotos dos funcionários</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-chart-bar"></i> Métricas Exibidas</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-calendar-alt text-primary"></i> Metas mensais individuais</li>
                                    <li><i class="fas fa-calendar-check text-primary"></i> Metas anuais individuais</li>
                                    <li><i class="fas fa-users text-primary"></i> Métricas gerais da equipe</li>
                                    <li><i class="fas fa-percentage text-primary"></i> Progresso em percentual</li>
                                    <li><i class="fas fa-chart-pie text-primary"></i> Gráficos de rosca</li>
                                </ul>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-desktop"></i> Resolução Recomendada</h6>
                                <p class="text-muted mb-0">1920x1080 ou superior</p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-sync-alt"></i> Atualização</h6>
                                <p class="text-muted mb-0">Automática a cada 5 minutos</p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-users-cog"></i> Funcionários</h6>
                                <p class="text-muted mb-0">6 funcionários monitorados</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Links Úteis -->
        <div class="row mt-4 mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-link"></i> Links Úteis
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="../gerenciar.php" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-list"></i> Gerenciar Registros
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="../metas.php" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-target"></i> Página de Metas
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="../index.php" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-home"></i> Página Principal
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="README.md" class="btn btn-outline-secondary w-100 mb-2" target="_blank">
                                    <i class="fas fa-book"></i> Documentação
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
