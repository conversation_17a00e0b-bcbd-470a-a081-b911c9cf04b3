<?php
require_once 'auth_check.php';

echo "<h2>Teste da Funcionalidade de Transferência em Massa</h2>";

try {
    // Testar conexão com banco MCI
    echo "<h3>1. Testando conexão com banco MCI...</h3>";
    $stmt = $pdo_mci->query("SELECT COUNT(*) as total FROM cad_registros");
    $total_registros = $stmt->fetchColumn();
    echo "✅ Conexão OK - Total de registros: $total_registros<br>";
    
    // Testar conexão com banco Sicoob
    echo "<h3>2. Testando conexão com banco Sicoob...</h3>";
    $stmt = $pdo_sicoob->query("SELECT COUNT(*) as total FROM usuarios WHERE ativo = TRUE");
    $total_usuarios = $stmt->fetchColumn();
    echo "✅ Conexão OK - Total de usuários ativos: $total_usuarios<br>";
    
    // Testar busca de funcionários do setor 8
    echo "<h3>3. Testando busca de funcionários do setor 8...</h3>";
    $stmt_funcionarios = $pdo_sicoob->prepare("
        SELECT u.id, u.nome_completo
        FROM usuarios u
        INNER JOIN usuario_setor us ON u.id = us.usuario_id
        WHERE us.setor_id = 8 AND u.ativo = TRUE
        ORDER BY u.nome_completo
    ");
    $stmt_funcionarios->execute();
    $funcionarios = $stmt_funcionarios->fetchAll();
    echo "✅ Encontrados " . count($funcionarios) . " funcionários do setor 8:<br>";
    foreach ($funcionarios as $funcionario) {
        echo "- ID: {$funcionario['id']} - {$funcionario['nome_completo']}<br>";
    }
    
    // Testar distribuição de registros por funcionário
    echo "<h3>4. Testando distribuição de registros por funcionário...</h3>";
    $stmt_distribuicao = $pdo_mci->prepare("
        SELECT 
            cr.funcionario,
            u.nome_completo,
            COUNT(*) as total_registros,
            SUM(CASE WHEN cs.nome != 'Removido' THEN 1 ELSE 0 END) as registros_disponiveis
        FROM cad_registros cr
        LEFT JOIN sicoob_access_control.usuarios u ON cr.funcionario = u.id
        INNER JOIN cad_status cs ON cr.status = cs.id
        WHERE cr.funcionario IS NOT NULL
        GROUP BY cr.funcionario, u.nome_completo
        ORDER BY total_registros DESC
        LIMIT 10
    ");
    $stmt_distribuicao->execute();
    $distribuicao = $stmt_distribuicao->fetchAll();
    
    echo "✅ Top 10 funcionários com mais registros:<br>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Total</th><th>Disponíveis</th><th>Removidos</th></tr>";
    foreach ($distribuicao as $func) {
        $removidos = $func['total_registros'] - $func['registros_disponiveis'];
        echo "<tr>";
        echo "<td>{$func['funcionario']}</td>";
        echo "<td>{$func['nome_completo']}</td>";
        echo "<td>{$func['total_registros']}</td>";
        echo "<td>{$func['registros_disponiveis']}</td>";
        echo "<td>$removidos</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Testar estrutura da tabela cad_status
    echo "<h3>5. Testando estrutura da tabela cad_status...</h3>";
    $stmt_status = $pdo_mci->query("SELECT id, nome FROM cad_status ORDER BY id");
    $status_list = $stmt_status->fetchAll();
    echo "✅ Status disponíveis:<br>";
    foreach ($status_list as $status) {
        echo "- ID: {$status['id']} - {$status['nome']}<br>";
    }
    
    // Testar simulação de transferência
    echo "<h3>6. Testando simulação de transferência...</h3>";
    if (count($distribuicao) >= 2) {
        $funcionario_origem = $distribuicao[0];
        $funcionario_destino = $distribuicao[1];
        
        echo "Simulando transferência de 5 registros:<br>";
        echo "- De: {$funcionario_origem['nome_completo']} (ID: {$funcionario_origem['funcionario']}) - {$funcionario_origem['registros_disponiveis']} disponíveis<br>";
        echo "- Para: {$funcionario_destino['nome_completo']} (ID: {$funcionario_destino['funcionario']})<br>";
        
        // Buscar registros que seriam transferidos
        $stmt_simulacao = $pdo_mci->prepare("
            SELECT id, nome_associado, cpf_cnpj
            FROM cad_registros cr
            INNER JOIN cad_status cs ON cr.status = cs.id
            WHERE cr.funcionario = ? 
            AND cs.nome != 'Removido'
            ORDER BY cr.data_cadastro ASC 
            LIMIT 5
        ");
        $stmt_simulacao->execute([$funcionario_origem['funcionario']]);
        $registros_simulacao = $stmt_simulacao->fetchAll();
        
        echo "✅ Registros que seriam transferidos:<br>";
        foreach ($registros_simulacao as $registro) {
            echo "- ID: {$registro['id']} - {$registro['nome_associado']} - {$registro['cpf_cnpj']}<br>";
        }
    } else {
        echo "⚠️ Não há funcionários suficientes para simular transferência<br>";
    }
    
    echo "<h3>7. Teste do arquivo AJAX...</h3>";
    if (file_exists('ajax/get_funcionario_registros.php')) {
        echo "✅ Arquivo AJAX existe<br>";
    } else {
        echo "❌ Arquivo AJAX não encontrado<br>";
    }
    
    echo "<h3>8. Teste da página principal...</h3>";
    if (file_exists('transferencia_massa.php')) {
        echo "✅ Página principal existe<br>";
    } else {
        echo "❌ Página principal não encontrada<br>";
    }
    
    echo "<h3>✅ Todos os testes concluídos com sucesso!</h3>";
    echo "<p><a href='transferencia_massa.php'>Ir para a página de Transferência em Massa</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante os testes:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
