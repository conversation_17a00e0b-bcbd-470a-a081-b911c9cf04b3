<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Converter Colunas de Usuários para IDs</h1>";
echo "<hr>";

try {
    echo "<h3>1. Verificando estrutura atual...</h3>";
    
    // Verificar estrutura atual das colunas
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field IN ('funcionario', 'tecnico_responsavel')");
    $stmt->execute();
    $colunas_atuais = $stmt->fetchAll();
    
    echo "<h4>Estrutura atual:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Padr<PERSON></th></tr>";
    foreach ($colunas_atuais as $coluna) {
        echo "<tr>";
        echo "<td><strong>{$coluna['Field']}</strong></td>";
        echo "<td><code>{$coluna['Type']}</code></td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>" . ($coluna['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar dados atuais
    echo "<h3>2. Analisando dados atuais...</h3>";
    
    $stmt = $pdo_mci->prepare("
        SELECT 
            funcionario, COUNT(*) as total_funcionario
        FROM cad_registros 
        WHERE funcionario IS NOT NULL AND funcionario != ''
        GROUP BY funcionario
        ORDER BY total_funcionario DESC
        LIMIT 10
    ");
    $stmt->execute();
    $funcionarios = $stmt->fetchAll();
    
    $stmt = $pdo_mci->prepare("
        SELECT 
            tecnico_responsavel, COUNT(*) as total_tecnico
        FROM cad_registros 
        WHERE tecnico_responsavel IS NOT NULL AND tecnico_responsavel != ''
        GROUP BY tecnico_responsavel
        ORDER BY total_tecnico DESC
        LIMIT 10
    ");
    $stmt->execute();
    $tecnicos = $stmt->fetchAll();
    
    echo "<h4>Funcionários mais frequentes:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Nome</th><th>Quantidade</th></tr>";
    foreach ($funcionarios as $func) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($func['funcionario']) . "</td>";
        echo "<td>{$func['total_funcionario']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>Técnicos mais frequentes:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Nome</th><th>Quantidade</th></tr>";
    foreach ($tecnicos as $tec) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($tec['tecnico_responsavel']) . "</td>";
        echo "<td>{$tec['total_tecnico']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar usuários disponíveis
    echo "<h3>3. Verificando usuários disponíveis...</h3>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM usuarios WHERE ativo = 1");
    $stmt->execute();
    $total_usuarios = $stmt->fetchColumn();

    $stmt = $pdo->prepare("SELECT id, nome_completo, email FROM usuarios WHERE ativo = 1 ORDER BY nome_completo LIMIT 10");
    $stmt->execute();
    $usuarios_amostra = $stmt->fetchAll();
    
    echo "<p><strong>Total de usuários ativos:</strong> $total_usuarios</p>";
    echo "<h4>Amostra de usuários (primeiros 10):</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Nome Completo</th><th>Email</th></tr>";
    foreach ($usuarios_amostra as $usuario) {
        echo "<tr>";
        echo "<td>{$usuario['id']}</td>";
        echo "<td>" . htmlspecialchars($usuario['nome_completo']) . "</td>";
        echo "<td>" . htmlspecialchars($usuario['email']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['converter_para_id'])) {
        echo "<h3>4. Executando conversão...</h3>";
        
        // Passo 1: Criar colunas temporárias INT
        echo "<p>Passo 1: Criando colunas temporárias...</p>";
        try {
            $pdo_mci->exec("ALTER TABLE cad_registros ADD COLUMN funcionario_temp INT NULL");
            $pdo_mci->exec("ALTER TABLE cad_registros ADD COLUMN tecnico_responsavel_temp INT NULL");
            echo "<p>✅ Colunas temporárias criadas</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p style='color: orange;'>⚠️ Colunas temporárias já existem</p>";
            } else {
                echo "<p style='color: red;'>❌ Erro ao criar colunas temporárias: " . htmlspecialchars($e->getMessage()) . "</p>";
                exit;
            }
        }
        
        // Passo 2: Mapear nomes para IDs
        echo "<p>Passo 2: Mapeando nomes para IDs...</p>";
        
        $mapeamentos_funcionario = 0;
        $mapeamentos_tecnico = 0;
        $nao_encontrados_funcionario = [];
        $nao_encontrados_tecnico = [];
        
        // Buscar todos os usuários
        $stmt = $pdo->prepare("SELECT id, nome_completo FROM usuarios WHERE ativo = 1");
        $stmt->execute();
        $todos_usuarios = $stmt->fetchAll();
        
        // Criar array de mapeamento para busca mais rápida
        $mapa_usuarios = [];
        foreach ($todos_usuarios as $usuario) {
            $mapa_usuarios[trim($usuario['nome_completo'])] = $usuario['id'];
        }
        
        // Mapear funcionários
        foreach ($funcionarios as $func) {
            $nome = trim($func['funcionario']);
            if (isset($mapa_usuarios[$nome])) {
                $id_usuario = $mapa_usuarios[$nome];
                try {
                    $stmt = $pdo_mci->prepare("UPDATE cad_registros SET funcionario_temp = ? WHERE funcionario = ?");
                    $stmt->execute([$id_usuario, $nome]);
                    $affected = $stmt->rowCount();
                    echo "<p>✅ Funcionário '$nome' → ID $id_usuario ($affected registros)</p>";
                    $mapeamentos_funcionario += $affected;
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Erro ao mapear funcionário '$nome': " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            } else {
                $nao_encontrados_funcionario[] = $nome;
            }
        }
        
        // Mapear técnicos
        foreach ($tecnicos as $tec) {
            $nome = trim($tec['tecnico_responsavel']);
            if (isset($mapa_usuarios[$nome])) {
                $id_usuario = $mapa_usuarios[$nome];
                try {
                    $stmt = $pdo_mci->prepare("UPDATE cad_registros SET tecnico_responsavel_temp = ? WHERE tecnico_responsavel = ?");
                    $stmt->execute([$id_usuario, $nome]);
                    $affected = $stmt->rowCount();
                    echo "<p>✅ Técnico '$nome' → ID $id_usuario ($affected registros)</p>";
                    $mapeamentos_tecnico += $affected;
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Erro ao mapear técnico '$nome': " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            } else {
                $nao_encontrados_tecnico[] = $nome;
            }
        }
        
        echo "<p><strong>Resumo do mapeamento:</strong></p>";
        echo "<ul>";
        echo "<li>Funcionários mapeados: $mapeamentos_funcionario registros</li>";
        echo "<li>Técnicos mapeados: $mapeamentos_tecnico registros</li>";
        echo "</ul>";
        
        if (!empty($nao_encontrados_funcionario)) {
            echo "<p style='color: orange;'><strong>Funcionários não encontrados:</strong></p>";
            echo "<ul>";
            foreach ($nao_encontrados_funcionario as $nome) {
                echo "<li>" . htmlspecialchars($nome) . "</li>";
            }
            echo "</ul>";
        }
        
        if (!empty($nao_encontrados_tecnico)) {
            echo "<p style='color: orange;'><strong>Técnicos não encontrados:</strong></p>";
            echo "<ul>";
            foreach ($nao_encontrados_tecnico as $nome) {
                echo "<li>" . htmlspecialchars($nome) . "</li>";
            }
            echo "</ul>";
        }
        
        // Passo 3: Substituir colunas originais
        echo "<p>Passo 3: Substituindo colunas originais...</p>";
        
        try {
            // Remover colunas antigas
            $pdo_mci->exec("ALTER TABLE cad_registros DROP COLUMN funcionario");
            $pdo_mci->exec("ALTER TABLE cad_registros DROP COLUMN tecnico_responsavel");
            echo "<p>✅ Colunas antigas removidas</p>";
            
            // Renomear colunas temporárias
            $pdo_mci->exec("ALTER TABLE cad_registros CHANGE funcionario_temp funcionario INT NULL COMMENT 'ID do funcionário (referência para sicoob_access_control.usuarios)'");
            $pdo_mci->exec("ALTER TABLE cad_registros CHANGE tecnico_responsavel_temp tecnico_responsavel INT NULL COMMENT 'ID do técnico responsável (referência para sicoob_access_control.usuarios)'");
            echo "<p>✅ Colunas renomeadas</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao substituir colunas: " . htmlspecialchars($e->getMessage()) . "</p>";
            exit;
        }
        
        // Passo 4: Adicionar chaves estrangeiras
        echo "<p>Passo 4: Adicionando chaves estrangeiras...</p>";
        
        try {
            $pdo_mci->exec("
                ALTER TABLE cad_registros 
                ADD CONSTRAINT fk_registros_funcionario 
                FOREIGN KEY (funcionario) REFERENCES sicoob_access_control.usuarios(id) 
                ON UPDATE CASCADE ON DELETE SET NULL
            ");
            echo "<p>✅ Chave estrangeira para funcionario adicionada</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Erro ao adicionar FK funcionario: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        try {
            $pdo_mci->exec("
                ALTER TABLE cad_registros 
                ADD CONSTRAINT fk_registros_tecnico 
                FOREIGN KEY (tecnico_responsavel) REFERENCES sicoob_access_control.usuarios(id) 
                ON UPDATE CASCADE ON DELETE SET NULL
            ");
            echo "<p>✅ Chave estrangeira para tecnico_responsavel adicionada</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Erro ao adicionar FK tecnico_responsavel: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Passo 5: Verificar resultado final
        echo "<h3>5. Verificando resultado final...</h3>";
        
        $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field IN ('funcionario', 'tecnico_responsavel')");
        $stmt->execute();
        $colunas_finais = $stmt->fetchAll();
        
        echo "<h4>Estrutura final:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Padrão</th><th>Comentário</th></tr>";
        foreach ($colunas_finais as $coluna) {
            echo "<tr>";
            echo "<td><strong>{$coluna['Field']}</strong></td>";
            echo "<td><code>{$coluna['Type']}</code></td>";
            echo "<td>{$coluna['Null']}</td>";
            echo "<td>" . ($coluna['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($coluna['Comment'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Estatísticas finais
        $stmt = $pdo_mci->prepare("
            SELECT 
                COUNT(*) as total,
                COUNT(funcionario) as funcionarios_com_id,
                COUNT(tecnico_responsavel) as tecnicos_com_id
            FROM cad_registros
        ");
        $stmt->execute();
        $stats = $stmt->fetch();
        
        echo "<h4>Estatísticas finais:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Métrica</th><th>Valor</th><th>Percentual</th></tr>";
        echo "<tr><td>Total de registros</td><td>{$stats['total']}</td><td>100%</td></tr>";
        echo "<tr><td>Funcionários com ID</td><td>{$stats['funcionarios_com_id']}</td><td>" . round(($stats['funcionarios_com_id'] / $stats['total']) * 100, 1) . "%</td></tr>";
        echo "<tr><td>Técnicos com ID</td><td>{$stats['tecnicos_com_id']}</td><td>" . round(($stats['tecnicos_com_id'] / $stats['total']) * 100, 1) . "%</td></tr>";
        echo "</table>";
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724;'>🎉 Conversão Concluída com Sucesso!</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ Colunas convertidas para INT com referência a usuários</li>";
        echo "<li>✅ Chaves estrangeiras configuradas</li>";
        echo "<li>✅ Dados mapeados automaticamente</li>";
        echo "<li>✅ Sistema agora usa apenas IDs de usuários</li>";
        echo "</ul>";
        echo "<p style='color: #155724;'><strong>Importante:</strong> Agora você deve atualizar a interface para usar dropdowns de usuários.</p>";
        echo "</div>";
        
    } else {
        // Mostrar formulário de confirmação
        echo "<h3>4. Conversão Proposta</h3>";
        
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h4>⚠️ ATENÇÃO: Esta operação é irreversível!</h4>";
        echo "<p>Esta operação irá:</p>";
        echo "<ul>";
        echo "<li><strong>Remover</strong> as colunas de texto 'funcionario' e 'tecnico_responsavel'</li>";
        echo "<li><strong>Criar</strong> novas colunas INT com os mesmos nomes</li>";
        echo "<li><strong>Mapear</strong> nomes existentes para IDs de usuários</li>";
        echo "<li><strong>Configurar</strong> chaves estrangeiras</li>";
        echo "<li><strong>Perder</strong> dados de nomes que não correspondem a usuários</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>📋 Resultado esperado:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Antes</th><th>Depois</th></tr>";
        echo "<tr><td>funcionario VARCHAR (texto)</td><td>funcionario INT (ID do usuário)</td></tr>";
        echo "<tr><td>tecnico_responsavel VARCHAR (texto)</td><td>tecnico_responsavel INT (ID do usuário)</td></tr>";
        echo "</table>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<div style='margin: 20px 0;'>";
        echo "<label>";
        echo "<input type='checkbox' name='converter_para_id' required> ";
        echo "<strong>Eu entendo que esta operação é irreversível e confirmo a conversão</strong>";
        echo "</label>";
        echo "</div>";
        
        echo "<button type='submit' style='background-color: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold;'>🔄 CONVERTER PARA IDs</button>";
        echo " ";
        echo "<a href='gerenciar.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>❌ Cancelar</a>";
        echo "</form>";
    }
    
    echo "<hr>";
    echo "<p><a href='gerenciar.php'>Voltar ao gerenciamento</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a conversão</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
