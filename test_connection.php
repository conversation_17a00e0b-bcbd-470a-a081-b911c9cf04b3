<?php
/**
 * Script de teste de conexão para o Sistema MCI
 * Execute este arquivo para verificar se as configurações estão corretas
 */

echo "<h1>Teste de Conexão - Sistema MCI</h1>";
echo "<hr>";

// Teste 1: Verificar se os arquivos de configuração existem
echo "<h3>1. Verificando arquivos de configuração...</h3>";

$config_files = [
    'config/database.php',
    'config/config.php',
    'classes/Logger.php',
    'auth_check.php'
];

foreach ($config_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file - OK<br>";
    } else {
        echo "❌ $file - ARQUIVO NÃO ENCONTRADO<br>";
    }
}

// Teste 2: Verificar conexão com banco principal
echo "<h3>2. Testando conexão com banco principal...</h3>";
try {
    require_once '../config/database.php';
    echo "✅ Conexão com banco principal - OK<br>";
    echo "Host: " . DB_HOST . "<br>";
    echo "Banco: " . DB_NAME . "<br>";
} catch (Exception $e) {
    echo "❌ Erro na conexão com banco principal: " . $e->getMessage() . "<br>";
}

// Teste 3: Verificar conexão com banco MCI
echo "<h3>3. Testando conexão com banco MCI...</h3>";
try {
    require_once 'config/database.php';
    echo "✅ Conexão com banco MCI - OK<br>";
    echo "Host: " . MCI_DB_HOST . "<br>";
    echo "Banco: " . MCI_DB_NAME . "<br>";
} catch (Exception $e) {
    echo "❌ Erro na conexão com banco MCI: " . $e->getMessage() . "<br>";
}

// Teste 4: Verificar se as tabelas existem
echo "<h3>4. Verificando tabelas do banco MCI...</h3>";
try {
    $tables = ['cad_registros', 'cad_logs', 'cad_importacoes', 'cad_erros_importacao'];
    
    foreach ($tables as $table) {
        $stmt = $pdo_mci->prepare("SHOW TABLES LIKE '$table'");
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            echo "✅ Tabela $table - OK<br>";
        } else {
            echo "❌ Tabela $table - NÃO ENCONTRADA<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erro ao verificar tabelas: " . $e->getMessage() . "<br>";
}

// Teste 5: Verificar diretórios
echo "<h3>5. Verificando diretórios...</h3>";
$directories = ['logs', 'uploads'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ Diretório $dir - OK (gravável)<br>";
        } else {
            echo "⚠️ Diretório $dir - Existe mas não é gravável<br>";
        }
    } else {
        echo "❌ Diretório $dir - NÃO ENCONTRADO<br>";
    }
}

// Teste 6: Verificar dependências
echo "<h3>6. Verificando dependências...</h3>";

// PhpSpreadsheet
if (file_exists('../vendor/autoload.php')) {
    require_once '../vendor/autoload.php';
    if (class_exists('PhpOffice\PhpSpreadsheet\IOFactory')) {
        echo "✅ PhpSpreadsheet - OK<br>";
    } else {
        echo "❌ PhpSpreadsheet - NÃO ENCONTRADO<br>";
    }
} else {
    echo "❌ Composer autoload - NÃO ENCONTRADO<br>";
}

// Extensões PHP
$required_extensions = ['pdo', 'pdo_mysql', 'zip', 'xml'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ Extensão PHP $ext - OK<br>";
    } else {
        echo "❌ Extensão PHP $ext - NÃO CARREGADA<br>";
    }
}

// Teste 7: Teste do sistema de logs
echo "<h3>7. Testando sistema de logs...</h3>";
try {
    require_once 'classes/Logger.php';
    $logger = new MciLogger();
    $logger->logFile("Teste de conexão executado", "INFO");
    echo "✅ Sistema de logs - OK<br>";
} catch (Exception $e) {
    echo "❌ Erro no sistema de logs: " . $e->getMessage() . "<br>";
}

// Resumo
echo "<hr>";
echo "<h3>Resumo do Teste</h3>";
echo "<p>Se todos os itens acima estão marcados com ✅, o sistema está pronto para uso.</p>";
echo "<p>Caso haja itens com ❌, verifique a instalação e configuração.</p>";
echo "<p><strong>Data do teste:</strong> " . date('d/m/Y H:i:s') . "</p>";

// Links para as páginas do sistema
echo "<hr>";
echo "<h3>Links do Sistema</h3>";
echo "<ul>";
echo "<li><a href='index.php'>Página Principal</a></li>";
echo "<li><a href='importar.php'>Importar Planilha</a></li>";
echo "<li><a href='gerenciar.php'>Gerenciar Registros</a></li>";
echo "<li><a href='dashboard.php'>Dashboard</a></li>";
echo "</ul>";
?>
