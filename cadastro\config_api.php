<?php
/**
 * Configurações da API da Intranet
 * Arquivo centralizado para configurações da API
 */

// Configurações da API da Intranet
define('API_INTRANET_URL', 'https://intranet.sicoobcredilivre.com.br/api');
define('API_INTRANET_USER', 'UFL7GXZ14LU9NOR');
define('API_INTRANET_TOKEN', '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG');
define('API_INTRANET_TIMEOUT', 10);

// URL base para fotos dos usuários
define('API_INTRANET_FOTO_URL', 'https://intranet.sicoobcredilivre.com.br/sys/conteudo/usuarios/');

/**
 * Classe para gerenciar a API da Intranet
 */
class IntranetAPI {
    private $logger;
    
    public function __construct($logger = null) {
        $this->logger = $logger;
    }
    
    /**
     * Busca todos os usuários da intranet
     * @return array Lista de usuários
     */
    public function buscarUsuarios() {
        $apiFields = [
            'api_user' => API_INTRANET_USER,
            'api_token' => API_INTRANET_TOKEN,
            'api_module' => 'Usuarios',
            'api_action' => 'listarUsuarios'
        ];
        
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => API_INTRANET_URL,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($apiFields),
            CURLOPT_TIMEOUT => API_INTRANET_TIMEOUT,
            CURLOPT_USERAGENT => 'MCI Dashboard/1.0'
        ));
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        
        if ($error) {
            if ($this->logger) {
                $this->logger->logFile("Erro cURL na API da Intranet: " . $error, 'ERROR');
            }
            curl_close($curl);
            return [];
        }
        
        if ($httpCode !== 200) {
            if ($this->logger) {
                $this->logger->logFile("Erro HTTP na API da Intranet: HTTP $httpCode", 'ERROR');
            }
            curl_close($curl);
            return [];
        }
        
        curl_close($curl);
        
        $usuarios = json_decode($response, true);
        
        if (!is_array($usuarios)) {
            if ($this->logger) {
                $this->logger->logFile("Resposta inválida da API da Intranet: " . substr($response, 0, 200), 'ERROR');
            }
            return [];
        }
        
        // Processar usuários e adicionar URLs das fotos
        $usuariosProcessados = [];
        foreach ($usuarios as $usuario) {
            // Filtro apenas por status ativo (removido filtro de bloqueio para dashboards)
            if (isset($usuario['status']) && $usuario['status'] == 1) {
                
                // Adicionar URL da foto se existir
                if (!empty($usuario['foto'])) {
                    $usuario['foto_url'] = API_INTRANET_FOTO_URL . $usuario['foto'];
                }
                
                // Normalizar campos
                $usuario['nome_completo'] = $usuario['nome'] ?? '';
                $usuario['setor_nome'] = $usuario['nomeSetor'] ?? '';
                $usuario['funcao_nome'] = $usuario['nomeFuncao'] ?? '';
                
                $usuariosProcessados[] = $usuario;
            }
        }
        
        if ($this->logger) {
            $this->logger->logFile("API da Intranet: " . count($usuariosProcessados) . " usuários ativos carregados (filtro de bloqueio removido)", 'INFO');
        }
        
        return $usuariosProcessados;
    }
    
    /**
     * Busca usuário por email
     * @param string $email Email do usuário
     * @param array $usuarios Lista de usuários (opcional, para evitar nova consulta)
     * @return array|null Dados do usuário ou null se não encontrado
     */
    public function buscarUsuarioPorEmail($email, $usuarios = null) {
        if (empty($email)) {
            return null;
        }
        
        if ($usuarios === null) {
            $usuarios = $this->buscarUsuarios();
        }
        
        $emailBusca = strtolower(trim($email));
        
        foreach ($usuarios as $usuario) {
            if (isset($usuario['email']) && strtolower(trim($usuario['email'])) === $emailBusca) {
                return $usuario;
            }
        }
        
        return null;
    }
    
    /**
     * Cria um mapa de usuários indexado por email
     * @return array Mapa email => dados do usuário
     */
    public function criarMapaUsuariosPorEmail() {
        $usuarios = $this->buscarUsuarios();
        $mapa = [];
        
        foreach ($usuarios as $usuario) {
            if (!empty($usuario['email'])) {
                $emailKey = strtolower(trim($usuario['email']));
                $mapa[$emailKey] = $usuario;
            }
        }
        
        return $mapa;
    }
}

/**
 * Função helper para obter instância da API
 * @param object $logger Logger opcional
 * @return IntranetAPI
 */
function getIntranetAPI($logger = null) {
    return new IntranetAPI($logger);
}
?>
