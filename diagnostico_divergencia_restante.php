<?php
/**
 * <PERSON><PERSON>t de Diagnóstico - Divergência Restante entre Cadastro e Técnicos
 * Identifica os 16 registros que ainda estão causando diferença
 */

require_once 'config/config.php';
require_once 'classes/Logger.php';

$logger = new MciLogger();
$logger->log('Diagnóstico divergência restante', 'Análise dos 16 registros restantes');

echo "<h1>🔍 Diagnóstico da Divergência Restante</h1>";
echo "<p><strong>Objetivo:</strong> Identificar os 16 registros entre 1.137 (cadastro) e 1.121 (técnicos)</p>";
echo "<hr>";

try {
    // 1. ANÁLISE DETALHADA DO CADASTRO
    echo "<h2>📊 1. Análise Detalhada do Cadastro (1.137 registros)</h2>";
    
    $query_cadastro_detalhado = "
        SELECT
            r.id,
            r.funcionario,
            r.tecnico_responsavel,
            r.data_solicitacao_laudo,
            r.status,
            s.nome as status_nome,
            uf.nome_completo as funcionario_nome,
            ut.nome_completo as tecnico_nome
        FROM cad_registros r
        LEFT JOIN cad_status s ON r.status = s.id
        LEFT JOIN sicoob_access_control.usuarios uf ON r.funcionario = uf.id
        LEFT JOIN sicoob_access_control.usuarios ut ON r.tecnico_responsavel = ut.id
        WHERE r.funcionario IN (17, 18, 19, 20, 21, 22)
        AND r.data_solicitacao_laudo IS NOT NULL
        AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        ORDER BY r.id
    ";
    
    $stmt = $pdo_mci->prepare($query_cadastro_detalhado);
    $stmt->execute();
    $registros_cadastro = $stmt->fetchAll();
    
    echo "<p><strong>Total de registros do cadastro:</strong> " . count($registros_cadastro) . "</p>";
    
    // 2. ANÁLISE DETALHADA DOS TÉCNICOS
    echo "<h2>🌱 2. Análise Detalhada dos Técnicos (1.121 registros)</h2>";
    
    $query_tecnicos_detalhado = "
        SELECT
            r.id,
            r.funcionario,
            r.tecnico_responsavel,
            r.data_solicitacao_laudo,
            r.status,
            s.nome as status_nome,
            uf.nome_completo as funcionario_nome,
            ut.nome_completo as tecnico_nome
        FROM cad_registros r
        LEFT JOIN cad_status s ON r.status = s.id
        LEFT JOIN sicoob_access_control.usuarios uf ON r.funcionario = uf.id
        LEFT JOIN sicoob_access_control.usuarios ut ON r.tecnico_responsavel = ut.id
        WHERE r.tecnico_responsavel IS NOT NULL
        AND r.data_solicitacao_laudo IS NOT NULL
        AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        ORDER BY r.id
    ";
    
    $stmt = $pdo_mci->prepare($query_tecnicos_detalhado);
    $stmt->execute();
    $registros_tecnicos = $stmt->fetchAll();
    
    echo "<p><strong>Total de registros dos técnicos:</strong> " . count($registros_tecnicos) . "</p>";
    
    // 3. IDENTIFICAÇÃO DOS REGISTROS QUE ESTÃO NO CADASTRO MAS NÃO NOS TÉCNICOS
    echo "<h2>🔍 3. Registros no Cadastro mas NÃO nos Técnicos</h2>";
    
    $ids_cadastro = array_column($registros_cadastro, 'id');
    $ids_tecnicos = array_column($registros_tecnicos, 'id');
    
    $apenas_cadastro = array_diff($ids_cadastro, $ids_tecnicos);
    $apenas_tecnicos = array_diff($ids_tecnicos, $ids_cadastro);
    
    echo "<p><strong>Registros APENAS no cadastro:</strong> " . count($apenas_cadastro) . "</p>";
    echo "<p><strong>Registros APENAS nos técnicos:</strong> " . count($apenas_tecnicos) . "</p>";
    
    if (count($apenas_cadastro) > 0) {
        echo "<h3>📋 Detalhamento dos Registros Apenas no Cadastro:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Funcionário</th><th>Técnico</th><th>Status</th><th>Data Solicitação</th><th>Problema</th>";
        echo "</tr>";
        
        foreach ($apenas_cadastro as $id) {
            $registro = array_filter($registros_cadastro, function($r) use ($id) { return $r['id'] == $id; });
            $registro = reset($registro);
            
            $problema = "";
            if (empty($registro['tecnico_responsavel'])) {
                $problema = "❌ SEM TÉCNICO ATRIBUÍDO";
            } elseif (!in_array($registro['tecnico_responsavel'], array_column($registros_tecnicos, 'tecnico_responsavel'))) {
                $problema = "❌ TÉCNICO INATIVO OU INEXISTENTE";
            }
            
            echo "<tr>";
            echo "<td>{$registro['id']}</td>";
            echo "<td>{$registro['funcionario_nome']} (ID: {$registro['funcionario']})</td>";
            echo "<td>" . ($registro['tecnico_nome'] ?: 'N/A') . " (ID: " . ($registro['tecnico_responsavel'] ?: 'N/A') . ")</td>";
            echo "<td>{$registro['status_nome']}</td>";
            echo "<td>" . date('d/m/Y', strtotime($registro['data_solicitacao_laudo'])) . "</td>";
            echo "<td style='color: red;'>{$problema}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. ANÁLISE DE TÉCNICOS INATIVOS
    echo "<h2>🚫 4. Análise de Técnicos Inativos</h2>";
    
    $query_tecnicos_inativos = "
        SELECT DISTINCT
            r.tecnico_responsavel,
            u.nome_completo,
            u.ativo
        FROM cad_registros r
        LEFT JOIN sicoob_access_control.usuarios u ON r.tecnico_responsavel = u.id
        WHERE r.data_solicitacao_laudo IS NOT NULL
        AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        AND (u.ativo = 0 OR u.ativo IS NULL)
    ";
    
    $stmt = $pdo_mci->prepare($query_tecnicos_inativos);
    $stmt->execute();
    $tecnicos_inativos = $stmt->fetchAll();
    
    if (count($tecnicos_inativos) > 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>⚠️ Técnicos Inativos com Registros:</h3>";
        echo "<ul>";
        foreach ($tecnicos_inativos as $tecnico) {
            echo "<li><strong>ID {$tecnico['tecnico_responsavel']}:</strong> {$tecnico['nome_completo']} (Ativo: " . ($tecnico['ativo'] ? 'Sim' : 'Não') . ")</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<p>✅ Todos os técnicos estão ativos</p>";
    }
    
    // 5. ANÁLISE DE REGISTROS SEM TÉCNICO
    echo "<h2>❓ 5. Registros Sem Técnico Atribuído</h2>";
    
    $query_sem_tecnico = "
        SELECT COUNT(*) as total_sem_tecnico
        FROM cad_registros r
        WHERE r.funcionario IN (17, 18, 19, 20, 21, 22)
        AND r.data_solicitacao_laudo IS NOT NULL
        AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
        AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
        AND r.tecnico_responsavel IS NULL
    ";
    
    $stmt = $pdo_mci->prepare($query_sem_tecnico);
    $stmt->execute();
    $sem_tecnico = $stmt->fetch();
    
    echo "<p><strong>Registros sem técnico atribuído:</strong> " . $sem_tecnico['total_sem_tecnico'] . "</p>";
    
    if ($sem_tecnico['total_sem_tecnico'] > 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>❌ Problema Identificado:</h3>";
        echo "<p>Existem registros que foram processados pelo cadastro mas não foram atribuídos a técnicos agrícolas.</p>";
        echo "<p>Estes registros aparecem na contagem do cadastro mas não na dos técnicos.</p>";
        echo "</div>";
    }
    
    // 6. RESUMO E CONCLUSÃO
    echo "<h2>💡 6. Resumo da Divergência</h2>";
    
    $divergencia = count($registros_cadastro) - count($registros_tecnicos);
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📊 Análise Final:</h3>";
    echo "<ul>";
    echo "<li><strong>Total Cadastro:</strong> " . count($registros_cadastro) . " registros</li>";
    echo "<li><strong>Total Técnicos:</strong> " . count($registros_tecnicos) . " registros</li>";
    echo "<li><strong>Divergência:</strong> {$divergencia} registros</li>";
    echo "</ul>";
    
    if ($divergencia == 0) {
        echo "<p><strong>✅ PROBLEMA RESOLVIDO:</strong> As contagens estão idênticas!</p>";
    } else {
        echo "<p><strong>⚠️ PROBLEMA PERSISTE:</strong> Ainda há {$divergencia} registros de diferença.</p>";
        
        if (count($apenas_cadastro) > 0) {
            echo "<p><strong>🔍 Causa Principal:</strong> Registros do cadastro sem técnico atribuído</p>";
        }
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ Erro durante o diagnóstico:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    $logger->logFile("Erro no diagnóstico: " . $e->getMessage(), 'ERROR');
}

echo "<hr>";
echo "<p><strong>Data do diagnóstico:</strong> " . date('d/m/Y H:i:s') . "</p>";
echo "<p><a href='metas.php'>← Voltar para Metas</a></p>";
?>
