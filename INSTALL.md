# Instruções de Instalação - Sistema MCI

## Pré-requisitos

- PHP 7.4 ou superior
- MySQL 5.7 ou superior
- Servidor web (Apache/Nginx)
- Projeto principal já configurado e funcionando
- Composer (para dependências do projeto principal)

## Passo a Passo

### 1. Verificar Projeto Principal

Certifique-se de que o projeto principal está funcionando corretamente:
- Acesso ao banco `sicoob_access_control`
- Sistema de autenticação ativo
- Dependências do Composer instaladas

### 2. Criar Banco de Dados MCI

Execute o seguinte comando SQL:

```sql
CREATE DATABASE mci CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

Ou execute o script completo de instalação:

```bash
mysql -u [usuario] -p < sql/install.sql
```

### 3. Configurar Permissões

Certifique-se de que o usuário do banco de dados tem permissões no banco `mci`:

```sql
GRANT ALL PRIVILEGES ON mci.* TO 'sis'@'%';
FLUSH PRIVILEGES;
```

### 4. Verificar Estrutura de Arquivos

A estrutura deve estar assim:

```
mci/
├── config/
│   ├── database.php
│   └── config.php
├── classes/
│   └── Logger.php
├── ajax/
│   ├── get_registro.php
│   └── get_detalhes.php
├── sql/
│   ├── create_tables.sql
│   └── install.sql
├── logs/ (será criado automaticamente)
├── uploads/ (será criado automaticamente)
├── index.php
├── importar.php
├── gerenciar.php
├── dashboard.php
├── auth_check.php
├── test_connection.php
├── README.md
└── INSTALL.md
```

### 5. Testar Instalação

Execute o script de teste:

```
http://seu-servidor/mci/test_connection.php
```

Este script verificará:
- Conexões com banco de dados
- Existência das tabelas
- Permissões de diretórios
- Dependências necessárias

### 6. Configurar Permissões de Diretório

Certifique-se de que os diretórios têm permissões de escrita:

```bash
chmod 755 mci/logs
chmod 755 mci/uploads
```

### 7. Configurar Acesso no Projeto Principal

Para adicionar um link no menu principal, edite o arquivo `navbar.php` do projeto principal e adicione:

```php
<li class="nav-item">
    <a class="nav-link text-white" href="mci/index.php">
        <i class="fas fa-user-edit"></i> Atualizações Cadastrais
    </a>
</li>
```

### 8. Configurar Card no Dashboard Principal

Para adicionar um card no dashboard principal, execute:

```sql
INSERT INTO sicoob_access_control.card_buttons (nome, link, icone, cor, ativo, ordem) 
VALUES ('Atualizações Cadastrais', 'mci/index.php', 'fas fa-user-edit', '#003641', 1, 100);
```

## Verificações Pós-Instalação

### 1. Teste de Importação

1. Acesse `mci/importar.php`
2. Faça upload de uma planilha de teste
3. Verifique se os dados foram importados corretamente

### 2. Teste de Gerenciamento

1. Acesse `mci/gerenciar.php`
2. Verifique se os registros aparecem
3. Teste a edição de status
4. Teste os filtros

### 3. Teste do Dashboard

1. Acesse `mci/dashboard.php`
2. Verifique se as métricas aparecem
3. Teste os gráficos

### 4. Verificar Logs

1. Verifique se os logs estão sendo gerados em `mci/logs/`
2. Verifique se os logs aparecem no sistema principal

## Solução de Problemas

### Erro de Conexão com Banco

- Verifique as credenciais em `config/database.php`
- Certifique-se de que o banco `mci` existe
- Verifique as permissões do usuário

### Erro de Upload

- Verifique permissões do diretório `uploads/`
- Verifique configurações PHP: `upload_max_filesize`, `post_max_size`

### Erro de Dependências

- Execute `composer install` no projeto principal
- Verifique se o PhpSpreadsheet está instalado

### Erro de Autenticação

- Certifique-se de que está logado no sistema principal
- Verifique se o arquivo `auth_check.php` está correto

## Configurações Avançadas

### Personalizar Tamanho Máximo de Upload

Edite `config/config.php`:

```php
define('MCI_MAX_FILE_SIZE', 20 * 1024 * 1024); // 20MB
```

### Configurar Log Level

Edite `classes/Logger.php` para ajustar o nível de log.

### Adicionar Validações Customizadas

Edite `importar.php` para adicionar validações específicas dos dados.

## Backup

Recomenda-se fazer backup regular do banco `mci`:

```bash
mysqldump -u [usuario] -p mci > backup_mci_$(date +%Y%m%d).sql
```

## Suporte

Para suporte técnico:
1. Verifique os logs em `mci/logs/`
2. Execute `test_connection.php` para diagnóstico
3. Consulte a documentação no `README.md`
