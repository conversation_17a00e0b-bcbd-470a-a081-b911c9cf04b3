# 🌱 Setup Dashboard Técnicos Agrícolas

## 📋 Status Atual

O dashboard dos técnicos agrícolas foi criado e está funcionando em modo demonstração. Para usar dados reais, é necessário configurar os técnicos responsáveis no sistema.

## 🔍 Diagnóstico

### **Arquivos Disponíveis:**
- ✅ `dashboard.php` - Dashboard principal (aguarda dados reais)
- ✅ `dashboard_demo.php` - Dashboard com dados de demonstração
- ✅ `test_connection.php` - Teste de conexão
- ✅ `debug_tecnicos.php` - Debug detalhado dos dados
- ✅ `test_layout.html` - Teste visual do layout

### **Verificações Necessárias:**
1. **Coluna tecnico_responsavel**: Verificar se existe e está preenchida
2. **Tipo de dados**: Verificar se são IDs numéricos ou nomes em texto
3. **Relacionamento**: Verificar se os IDs correspondem a usuários válidos

## 🛠️ Como Configurar

### **Passo 1: Verificar Estrutura**
Execute o debug para verificar o estado atual:
```
/mci/tecagricola/debug_tecnicos.php
```

### **Passo 2: Configurar Técnicos Responsáveis**

#### **Opção A: Se a coluna não existe**
```sql
ALTER TABLE cad_registros 
ADD COLUMN tecnico_responsavel INT NULL 
COMMENT 'ID do técnico responsável';
```

#### **Opção B: Se a coluna existe mas está vazia**
Atualizar registros manualmente:
```sql
UPDATE cad_registros 
SET tecnico_responsavel = [ID_DO_TECNICO] 
WHERE [CONDIÇÃO_PARA_IDENTIFICAR_REGISTROS];
```

#### **Opção C: Se a coluna tem nomes em texto**
Converter nomes para IDs usando o script de mapeamento:
```
/mci/mapear_usuarios_gradual.php
```

### **Passo 3: Identificar Técnicos Agrícolas**

#### **Buscar usuários do setor técnico:**
```sql
SELECT u.id, u.nome_completo, u.email, s.nome as setor
FROM sicoob_access_control.usuarios u
LEFT JOIN sicoob_access_control.usuario_setor us ON u.id = us.usuario_id
LEFT JOIN sicoob_access_control.setores s ON us.setor_id = s.id
WHERE u.ativo = 1 
AND (s.nome LIKE '%técnico%' OR s.nome LIKE '%agrícola%')
ORDER BY u.nome_completo;
```

#### **Atribuir registros aos técnicos:**
```sql
-- Exemplo: Atribuir registros por PA ou região
UPDATE cad_registros 
SET tecnico_responsavel = 123 
WHERE pa IN ('001', '002', '003');

UPDATE cad_registros 
SET tecnico_responsavel = 124 
WHERE pa IN ('004', '005', '006');
```

### **Passo 4: Configurar Status**

Verificar se existem registros com status 'Atualizado':
```sql
SELECT s.nome, COUNT(r.id) as total
FROM cad_registros r
LEFT JOIN cad_status s ON r.status = s.id
GROUP BY s.nome
ORDER BY total DESC;
```

Se necessário, criar status 'Atualizado':
```sql
INSERT INTO cad_status (nome, descricao, cor) 
VALUES ('Atualizado', 'Registro atualizado pelo técnico', '#28a745');
```

## 📊 Exemplo de Configuração

### **Cenário: 6 Técnicos Agrícolas**

```sql
-- 1. Identificar técnicos (exemplo)
-- João Silva (ID: 101), Maria Santos (ID: 102), etc.

-- 2. Distribuir registros por PA
UPDATE cad_registros SET tecnico_responsavel = 101 WHERE pa BETWEEN '001' AND '010';
UPDATE cad_registros SET tecnico_responsavel = 102 WHERE pa BETWEEN '011' AND '020';
UPDATE cad_registros SET tecnico_responsavel = 103 WHERE pa BETWEEN '021' AND '030';
UPDATE cad_registros SET tecnico_responsavel = 104 WHERE pa BETWEEN '031' AND '040';
UPDATE cad_registros SET tecnico_responsavel = 105 WHERE pa BETWEEN '041' AND '050';
UPDATE cad_registros SET tecnico_responsavel = 106 WHERE pa >= '051';

-- 3. Marcar alguns como atualizados (exemplo)
UPDATE cad_registros 
SET status = (SELECT id FROM cad_status WHERE nome = 'Atualizado')
WHERE tecnico_responsavel IS NOT NULL 
AND RAND() < 0.7; -- 70% dos registros como exemplo
```

## 🎯 Validação

### **Verificar se está funcionando:**
```sql
SELECT 
    u.nome_completo,
    COUNT(r.id) as total_registros,
    COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados,
    ROUND(COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id) * 100, 1) as percentual
FROM sicoob_access_control.usuarios u
INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
LEFT JOIN cad_status s ON r.status = s.id
WHERE u.ativo = 1
GROUP BY u.id, u.nome_completo
ORDER BY total_registros DESC;
```

### **Resultado esperado:**
- Cada técnico deve ter registros atribuídos
- Alguns registros devem ter status 'Atualizado'
- O dashboard deve exibir os técnicos com suas métricas

## 🚀 Após Configuração

1. **Testar dashboard real**: `/mci/tecagricola/dashboard.php`
2. **Verificar métricas**: Cada técnico deve aparecer com suas estatísticas
3. **Validar cores**: Cada técnico deve ter sua cor específica
4. **Testar responsividade**: Verificar em diferentes resoluções

## 🔧 Troubleshooting

### **Dashboard vazio:**
- Verificar se `tecnico_responsavel` está preenchido
- Verificar se usuários estão ativos
- Verificar se existem registros com status válido

### **Técnicos não aparecem:**
- Verificar JOIN entre tabelas
- Verificar se IDs correspondem a usuários válidos
- Verificar se há registros atribuídos

### **Métricas zeradas:**
- Verificar se status 'Atualizado' existe
- Verificar se registros têm esse status
- Verificar cálculo de percentuais

## 📞 Suporte

Para dúvidas ou problemas:
1. Execute `/mci/tecagricola/debug_tecnicos.php`
2. Verifique logs em `/mci/logs/`
3. Consulte documentação em `/mci/tecagricola/README.md`

---

**Dashboard MCI - Técnicos Agrícolas**  
*Configuração e troubleshooting* 🌱
