<?php
// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

require_once '../auth_check.php';

// Buscar dados dos técnicos agrícolas
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

// Calcular métricas gerais
$total_meta = array_sum(array_column($tecnicos_dados, 'meta_total'));
$total_atualizados = array_sum(array_column($tecnicos_dados, 'atualizados_total'));
$progresso_geral = $total_meta > 0 ? round(($total_atualizados / $total_meta) * 100, 1) : 0;
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard MCI - Técnicos Agrícolas (SIMPLES)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #003641 0%, #00AE9D 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .overview {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border-top: 3px solid #00AE9D;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #003641, #00AE9D);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
            margin: 0 auto 15px;
        }

        .nome {
            font-size: 18px;
            font-weight: bold;
            color: #003641;
            margin-bottom: 15px;
        }

        .meta {
            font-size: 24px;
            font-weight: bold;
            color: #00AE9D;
            margin-bottom: 5px;
        }

        .progresso {
            font-size: 16px;
            color: #003641;
        }

        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-seedling"></i> MCI - TÉCNICOS AGRÍCOLAS (SIMPLES)</h1>
            <p>Dashboard Simplificado para Debug</p>
        </div>

        <!-- Debug Info -->
        <div class="debug">
            <strong>DEBUG INFO:</strong><br>
            Timestamp: <?php echo date('Y-m-d H:i:s'); ?><br>
            Total de técnicos no array: <?php echo count($tecnicos_dados); ?><br>
            IDs encontrados: <?php echo implode(', ', array_column($tecnicos_dados, 'id')); ?><br>
        </div>

        <!-- Overview -->
        <div class="overview">
            <h3><i class="fas fa-chart-line"></i> Meta Total da Equipe</h3>
            <div style="font-size: 32px; font-weight: bold; color: #00AE9D; margin: 10px 0;">
                <?php echo number_format($total_meta); ?>
            </div>
            <div style="font-size: 16px; color: #6c757d;">
                <?php echo $total_atualizados; ?> concluídos (<?php echo $progresso_geral; ?>%)
            </div>
        </div>

        <!-- Grid de Técnicos -->
        <div class="grid">
            <?php 
            $contador = 1;
            foreach ($tecnicos_dados as $tecnico): 
            ?>
            <div class="card">
                <div class="debug">
                    Card <?php echo $contador; ?>: ID <?php echo $tecnico['id']; ?>
                </div>
                
                <div class="avatar">
                    <?php echo strtoupper(substr($tecnico['nome'], 0, 1)); ?>
                </div>

                <div class="nome">
                    <?php echo htmlspecialchars($tecnico['nome']); ?>
                </div>

                <div class="meta">
                    <?php echo $tecnico['atualizados_total']; ?>/<?php echo $tecnico['meta_total']; ?>
                </div>

                <div class="progresso">
                    <?php echo $tecnico['progresso_total']; ?>%
                </div>
            </div>
            <?php 
            $contador++;
            endforeach; 
            ?>
        </div>

        <!-- Debug Final -->
        <div class="debug" style="margin-top: 20px;">
            <strong>LOOP EXECUTADO:</strong><br>
            Total de iterações: <?php echo $contador - 1; ?><br>
            Último contador: <?php echo $contador; ?><br>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <a href="dashboard.php" class="btn btn-primary">🚀 Dashboard Completo</a>
            <a href="debug_loop.php" class="btn btn-secondary">🔧 Debug Loop</a>
        </div>
    </div>

    <script>
        // Debug JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            console.log('=== DEBUG DASHBOARD SIMPLES ===');
            console.log('Total de cards no DOM:', cards.length);
            
            cards.forEach((card, index) => {
                const nome = card.querySelector('.nome')?.textContent || 'Nome não encontrado';
                const debug = card.querySelector('.debug')?.textContent || 'Debug não encontrado';
                console.log(`Card ${index + 1}: ${nome} (${debug})`);
            });
            
            // Verificar se Felipe e Sidnei estão presentes
            const nomes = Array.from(cards).map(card => card.querySelector('.nome')?.textContent || '');
            const felipe = nomes.find(nome => nome.includes('Felipe'));
            const sidnei = nomes.find(nome => nome.includes('Sidnei'));
            const maycon = nomes.filter(nome => nome.includes('Maycon'));
            
            console.log('Felipe encontrado:', felipe || 'NÃO');
            console.log('Sidnei encontrado:', sidnei || 'NÃO');
            console.log('Maycon encontrado:', maycon.length, 'vez(es)');
            
            if (maycon.length > 1) {
                console.error('PROBLEMA: Maycon aparece', maycon.length, 'vezes!');
            }
            
            if (!felipe) {
                console.error('PROBLEMA: Felipe não encontrado!');
            }
        });
    </script>
</body>
</html>
