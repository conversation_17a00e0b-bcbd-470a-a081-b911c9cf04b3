<?php
require_once __DIR__ . '/../config/config.php';

class MciLogger {
    private $logFile;
    private $pdo;
    private $pdo_mci;

    public function __construct() {
        global $pdo, $pdo_mci;
        $this->pdo = $pdo;
        $this->pdo_mci = $pdo_mci;
        $this->logFile = MCI_LOG_PATH . 'mci_' . date('Y-m-d') . '.log';
    }

    /**
     * Registra log no arquivo
     */
    public function logFile($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [$level] $message\n";
        
        try {
            file_put_contents($this->logFile, $logMessage, FILE_APPEND);
        } catch (Exception $e) {
            // Silently fail if we can't write to the log file
        }
    }

    /**
     * Registra log no banco de dados principal (tabela logs)
     */
    public function logDatabase($acao, $detalhes) {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }

        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO logs (usuario_id, acao, detalhes, data_hora) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([
                $_SESSION['user_id'],
                $acao,
                $detalhes
            ]);
            return true;
        } catch (Exception $e) {
            $this->logFile("Erro ao registrar log no banco principal: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }

    /**
     * Registra log no banco MCI (tabela cad_logs)
     */
    public function logMci($acao, $detalhes, $registro_id = null) {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }

        try {
            $stmt = $this->pdo_mci->prepare("
                INSERT INTO cad_logs (usuario_id, acao, detalhes, registro_id, data_hora) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $_SESSION['user_id'],
                $acao,
                $detalhes,
                $registro_id
            ]);
            return true;
        } catch (Exception $e) {
            $this->logFile("Erro ao registrar log no banco MCI: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }

    /**
     * Registra log completo (arquivo + banco principal + banco MCI)
     */
    public function log($acao, $detalhes, $registro_id = null) {
        $this->logFile("$acao: $detalhes");
        $this->logDatabase($acao, $detalhes);
        $this->logMci($acao, $detalhes, $registro_id);
    }
}
?>
