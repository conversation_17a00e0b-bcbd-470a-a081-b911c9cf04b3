<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

$logger = new MciLogger();
$logger->log('Acesso ao dashboard MCI', 'Usuário acessou o dashboard de métricas');

// Buscar estatísticas gerais
try {
    // Total de registros
    $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_registros");
    $stmt->execute();
    $total_registros = $stmt->fetchColumn();

    // Registros por status
    $stmt = $pdo_mci->prepare("
        SELECT status, COUNT(*) as quantidade 
        FROM cad_registros 
        GROUP BY status
        ORDER BY quantidade DESC
    ");
    $stmt->execute();
    $registros_por_status = $stmt->fetchAll();

    // Registros por PA (top 10)
    $stmt = $pdo_mci->prepare("
        SELECT pa, COUNT(*) as quantidade 
        FROM cad_registros 
        GROUP BY pa 
        ORDER BY quantidade DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $registros_por_pa = $stmt->fetchAll();

    // Registros por funcionário (top 10)
    $stmt = $pdo_mci->prepare("
        SELECT funcionario, COUNT(*) as quantidade 
        FROM cad_registros 
        WHERE funcionario IS NOT NULL AND funcionario != ''
        GROUP BY funcionario 
        ORDER BY quantidade DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $registros_por_funcionario = $stmt->fetchAll();

    // Registros por técnico responsável (top 10)
    $stmt = $pdo_mci->prepare("
        SELECT tecnico_responsavel, COUNT(*) as quantidade 
        FROM cad_registros 
        WHERE tecnico_responsavel IS NOT NULL AND tecnico_responsavel != ''
        GROUP BY tecnico_responsavel 
        ORDER BY quantidade DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $registros_por_tecnico = $stmt->fetchAll();

    // Evolução por mês (últimos 6 meses)
    $stmt = $pdo_mci->prepare("
        SELECT 
            DATE_FORMAT(data_cadastro, '%Y-%m') as mes,
            COUNT(*) as quantidade
        FROM cad_registros 
        WHERE data_cadastro >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(data_cadastro, '%Y-%m')
        ORDER BY mes
    ");
    $stmt->execute();
    $evolucao_mensal = $stmt->fetchAll();

    // Estatísticas de importações
    $stmt = $pdo_mci->prepare("
        SELECT 
            COUNT(*) as total_importacoes,
            SUM(registros_importados) as total_importados,
            SUM(registros_erro) as total_erros,
            AVG(registros_importados) as media_por_importacao
        FROM cad_importacoes
    ");
    $stmt->execute();
    $stats_importacoes = $stmt->fetch();

} catch (Exception $e) {
    $logger->logFile("Erro ao buscar estatísticas do dashboard: " . $e->getMessage(), 'ERROR');
    $total_registros = 0;
    $registros_por_status = [];
    $registros_por_pa = [];
    $registros_por_funcionario = [];
    $registros_por_tecnico = [];
    $evolucao_mensal = [];
    $stats_importacoes = ['total_importacoes' => 0, 'total_importados' => 0, 'total_erros' => 0, 'media_por_importacao' => 0];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo MCI_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-roxo: #49479D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body { background-color: #f8f9fa; }
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); transition: transform 0.2s ease; }
        .card:hover { transform: translateY(-2px); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; border-radius: 12px 12px 0 0 !important; }
        .btn-primary { background-color: var(--sicoob-verde-escuro); border-color: var(--sicoob-verde-escuro); }
        .btn-primary:hover { background-color: var(--sicoob-turquesa); border-color: var(--sicoob-turquesa); }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        
        .stats-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--sicoob-verde-escuro);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .metric-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <img src="../assets/images/logo1.png" alt="Sicoob" height="40">
                <span class="ms-2">MCI - Dashboard</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-primary">
                    <i class="fas fa-chart-bar"></i> 
                    Dashboard - Atualizações Cadastrais
                </h1>
                <p class="text-muted">Métricas e relatórios detalhados dos registros</p>
            </div>
        </div>

        <!-- Estatísticas Principais -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($total_registros); ?></div>
                        <div class="text-muted">Total de Registros</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($stats_importacoes['total_importacoes']); ?></div>
                        <div class="text-muted">Importações Realizadas</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($stats_importacoes['total_importados']); ?></div>
                        <div class="text-muted">Registros Importados</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number text-danger"><?php echo number_format($stats_importacoes['total_erros']); ?></div>
                        <div class="text-muted">Erros de Importação</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="row mb-4">
            <!-- Status dos Registros -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie"></i> Distribuição por Status
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Evolução Mensal -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line"></i> Evolução Mensal
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="evolucaoChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rankings -->
        <div class="row">
            <!-- Top PAs -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-building"></i> Top 10 PAs
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($registros_por_pa as $index => $item): ?>
                        <div class="metric-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong><?php echo htmlspecialchars($item['pa']); ?></strong>
                            </div>
                            <div>
                                <span class="badge bg-primary"><?php echo number_format($item['quantidade']); ?></span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Top Funcionários -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-users"></i> Top 10 Funcionários
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($registros_por_funcionario as $index => $item): ?>
                        <div class="metric-item d-flex justify-content-between align-items-center">
                            <div>
                                <small><?php echo htmlspecialchars($item['funcionario']); ?></small>
                            </div>
                            <div>
                                <span class="badge bg-success"><?php echo number_format($item['quantidade']); ?></span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Top Técnicos -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-user-tie"></i> Top 10 Técnicos
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($registros_por_tecnico as $index => $item): ?>
                        <div class="metric-item d-flex justify-content-between align-items-center">
                            <div>
                                <small><?php echo htmlspecialchars($item['tecnico_responsavel']); ?></small>
                            </div>
                            <div>
                                <span class="badge bg-info"><?php echo number_format($item['quantidade']); ?></span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Status Detalhado -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tasks"></i> Status Detalhado
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($registros_por_status as $status): ?>
                        <div class="metric-item">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small><?php echo ucfirst(str_replace('_', ' ', $status['status'])); ?></small>
                                <span class="badge bg-secondary"><?php echo number_format($status['quantidade']); ?></span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar" style="width: <?php echo ($status['quantidade'] / $total_registros) * 100; ?>%"></div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dados para os gráficos
        const statusData = {
            labels: [<?php echo implode(',', array_map(function($s) { return '"' . ucfirst(str_replace('_', ' ', $s['status'])) . '"'; }, $registros_por_status)); ?>],
            datasets: [{
                data: [<?php echo implode(',', array_column($registros_por_status, 'quantidade')); ?>],
                backgroundColor: [
                    '#ffc107', // Pendente - Amarelo
                    '#17a2b8', // Em Andamento - Azul
                    '#28a745', // Concluído - Verde
                    '#dc3545'  // Cancelado - Vermelho
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        const evolucaoData = {
            labels: [<?php echo implode(',', array_map(function($e) { return '"' . date('M/Y', strtotime($e['mes'] . '-01')) . '"'; }, $evolucao_mensal)); ?>],
            datasets: [{
                label: 'Registros Cadastrados',
                data: [<?php echo implode(',', array_column($evolucao_mensal, 'quantidade')); ?>],
                borderColor: '#003641',
                backgroundColor: 'rgba(0, 54, 65, 0.1)',
                tension: 0.4,
                fill: true
            }]
        };

        // Configuração dos gráficos
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        };

        // Gráfico de Status
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: statusData,
            options: {
                ...chartOptions,
                plugins: {
                    ...chartOptions.plugins,
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Gráfico de Evolução
        const evolucaoCtx = document.getElementById('evolucaoChart').getContext('2d');
        new Chart(evolucaoCtx, {
            type: 'line',
            data: evolucaoData,
            options: {
                ...chartOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // Atualizar página a cada 5 minutos
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
