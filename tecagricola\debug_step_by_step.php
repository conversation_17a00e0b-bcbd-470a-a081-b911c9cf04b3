<?php
require_once '../auth_check.php';
require_once '../classes/Logger.php';

$logger = new MciLogger();

echo "<h2>🔍 Debug Step-by-Step - Processamento Completo</h2>";

echo "<h3>📊 PASSO 1: Buscar Técnicos no Banco MCI</h3>";

$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

echo "<p>✅ <strong>Encontrados " . count($tecnicos_dados) . " técnicos no banco MCI</strong></p>";

// Mostrar dados do Maycon especificamente
$maycon_banco = null;
foreach ($tecnicos_dados as $tecnico) {
    if ($tecnico['id'] == 58) {
        $maycon_banco = $tecnico;
        break;
    }
}

if ($maycon_banco) {
    echo "<div style='border: 2px solid #28a745; padding: 15px; background: #d4edda;'>";
    echo "<h4>✅ Maycon encontrado no banco MCI:</h4>";
    echo "<p><strong>ID:</strong> " . $maycon_banco['id'] . "</p>";
    echo "<p><strong>Nome:</strong> " . htmlspecialchars($maycon_banco['nome']) . "</p>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($maycon_banco['email']) . "</p>";
    echo "<p><strong>Meta:</strong> " . $maycon_banco['meta_total'] . "</p>";
    echo "</div>";
} else {
    echo "<div style='border: 2px solid #dc3545; padding: 15px; background: #f8d7da;'>";
    echo "<h4>❌ Maycon NÃO encontrado no banco MCI!</h4>";
    echo "</div>";
}

echo "<h3>🌐 PASSO 2: Carregar API da Intranet</h3>";

try {
    require_once '../cadastro/config_api.php';
    $intranetAPI = getIntranetAPI($logger);
    
    echo "<p>✅ <strong>API inicializada</strong></p>";
    
    // Buscar usuários com debug
    echo "<h4>🔍 Carregando usuários da API...</h4>";
    $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
    
    echo "<p>✅ <strong>Cache criado com " . count($usuarios_intranet) . " usuários</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Erro na API:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

echo "<h3>🔄 PASSO 3: Processamento dos Técnicos</h3>";

// Simular exatamente o processamento do dashboard
$tecnicos_dados_temp = $tecnicos_dados;
$processamento_log = [];

foreach ($tecnicos_dados_temp as $index => &$tecnico) {
    $log_tecnico = [
        'id' => $tecnico['id'],
        'nome' => $tecnico['nome'],
        'email_original' => $tecnico['email'],
        'email_normalizado' => null,
        'encontrado_api' => false,
        'foto_url' => null,
        'motivo' => ''
    ];
    
    // Inicializar campos
    $tecnico['foto_url'] = null;
    $tecnico['setor'] = null;
    $tecnico['funcao'] = null;
    
    if (!empty($tecnico['email'])) {
        $email_key = strtolower(trim($tecnico['email']));
        $log_tecnico['email_normalizado'] = $email_key;
        
        $usuario_intranet = $usuarios_intranet[$email_key] ?? null;
        
        if ($usuario_intranet) {
            $log_tecnico['encontrado_api'] = true;
            $tecnico['foto_url'] = $usuario_intranet['foto_url'] ?? null;
            $tecnico['setor'] = $usuario_intranet['setor_nome'] ?? null;
            $tecnico['funcao'] = $usuario_intranet['funcao_nome'] ?? null;
            $log_tecnico['foto_url'] = $tecnico['foto_url'];
            $log_tecnico['motivo'] = 'Encontrado na API e dados adicionados';
        } else {
            $log_tecnico['motivo'] = 'Email não encontrado na API';
        }
    } else {
        $log_tecnico['motivo'] = 'Email vazio no banco';
    }
    
    $processamento_log[] = $log_tecnico;
    $tecnicos_dados[$index] = $tecnico;
}

// Mostrar log do processamento
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Nome</th><th>Email Original</th><th>Email Normalizado</th><th>Encontrado API</th><th>Foto URL</th><th>Motivo</th></tr>";

foreach ($processamento_log as $log) {
    $cor = '';
    if ($log['id'] == 58) {
        $cor = 'style="background-color: #fff3cd; font-weight: bold;"';
    } elseif ($log['encontrado_api']) {
        $cor = 'style="background-color: #d4edda;"';
    } else {
        $cor = 'style="background-color: #f8d7da;"';
    }
    
    echo "<tr $cor>";
    echo "<td>" . $log['id'] . "</td>";
    echo "<td>" . htmlspecialchars($log['nome']) . "</td>";
    echo "<td>" . htmlspecialchars($log['email_original']) . "</td>";
    echo "<td>" . htmlspecialchars($log['email_normalizado'] ?? 'N/A') . "</td>";
    echo "<td>" . ($log['encontrado_api'] ? '✅ Sim' : '❌ Não') . "</td>";
    echo "<td>" . htmlspecialchars($log['foto_url'] ?? 'NULL') . "</td>";
    echo "<td>" . htmlspecialchars($log['motivo']) . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>🎯 PASSO 4: Análise Específica do Maycon</h3>";

$maycon_log = null;
foreach ($processamento_log as $log) {
    if ($log['id'] == 58) {
        $maycon_log = $log;
        break;
    }
}

if ($maycon_log) {
    echo "<div style='border: 2px solid #ffc107; padding: 15px; background: #fff3cd;'>";
    echo "<h4>🎯 Resultado do Processamento - Maycon:</h4>";
    echo "<p><strong>Email normalizado:</strong> " . htmlspecialchars($maycon_log['email_normalizado']) . "</p>";
    echo "<p><strong>Encontrado na API:</strong> " . ($maycon_log['encontrado_api'] ? '✅ SIM' : '❌ NÃO') . "</p>";
    echo "<p><strong>Foto URL:</strong> " . htmlspecialchars($maycon_log['foto_url'] ?? 'NULL') . "</p>";
    echo "<p><strong>Motivo:</strong> " . htmlspecialchars($maycon_log['motivo']) . "</p>";
    
    if (!$maycon_log['encontrado_api']) {
        echo "<h5>🔍 Investigação Adicional:</h5>";
        
        // Verificar se o email existe na API
        $email_maycon = $maycon_log['email_normalizado'];
        
        echo "<p><strong>Verificando se email existe na API...</strong></p>";
        
        if (isset($usuarios_intranet[$email_maycon])) {
            echo "<p style='color: red;'>🚨 <strong>ERRO CRÍTICO:</strong> Email existe na API mas não foi encontrado no processamento!</p>";
        } else {
            echo "<p><strong>Confirmado:</strong> Email realmente não existe na API</p>";
            
            // Buscar emails similares
            $similares = [];
            foreach ($usuarios_intranet as $api_email => $api_user) {
                if (stripos($api_email, 'maycon') !== false) {
                    $similares[] = $api_email;
                }
            }
            
            if (!empty($similares)) {
                echo "<p><strong>Emails similares encontrados:</strong></p>";
                echo "<ul>";
                foreach ($similares as $email_similar) {
                    echo "<li>" . htmlspecialchars($email_similar) . "</li>";
                }
                echo "</ul>";
            }
        }
    }
    
    echo "</div>";
}

echo "<h3>📋 PASSO 5: Verificação Final</h3>";

echo "<div style='border: 2px solid #17a2b8; padding: 15px; background: #d1ecf1;'>";
echo "<h4>📊 Resumo:</h4>";
echo "<p><strong>Total de técnicos processados:</strong> " . count($processamento_log) . "</p>";

$com_foto = 0;
$sem_foto = 0;
foreach ($processamento_log as $log) {
    if ($log['foto_url']) {
        $com_foto++;
    } else {
        $sem_foto++;
    }
}

echo "<p><strong>Com foto:</strong> $com_foto</p>";
echo "<p><strong>Sem foto:</strong> $sem_foto</p>";

if ($maycon_log && !$maycon_log['encontrado_api']) {
    echo "<p style='color: red; font-weight: bold;'>❌ <strong>Maycon não tem foto porque seu email não foi encontrado na API da Intranet</strong></p>";
} elseif ($maycon_log && $maycon_log['encontrado_api'] && !$maycon_log['foto_url']) {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ <strong>Maycon foi encontrado na API mas não tem foto cadastrada</strong></p>";
} elseif ($maycon_log && $maycon_log['foto_url']) {
    echo "<p style='color: green; font-weight: bold;'>✅ <strong>Maycon deveria ter foto!</strong></p>";
}

echo "</div>";

echo "<hr>";
echo "<p><a href='debug_filtros_api.php'>🔍 Debug Filtros API</a> | <a href='dashboard.php'>🚀 Dashboard</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
