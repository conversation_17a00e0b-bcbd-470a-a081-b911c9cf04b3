<?php
require_once '../auth_check.php';

header('Content-Type: application/json');

try {
    // Verificar se é uma requisição POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }
    
    // Obter dados JSON da requisição
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['funcionario_id']) || empty($input['funcionario_id'])) {
        throw new Exception('ID do funcionário é obrigatório');
    }
    
    $funcionario_id = intval($input['funcionario_id']);
    
    // Buscar total de registros do funcionário
    $stmt_total = $pdo_mci->prepare("
        SELECT COUNT(*) as total
        FROM cad_registros cr
        WHERE cr.funcionario = ?
    ");
    $stmt_total->execute([$funcionario_id]);
    $total_registros = $stmt_total->fetchColumn();
    
    // Buscar registros disponíveis (apenas status 'Pendente')
    $stmt_disponiveis = $pdo_mci->prepare("
        SELECT COUNT(*) as disponiveis
        FROM cad_registros cr
        INNER JOIN cad_status cs ON cr.status = cs.id
        WHERE cr.funcionario = ?
        AND cs.nome = 'Pendente'
    ");
    $stmt_disponiveis->execute([$funcionario_id]);
    $registros_disponiveis = $stmt_disponiveis->fetchColumn();
    
    // Buscar distribuição por status
    $stmt_status = $pdo_mci->prepare("
        SELECT cs.nome as status, COUNT(*) as quantidade
        FROM cad_registros cr
        INNER JOIN cad_status cs ON cr.status = cs.id
        WHERE cr.funcionario = ?
        GROUP BY cs.nome
        ORDER BY quantidade DESC
    ");
    $stmt_status->execute([$funcionario_id]);
    $distribuicao_status = $stmt_status->fetchAll(PDO::FETCH_ASSOC);
    
    // Buscar últimas atualizações (apenas registros pendentes)
    $stmt_ultimas = $pdo_mci->prepare("
        SELECT
            cr.nome_cliente,
            cr.numero_cpf_cnpj,
            cs.nome as status,
            cr.data_atualizacao
        FROM cad_registros cr
        INNER JOIN cad_status cs ON cr.status = cs.id
        WHERE cr.funcionario = ?
        AND cs.nome = 'Pendente'
        ORDER BY cr.data_atualizacao DESC
        LIMIT 5
    ");
    $stmt_ultimas->execute([$funcionario_id]);
    $ultimas_atualizacoes = $stmt_ultimas->fetchAll(PDO::FETCH_ASSOC);
    
    $response = [
        'success' => true,
        'data' => [
            'funcionario_id' => $funcionario_id,
            'total_registros' => intval($total_registros),
            'registros_disponiveis' => intval($registros_disponiveis),
            'registros_removidos' => intval($total_registros - $registros_disponiveis),
            'distribuicao_status' => $distribuicao_status,
            'ultimas_atualizacoes' => $ultimas_atualizacoes
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
