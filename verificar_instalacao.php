<?php
/**
 * Script de verificação e correção automática da instalação do MCI
 * Execute este arquivo para verificar e corrigir problemas de instalação
 */

require_once 'config/config.php';

echo "<!DOCTYPE html>";
echo "<html lang='pt-BR'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Verificação da Instalação - MCI</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo ":root { --sicoob-verde-escuro: #003641; --sicoob-turquesa: #00AE9D; }";
echo "body { background-color: #f8f9fa; }";
echo ".card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }";
echo ".card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }";
echo ".status-ok { color: #28a745; font-weight: bold; }";
echo ".status-error { color: #dc3545; font-weight: bold; }";
echo ".status-warning { color: #ffc107; font-weight: bold; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-4'>";
echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h3><i class='fas fa-tools'></i> Verificação da Instalação - Sistema MCI</h3>";
echo "</div>";
echo "<div class='card-body'>";

$problemas_encontrados = [];
$correcoes_aplicadas = [];

try {
    // 1. Verificar conexão com banco MCI
    echo "<h5>1. Verificando conexão com banco MCI...</h5>";
    
    if (isset($pdo_mci)) {
        echo "<p class='status-ok'>✅ Conexão com banco MCI estabelecida</p>";
    } else {
        echo "<p class='status-error'>❌ Erro na conexão com banco MCI</p>";
        $problemas_encontrados[] = "Conexão com banco MCI falhou";
    }
    
    // 2. Verificar tabelas principais
    echo "<h5>2. Verificando tabelas principais...</h5>";
    
    $tabelas_necessarias = [
        'cad_registros' => 'Tabela principal de registros',
        'cad_status' => 'Tabela de status',
        'cad_logs' => 'Tabela de logs',
        'cad_importacoes' => 'Tabela de importações',
        'cad_erros_importacao' => 'Tabela de erros de importação'
    ];
    
    foreach ($tabelas_necessarias as $tabela => $descricao) {
        try {
            $stmt = $pdo_mci->query("SHOW TABLES LIKE '$tabela'");

            if ($stmt && $stmt->fetch()) {
                echo "<p class='status-ok'>✅ $tabela - $descricao</p>";
            } else {
                echo "<p class='status-error'>❌ $tabela - $descricao (NÃO ENCONTRADA)</p>";
                $problemas_encontrados[] = "Tabela $tabela não encontrada";
            }
        } catch (PDOException $e) {
            echo "<p class='status-error'>❌ $tabela - Erro ao verificar: " . htmlspecialchars($e->getMessage()) . "</p>";
            $problemas_encontrados[] = "Erro ao verificar tabela $tabela";
        }
    }
    
    // 3. Verificar e criar tabela mci_metas
    echo "<h5>3. Verificando tabela de metas...</h5>";
    
    $stmt = $pdo_mci->query("SHOW TABLES LIKE 'mci_metas'");
    
    if ($stmt->fetch()) {
        echo "<p class='status-ok'>✅ Tabela mci_metas encontrada</p>";
        
        // Verificar se tem dados
        $stmt_count = $pdo_mci->prepare("SELECT COUNT(*) FROM mci_metas WHERE ativo = TRUE");
        $stmt_count->execute();
        $count = $stmt_count->fetchColumn();
        
        if ($count > 0) {
            echo "<p class='status-ok'>✅ Meta ativa encontrada</p>";
        } else {
            echo "<p class='status-warning'>⚠️ Nenhuma meta ativa encontrada, inserindo meta padrão...</p>";
            
            $stmt_insert = $pdo_mci->prepare("
                INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) 
                VALUES (75.00, TRUE, 1)
            ");
            $stmt_insert->execute();
            
            echo "<p class='status-ok'>✅ Meta padrão de 75% inserida</p>";
            $correcoes_aplicadas[] = "Meta padrão inserida";
        }
    } else {
        echo "<p class='status-warning'>⚠️ Tabela mci_metas não encontrada, criando...</p>";
        
        $sql_create_metas = "
            CREATE TABLE mci_metas (
                id INT AUTO_INCREMENT PRIMARY KEY,
                porcentagem_meta DECIMAL(5,2) NOT NULL COMMENT 'Porcentagem da meta (ex: 75.00 para 75%)',
                ativo BOOLEAN DEFAULT TRUE COMMENT 'Meta ativa/inativa',
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de criação',
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data de atualização',
                usuario_criacao INT COMMENT 'ID do usuário que criou',
                usuario_atualizacao INT COMMENT 'ID do usuário que atualizou',
                
                INDEX idx_ativo (ativo),
                INDEX idx_data_criacao (data_criacao)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela de configuração de metas do MCI'
        ";
        
        $pdo_mci->exec($sql_create_metas);
        echo "<p class='status-ok'>✅ Tabela mci_metas criada</p>";
        
        // Inserir meta padrão
        $stmt_insert = $pdo_mci->prepare("
            INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) 
            VALUES (75.00, TRUE, 1)
        ");
        $stmt_insert->execute();
        
        echo "<p class='status-ok'>✅ Meta padrão de 75% inserida</p>";
        $correcoes_aplicadas[] = "Tabela mci_metas criada e meta padrão inserida";
    }
    
    // 4. Verificar diretórios
    echo "<h5>4. Verificando diretórios...</h5>";
    
    $diretorios_necessarios = ['logs', 'uploads'];
    
    foreach ($diretorios_necessarios as $dir) {
        if (is_dir($dir)) {
            echo "<p class='status-ok'>✅ Diretório '$dir' existe</p>";
        } else {
            echo "<p class='status-warning'>⚠️ Diretório '$dir' não encontrado, criando...</p>";
            
            if (mkdir($dir, 0755, true)) {
                echo "<p class='status-ok'>✅ Diretório '$dir' criado</p>";
                $correcoes_aplicadas[] = "Diretório '$dir' criado";
            } else {
                echo "<p class='status-error'>❌ Erro ao criar diretório '$dir'</p>";
                $problemas_encontrados[] = "Não foi possível criar diretório '$dir'";
            }
        }
    }
    
    // 5. Verificar permissões de escrita
    echo "<h5>5. Verificando permissões...</h5>";
    
    foreach ($diretorios_necessarios as $dir) {
        if (is_dir($dir) && is_writable($dir)) {
            echo "<p class='status-ok'>✅ Diretório '$dir' tem permissão de escrita</p>";
        } else {
            echo "<p class='status-error'>❌ Diretório '$dir' sem permissão de escrita</p>";
            $problemas_encontrados[] = "Diretório '$dir' sem permissão de escrita";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'>❌ Erro durante verificação: " . htmlspecialchars($e->getMessage()) . "</p>";
    $problemas_encontrados[] = "Erro geral: " . $e->getMessage();
}

// Resumo
echo "<hr>";
echo "<h5>Resumo da Verificação</h5>";

if (empty($problemas_encontrados)) {
    echo "<div class='alert alert-success'>";
    echo "<h6><i class='fas fa-check-circle'></i> Sistema MCI está funcionando corretamente!</h6>";
    echo "<p>Todas as verificações passaram com sucesso.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h6><i class='fas fa-exclamation-triangle'></i> Problemas encontrados:</h6>";
    echo "<ul>";
    foreach ($problemas_encontrados as $problema) {
        echo "<li>" . htmlspecialchars($problema) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($correcoes_aplicadas)) {
    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-tools'></i> Correções aplicadas automaticamente:</h6>";
    echo "<ul>";
    foreach ($correcoes_aplicadas as $correcao) {
        echo "<li>" . htmlspecialchars($correcao) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div class='mt-4'>";
echo "<h6>Próximos passos:</h6>";
echo "<ul>";
echo "<li><a href='test_connection.php' class='btn btn-primary btn-sm'>Executar teste de conexão</a></li>";
echo "<li><a href='index.php' class='btn btn-success btn-sm'>Acessar o sistema</a></li>";
echo "<li><a href='gerenciar.php' class='btn btn-info btn-sm'>Testar gerenciamento</a></li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
