-- Cria<PERSON> das tabelas para o projeto MCI - Atualizações Cadastrais
-- Banco: mci
-- Prefixo: cad

-- Tabela principal com os dados da planilha
CREATE TABLE IF NOT EXISTS cad_registros (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pa VARCHAR(10) NOT NULL COMMENT 'PA',
    nome_cliente VARCHAR(255) NOT NULL COMMENT 'Nome Cliente',
    numero_cpf_cnpj VARCHAR(20) NOT NULL COMMENT 'Número CPF/CNPJ',
    cnae VARCHAR(20) COMMENT 'CNAE',
    data_ultima_atualizacao_renda DATE COMMENT 'Data Última Atualização Renda',
    sigla_tipo_pessoa VARCHAR(5) COMMENT 'Sigla Tipo Pessoa',
    profissao VARCHAR(100) COMMENT 'Profissão',
    deposito_total DECIMAL(15,2) COMMENT 'Depósito Total',
    saldo_devedor DECIMAL(15,2) COMMENT '<PERSON><PERSON>edor',
    funcionario VARCHAR(100) COMMENT 'FUNCIONÁRIO',
    data_solicitacao_laudo DATE COMMENT 'DATA DA SOLICITAÇÃO DO LAUDO',
    tecnico_responsavel VARCHAR(100) COMMENT 'TÉCNICO RESPONSÁVEL',
    data_atual_sisbr DATE COMMENT 'DATA DA ATUAL. SISBR',
    
    -- Campos de controle
    status ENUM('pendente', 'em_andamento', 'concluido', 'cancelado') DEFAULT 'pendente',
    observacoes TEXT,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    usuario_cadastro INT,
    usuario_atualizacao INT,
    
    -- Índices
    INDEX idx_pa (pa),
    INDEX idx_cpf_cnpj (numero_cpf_cnpj),
    INDEX idx_status (status),
    INDEX idx_data_cadastro (data_cadastro)
);

-- Tabela de logs específicos do MCI
CREATE TABLE IF NOT EXISTS cad_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    acao VARCHAR(100) NOT NULL,
    detalhes TEXT,
    registro_id INT,
    data_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_usuario (usuario_id),
    INDEX idx_registro (registro_id),
    INDEX idx_data (data_hora),
    FOREIGN KEY (registro_id) REFERENCES cad_registros(id) ON DELETE SET NULL
);

-- Tabela de controle de importações
CREATE TABLE IF NOT EXISTS cad_importacoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome_arquivo VARCHAR(255) NOT NULL,
    tamanho_arquivo INT NOT NULL,
    total_registros INT NOT NULL,
    registros_importados INT DEFAULT 0,
    registros_erro INT DEFAULT 0,
    status ENUM('processando', 'concluido', 'erro') DEFAULT 'processando',
    detalhes_erro TEXT,
    usuario_id INT NOT NULL,
    data_importacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_conclusao TIMESTAMP NULL,
    
    INDEX idx_usuario (usuario_id),
    INDEX idx_status (status),
    INDEX idx_data (data_importacao)
);

-- Tabela de erros de importação
CREATE TABLE IF NOT EXISTS cad_erros_importacao (
    id INT AUTO_INCREMENT PRIMARY KEY,
    importacao_id INT NOT NULL,
    linha INT NOT NULL,
    campo VARCHAR(50),
    valor TEXT,
    erro TEXT NOT NULL,
    data_erro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (importacao_id) REFERENCES cad_importacoes(id) ON DELETE CASCADE,
    INDEX idx_importacao (importacao_id)
);
