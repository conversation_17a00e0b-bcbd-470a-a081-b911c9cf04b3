<?php
try {
    require_once 'config/database.php';
    echo "✅ Conexão MCI OK\n";
    
    // <PERSON><PERSON>r alias para compatibilidade
    $pdo_sicoob = $pdo;
    echo "✅ Alias Sicoob OK\n";
    
    // Testar busca de funcionários
    $stmt = $pdo_sicoob->prepare("
        SELECT u.id, u.nome_completo
        FROM usuarios u
        INNER JOIN usuario_setor us ON u.id = us.usuario_id
        WHERE us.setor_id = 8 AND u.ativo = TRUE
        ORDER BY u.nome_completo
        LIMIT 5
    ");
    $stmt->execute();
    $funcionarios = $stmt->fetchAll();
    echo "✅ Funcionários encontrados: " . count($funcionarios) . "\n";
    
    // Testar contagem de registros
    $stmt = $pdo_mci->query("SELECT COUNT(*) FROM cad_registros");
    $total = $stmt->fetchColumn();
    echo "✅ Total de registros MCI: $total\n";
    
    echo "✅ Todos os testes básicos passaram!\n";
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}
?>
