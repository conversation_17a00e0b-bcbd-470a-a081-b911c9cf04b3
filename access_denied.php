<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acesso Negado - MCI | Sicoob</title>
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-cinza: #6c757d;
        }

        body {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .access-denied-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .access-denied-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .access-denied-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa), var(--sicoob-verde-claro));
        }

        .lock-icon {
            font-size: 4rem;
            color: var(--sicoob-cinza);
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .access-denied-title {
            color: var(--sicoob-verde-escuro);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .access-denied-subtitle {
            color: var(--sicoob-cinza);
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }

        .access-denied-description {
            color: #666;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 4px solid var(--sicoob-turquesa);
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
            text-align: left;
        }

        .info-box h5 {
            color: var(--sicoob-verde-escuro);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .info-box ul {
            margin: 0;
            padding-left: 1.5rem;
        }

        .info-box li {
            margin-bottom: 0.5rem;
            color: #666;
        }

        .btn-sicoob {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn-sicoob:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            color: white;
            text-decoration: none;
        }

        .btn-secondary-sicoob {
            background: transparent;
            border: 2px solid var(--sicoob-verde-escuro);
            color: var(--sicoob-verde-escuro);
            padding: 10px 28px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn-secondary-sicoob:hover {
            background: var(--sicoob-verde-escuro);
            color: white;
            text-decoration: none;
        }

        .contact-info {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .contact-info h6 {
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: #666;
        }

        .contact-item i {
            color: var(--sicoob-turquesa);
            margin-right: 0.5rem;
            width: 20px;
        }

        @media (max-width: 768px) {
            .access-denied-card {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .access-denied-title {
                font-size: 2rem;
            }

            .lock-icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="access-denied-card">
            <i class="fas fa-shield-alt lock-icon"></i>
            
            <h1 class="access-denied-title">Acesso Restrito</h1>
            <p class="access-denied-subtitle">Sistema MCI - Atualizações Cadastrais</p>
            
            <p class="access-denied-description">
                Você não possui permissão para acessar o sistema <strong>MCI (Atualizações Cadastrais)</strong>. 
                Este módulo requer autorização específica para garantir a segurança e integridade dos dados.
            </p>

            <div class="info-box">
                <h5><i class="fas fa-info-circle"></i> Para solicitar acesso:</h5>
                <ul>
                    <li>Entre em contato com seu <strong>gestor imediato</strong></li>
                    <li>Solicite ao <strong>administrador do sistema</strong></li>
                    <li>Envie um chamado para o <strong>setor de TI</strong></li>
                </ul>
            </div>

            <div class="contact-info">
                <h6><i class="fas fa-headset"></i> Precisa de ajuda?</h6>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>Entre em contato com o suporte técnico</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-user-shield"></i>
                    <span>Fale com o administrador do sistema</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-users"></i>
                    <span>Consulte seu gestor de setor</span>
                </div>
            </div>

            <div class="mt-4">
                <a href="../dashboard.php" class="btn-sicoob">
                    <i class="fas fa-home"></i> Voltar ao Dashboard
                </a>
                <a href="../logout.php" class="btn-secondary-sicoob">
                    <i class="fas fa-sign-out-alt"></i> Sair do Sistema
                </a>
            </div>

            <div class="mt-4">
                <small class="text-muted">
                    <i class="fas fa-clock"></i> 
                    Acesso negado em <?php echo date('d/m/Y H:i:s'); ?>
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
