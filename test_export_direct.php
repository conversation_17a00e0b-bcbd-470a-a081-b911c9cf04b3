<?php
// Teste direto do export_excel.php
require_once 'auth_check.php';

echo "<h1>🧪 Teste Direto do Export Excel</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .info { color: #17a2b8; }
    .card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
</style>";

try {
    echo "<div class='card'>";
    echo "<h2 class='info'>1. Testando Conexão com Banco</h2>";
    
    // Testar conexão
    $stmt_test = $pdo_mci->query("SELECT COUNT(*) as total FROM cad_registros");
    $total = $stmt_test->fetchColumn();
    echo "<p class='success'>✅ Conexão OK - Total de registros: $total</p>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>2. Testando Query do Export</h2>";
    
    // Testar query do export (mesma do export_excel.php)
    $sql = "
        SELECT 
            cr.id,
            cr.pa,
            pa.nome as nome_ponto_atendimento,
            cr.nome_cliente,
            cr.numero_cpf_cnpj,
            cr.data_ultima_atualizacao_renda,
            cr.funcionario,
            u_func.nome_completo as funcionario_nome,
            cr.data_solicitacao_laudo,
            cr.tecnico_responsavel,
            u_tec.nome_completo as tecnico_nome,
            cr.data_atual_sisbr,
            cr.status,
            cs.nome as status_nome,
            cr.data_cadastro,
            cr.observacoes
        FROM cad_registros cr
        LEFT JOIN sicoob_access_control.pontos_atendimento pa ON cr.pa = pa.numero
        LEFT JOIN sicoob_access_control.usuarios u_func ON cr.funcionario = u_func.id
        LEFT JOIN sicoob_access_control.usuarios u_tec ON cr.tecnico_responsavel = u_tec.id
        INNER JOIN cad_status cs ON cr.status = cs.id
        ORDER BY cr.data_cadastro DESC
        LIMIT 5
    ";

    $stmt = $pdo_mci->prepare($sql);
    $stmt->execute();
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p class='success'>✅ Query executada com sucesso - " . count($registros) . " registros encontrados</p>";
    
    if (count($registros) > 0) {
        echo "<h4>Primeiros 5 registros:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        echo "<th>ID</th><th>PA</th><th>Cliente</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($registros as $registro) {
            echo "<tr>";
            echo "<td>{$registro['id']}</td>";
            echo "<td>{$registro['pa']}</td>";
            echo "<td>{$registro['nome_cliente']}</td>";
            echo "<td>{$registro['status_nome']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>3. Links de Teste</h2>";
    echo "<p><strong>Teste direto do export:</strong></p>";
    echo "<ul>";
    echo "<li><a href='export_excel.php?download=excel' target='_blank'>Download direto (todos os registros)</a></li>";
    echo "<li><a href='export_excel.php?download=excel&status=Pendente' target='_blank'>Download com filtro Status=Pendente</a></li>";
    echo "<li><a href='export_excel.php?download=excel&pa=2' target='_blank'>Download com filtro PA=2</a></li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>4. Teste da Função JavaScript</h2>";
    echo "<p>Clique no botão abaixo para testar a mesma função JavaScript da página gerenciar.php:</p>";
    
    // Simular variáveis PHP para JavaScript
    $filtro_status = '';
    $filtro_pa = '';
    $filtro_associado = '';
    $filtro_funcionario = '';
    $filtro_tecnico = '';
    $filtro_mes_renda = '';
    $sort_column = 'data_cadastro';
    $sort_direction = 'desc';
    
    echo "<button type='button' class='btn btn-success' onclick='downloadExcel()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
    echo "<i class='fas fa-file-excel'></i> Testar Download Excel";
    echo "</button>";
    echo "</div>";

    echo "<div class='card'>";
    echo "<h2 class='info'>5. Debug da URL</h2>";
    echo "<p>URL que será gerada pela função JavaScript:</p>";
    echo "<div id='debug-url' style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;'></div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='card'>";
    echo "<h2 class='error'>❌ Erro no Teste</h2>";
    echo "<p>Erro: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<script>
function downloadExcel() {
    // Construir URL com os filtros atuais (mesma função do gerenciar.php)
    const params = new URLSearchParams();
    
    // Adicionar filtros atuais
    const status = '<?php echo htmlspecialchars($filtro_status); ?>';
    const pa = '<?php echo htmlspecialchars($filtro_pa); ?>';
    const associado = '<?php echo htmlspecialchars($filtro_associado); ?>';
    const funcionario = '<?php echo htmlspecialchars($filtro_funcionario); ?>';
    const tecnico = '<?php echo htmlspecialchars($filtro_tecnico); ?>';
    const mesRenda = '<?php echo htmlspecialchars($filtro_mes_renda); ?>';
    const sort = '<?php echo htmlspecialchars($sort_column); ?>';
    const dir = '<?php echo htmlspecialchars($sort_direction); ?>';
    
    if (status) params.append('status', status);
    if (pa) params.append('pa', pa);
    if (associado) params.append('associado', associado);
    if (funcionario) params.append('funcionario', funcionario);
    if (tecnico) params.append('tecnico', tecnico);
    if (mesRenda) params.append('mes_renda', mesRenda);
    if (sort) params.append('sort', sort);
    if (dir) params.append('dir', dir);
    
    // Adicionar parâmetro para indicar que é download
    params.append('download', 'excel');
    
    // Mostrar URL no debug
    const finalUrl = 'export_excel.php?' + params.toString();
    document.getElementById('debug-url').textContent = finalUrl;
    
    console.log('URL gerada:', finalUrl);
    
    // Redirecionar para download
    window.location.href = finalUrl;
}
</script>
