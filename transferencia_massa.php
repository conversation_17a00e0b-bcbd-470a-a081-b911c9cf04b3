<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

// Verificar se usuário tem permissão de gestor ou administrador
if (!in_array($_SESSION['mci_permission_level'], ['gestor', 'administrador'])) {
    header('Location: access_denied.php');
    exit;
}

$logger = new MciLogger();
$message = '';
$error = '';

// Processar transferência em massa
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'transferir_massa') {
    try {
        $funcionario_origem = $_POST['funcionario_origem'];
        $transferencias = $_POST['transferencias'] ?? [];
        
        if (empty($funcionario_origem)) {
            throw new Exception('Funcionário de origem é obrigatório');
        }
        
        if (empty($transferencias)) {
            throw new Exception('Nenhuma transferência foi configurada');
        }
        
        $pdo_mci->beginTransaction();
        
        $total_transferidos = 0;
        $detalhes_transferencia = [];
        
        foreach ($transferencias as $transferencia) {
            $funcionario_destino = $transferencia['funcionario_destino'];
            $quantidade = intval($transferencia['quantidade']);
            
            if (empty($funcionario_destino) || $quantidade <= 0) {
                continue;
            }
            
            // Buscar registros do funcionário origem (apenas status 'Pendente', ordenados por data_cadastro para transferir os mais antigos primeiro)
            $stmt = $pdo_mci->prepare("
                SELECT id FROM cad_registros
                WHERE funcionario = ?
                AND status = (SELECT id FROM cad_status WHERE nome = 'Pendente' LIMIT 1)
                ORDER BY data_cadastro ASC
                LIMIT ?
            ");
            $stmt->execute([$funcionario_origem, $quantidade]);
            $registros_para_transferir = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (count($registros_para_transferir) < $quantidade) {
                throw new Exception("Funcionário de origem não possui registros suficientes. Solicitado: $quantidade, Disponível: " . count($registros_para_transferir));
            }
            
            // Transferir registros
            $placeholders = str_repeat('?,', count($registros_para_transferir) - 1) . '?';
            $stmt_update = $pdo_mci->prepare("
                UPDATE cad_registros 
                SET funcionario = ?, usuario_atualizacao = ?, data_atualizacao = NOW()
                WHERE id IN ($placeholders)
            ");
            
            $params = [$funcionario_destino, $_SESSION['user_id']];
            $params = array_merge($params, $registros_para_transferir);
            $stmt_update->execute($params);
            
            $total_transferidos += $quantidade;
            
            // Buscar nomes dos funcionários para log
            $stmt_origem = $pdo_sicoob->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
            $stmt_origem->execute([$funcionario_origem]);
            $nome_origem = $stmt_origem->fetchColumn();
            
            $stmt_destino = $pdo_sicoob->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
            $stmt_destino->execute([$funcionario_destino]);
            $nome_destino = $stmt_destino->fetchColumn();
            
            $detalhes_transferencia[] = "$quantidade registros de '$nome_origem' para '$nome_destino'";
        }
        
        $pdo_mci->commit();
        
        // Log da transferência
        $detalhes_log = "MCI - Transferência em massa: " . implode(', ', $detalhes_transferencia) . " | Total: $total_transferidos registros";
        $logger->log('MCI - Transferência em massa', $detalhes_log);
        
        $message = "Transferência realizada com sucesso! $total_transferidos registros transferidos.";
        
    } catch (Exception $e) {
        if ($pdo_mci->inTransaction()) {
            $pdo_mci->rollBack();
        }
        $error = $e->getMessage();
        $logger->logFile("Erro na transferência em massa: " . $error, 'ERROR');
    }
}

// Buscar funcionários do setor 8 (cadastro)
$stmt_funcionarios = $pdo_sicoob->prepare("
    SELECT u.id, u.nome_completo
    FROM usuarios u
    INNER JOIN usuario_setor us ON u.id = us.usuario_id
    WHERE us.setor_id = 8 AND u.ativo = TRUE
    ORDER BY u.nome_completo
");
$stmt_funcionarios->execute();
$funcionarios = $stmt_funcionarios->fetchAll();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transferência em Massa - <?php echo MCI_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
        }

        body { background-color: #f8f9fa; }
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .btn-primary { background-color: var(--sicoob-verde-escuro); border-color: var(--sicoob-verde-escuro); }
        .btn-primary:hover { background-color: var(--sicoob-turquesa); border-color: var(--sicoob-turquesa); }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        
        .transferencia-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }
        
        .transferencia-item.removing {
            opacity: 0.5;
            transform: scale(0.95);
            transition: all 0.3s ease;
        }
        
        .btn-remove-transferencia {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }
        
        .info-funcionario {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .contador-registros {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--sicoob-verde-escuro);
        }
        
        .resumo-transferencia {
            background: linear-gradient(135deg, #fff3e0, #fce4ec);
            border-radius: 8px;
            padding: 1rem;
            border-left: 4px solid var(--sicoob-turquesa);
        }
        
        .btn-add-transferencia {
            background: linear-gradient(135deg, var(--sicoob-verde-claro), #a4c639);
            border: none;
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }
        
        .btn-add-transferencia:hover {
            background: linear-gradient(135deg, #a4c639, var(--sicoob-verde-claro));
            color: var(--sicoob-verde-escuro);
        }
        
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }
        
        .loading-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .spinner-border-custom {
            width: 3rem;
            height: 3rem;
            border-width: 0.3rem;
            border-color: var(--sicoob-turquesa);
            border-right-color: transparent;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner-border spinner-border-custom" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <h5 class="mt-3">Processando transferência...</h5>
            <p class="text-muted">Por favor, aguarde enquanto os registros são transferidos.</p>
        </div>
    </div>

    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-exchange-alt"></i> <?php echo MCI_PROJECT_NAME; ?> - Transferência em Massa
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="gerenciar.php">
                    <i class="fas fa-arrow-left"></i> Voltar para Registros
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Alertas -->
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-primary">
                    <i class="fas fa-exchange-alt"></i>
                    Transferência em Massa de Registros
                </h1>
                <p class="text-muted">Transfira registros entre funcionários de forma rápida e organizada</p>
            </div>
        </div>

        <!-- Formulário Principal -->
        <form method="POST" id="formTransferencia">
            <input type="hidden" name="action" value="transferir_massa">

            <div class="row">
                <!-- Seleção do Funcionário de Origem -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-user-minus"></i> Funcionário de Origem
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Selecione o funcionário que possui os registros:</label>
                                <select name="funcionario_origem" id="funcionario_origem" class="form-select" required>
                                    <option value="">Selecione um funcionário...</option>
                                    <?php foreach ($funcionarios as $funcionario): ?>
                                    <option value="<?php echo $funcionario['id']; ?>">
                                        <?php echo htmlspecialchars($funcionario['nome_completo']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Informações do funcionário selecionado -->
                            <div id="info_funcionario_origem" class="info-funcionario" style="display: none;">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-user-circle fa-2x text-primary me-3"></i>
                                    <div>
                                        <h6 class="mb-0" id="nome_funcionario_origem"></h6>
                                        <small class="text-muted">Funcionário selecionado</small>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="contador-registros" id="total_registros_origem">0</div>
                                        <small class="text-muted">Total de Registros</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="contador-registros" id="registros_disponiveis">0</div>
                                        <small class="text-muted">Disponíveis</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Configuração das Transferências -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-exchange-alt"></i> Configurar Transferências
                            </h6>
                            <button type="button" class="btn btn-add-transferencia btn-sm" id="btn_add_transferencia" disabled>
                                <i class="fas fa-plus"></i> Adicionar Transferência
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="transferencias_container">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-arrow-right fa-3x mb-3"></i>
                                    <p>Selecione um funcionário de origem para começar a configurar as transferências</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resumo e Ações -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-clipboard-list"></i> Resumo da Transferência
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="resumo_transferencia" class="resumo-transferencia">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="contador-registros" id="total_a_transferir">0</div>
                                        <small class="text-muted">Total a Transferir</small>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="contador-registros" id="total_transferencias">0</div>
                                        <small class="text-muted">Nº de Transferências</small>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="contador-registros" id="registros_restantes">0</div>
                                        <small class="text-muted">Permanecerão com Origem</small>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="contador-registros text-success" id="status_validacao">✓</div>
                                        <small class="text-muted">Status</small>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg" id="btn_executar_transferencia" disabled>
                                    <i class="fas fa-exchange-alt"></i> Executar Transferência
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetarFormulario()">
                                    <i class="fas fa-undo"></i> Resetar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let contadorTransferencias = 0;
        let funcionarioOrigemData = null;

        // Dados dos funcionários para uso no JavaScript
        const funcionarios = <?php echo json_encode($funcionarios); ?>;

        document.addEventListener('DOMContentLoaded', function() {
            // Event listener para seleção do funcionário de origem
            document.getElementById('funcionario_origem').addEventListener('change', function() {
                const funcionarioId = this.value;
                if (funcionarioId) {
                    carregarDadosFuncionarioOrigem(funcionarioId);
                } else {
                    resetarFuncionarioOrigem();
                }
            });

            // Event listener para adicionar transferência
            document.getElementById('btn_add_transferencia').addEventListener('click', function() {
                adicionarTransferencia();
            });

            // Event listener para o formulário
            document.getElementById('formTransferencia').addEventListener('submit', function(e) {
                if (!validarFormulario()) {
                    e.preventDefault();
                    return false;
                }

                // Mostrar loading
                document.getElementById('loadingOverlay').style.display = 'flex';
            });
        });

        function carregarDadosFuncionarioOrigem(funcionarioId) {
            // Buscar dados do funcionário via AJAX
            fetch('ajax/get_funcionario_registros.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    funcionario_id: funcionarioId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    funcionarioOrigemData = data.data;
                    exibirDadosFuncionarioOrigem();
                    habilitarAdicaoTransferencias();
                } else {
                    alert('Erro ao carregar dados do funcionário: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao carregar dados do funcionário');
            });
        }

        function exibirDadosFuncionarioOrigem() {
            const funcionario = funcionarios.find(f => f.id == funcionarioOrigemData.funcionario_id);

            document.getElementById('nome_funcionario_origem').textContent = funcionario.nome_completo;
            document.getElementById('total_registros_origem').textContent = funcionarioOrigemData.total_registros;
            document.getElementById('registros_disponiveis').textContent = funcionarioOrigemData.registros_disponiveis;

            document.getElementById('info_funcionario_origem').style.display = 'block';
        }

        function resetarFuncionarioOrigem() {
            funcionarioOrigemData = null;
            document.getElementById('info_funcionario_origem').style.display = 'none';
            document.getElementById('btn_add_transferencia').disabled = true;
            limparTransferencias();
        }

        function habilitarAdicaoTransferencias() {
            document.getElementById('btn_add_transferencia').disabled = false;
            limparTransferencias();
            adicionarTransferencia(); // Adicionar primeira transferência automaticamente
        }

        function adicionarTransferencia() {
            contadorTransferencias++;

            const container = document.getElementById('transferencias_container');

            // Limpar mensagem inicial se existir
            if (container.querySelector('.text-center.text-muted')) {
                container.innerHTML = '';
            }

            // Calcular registros já alocados
            const registrosJaAlocados = calcularRegistrosAlocados();
            const registrosDisponiveis = funcionarioOrigemData.registros_disponiveis - registrosJaAlocados;

            // Filtrar funcionários para excluir o funcionário de origem e os já selecionados
            const funcionarioOrigemId = document.getElementById('funcionario_origem').value;
            const funcionariosJaSelecionados = obterFuncionariosJaSelecionados();
            const funcionariosDisponiveis = funcionarios.filter(f =>
                f.id != funcionarioOrigemId && !funcionariosJaSelecionados.includes(f.id.toString())
            );

            const transferenciaHtml = `
                <div class="transferencia-item" data-index="${contadorTransferencias}">
                    <button type="button" class="btn btn-danger btn-sm btn-remove-transferencia" onclick="removerTransferencia(${contadorTransferencias})">
                        <i class="fas fa-times"></i>
                    </button>

                    <div class="row align-items-end">
                        <div class="col-md-6">
                            <label class="form-label">Funcionário de Destino</label>
                            <select name="transferencias[${contadorTransferencias}][funcionario_destino]" class="form-select funcionario-destino" required>
                                <option value="">Selecione um funcionário...</option>
                                ${funcionariosDisponiveis.map(f => `<option value="${f.id}">${f.nome_completo}</option>`).join('')}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Quantidade de Registros</label>
                            <input type="number" name="transferencias[${contadorTransferencias}][quantidade]"
                                   class="form-control quantidade-registros" min="1"
                                   max="${registrosDisponiveis}" required>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="sugerirQuantidade(${contadorTransferencias})">
                                <i class="fas fa-magic"></i> Auto
                            </button>
                        </div>
                    </div>

                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Máximo disponível: <span class="fw-bold" id="max-disponivel-${contadorTransferencias}">${registrosDisponiveis}</span> registros
                        </small>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', transferenciaHtml);

            // Adicionar event listeners para os novos campos
            const novaTransferencia = container.lastElementChild;
            const inputQuantidade = novaTransferencia.querySelector('.quantidade-registros');

            inputQuantidade.addEventListener('input', function() {
                validarQuantidadeMaxima(this);
                atualizarResumo();
                atualizarMaximosDisponiveis();
            });

            inputQuantidade.addEventListener('keydown', function(e) {
                // Permitir apenas números, backspace, delete, tab, escape, enter, home, end, setas
                if ([46, 8, 9, 27, 13, 110, 190].indexOf(e.keyCode) !== -1 ||
                    // Permitir Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                    (e.keyCode === 65 && e.ctrlKey === true) ||
                    (e.keyCode === 67 && e.ctrlKey === true) ||
                    (e.keyCode === 86 && e.ctrlKey === true) ||
                    (e.keyCode === 88 && e.ctrlKey === true) ||
                    // Permitir home, end, left, right
                    (e.keyCode >= 35 && e.keyCode <= 39)) {
                    return;
                }
                // Garantir que é um número
                if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                    e.preventDefault();
                }
            });

            novaTransferencia.querySelector('.funcionario-destino').addEventListener('change', function() {
                atualizarResumo();
                atualizarOpcoesDestino();
            });

            atualizarResumo();
        }

        function removerTransferencia(index) {
            const transferencia = document.querySelector(`[data-index="${index}"]`);
            transferencia.classList.add('removing');

            setTimeout(() => {
                transferencia.remove();
                atualizarResumo();
                atualizarMaximosDisponiveis();
                atualizarOpcoesDestino();

                // Se não há mais transferências, mostrar mensagem inicial
                const container = document.getElementById('transferencias_container');
                if (container.children.length === 0) {
                    container.innerHTML = `
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-arrow-right fa-3x mb-3"></i>
                            <p>Clique em "Adicionar Transferência" para configurar as transferências</p>
                        </div>
                    `;
                }
            }, 300);
        }

        function calcularRegistrosAlocados() {
            const transferencias = document.querySelectorAll('.transferencia-item');
            let totalAlocado = 0;

            transferencias.forEach(t => {
                const quantidade = parseInt(t.querySelector('.quantidade-registros').value) || 0;
                totalAlocado += quantidade;
            });

            return totalAlocado;
        }

        function obterFuncionariosJaSelecionados() {
            const transferencias = document.querySelectorAll('.transferencia-item');
            const selecionados = [];

            transferencias.forEach(t => {
                const funcionarioId = t.querySelector('.funcionario-destino').value;
                if (funcionarioId) {
                    selecionados.push(funcionarioId);
                }
            });

            return selecionados;
        }

        function atualizarMaximosDisponiveis() {
            const transferencias = document.querySelectorAll('.transferencia-item');

            transferencias.forEach(t => {
                const index = t.dataset.index;
                const quantidadeAtual = parseInt(t.querySelector('.quantidade-registros').value) || 0;

                // Calcular registros alocados em outras transferências
                let registrosAlocadosOutras = 0;
                transferencias.forEach(outra => {
                    if (outra.dataset.index !== index) {
                        const quantidade = parseInt(outra.querySelector('.quantidade-registros').value) || 0;
                        registrosAlocadosOutras += quantidade;
                    }
                });

                const registrosDisponiveis = funcionarioOrigemData.registros_disponiveis - registrosAlocadosOutras;
                const maxElement = document.getElementById(`max-disponivel-${index}`);
                const inputElement = t.querySelector('.quantidade-registros');

                if (maxElement) {
                    maxElement.textContent = registrosDisponiveis;
                }
                if (inputElement) {
                    inputElement.max = registrosDisponiveis;
                }
            });
        }

        function atualizarOpcoesDestino() {
            const funcionarioOrigemId = document.getElementById('funcionario_origem').value;
            const funcionariosJaSelecionados = obterFuncionariosJaSelecionados();

            const transferencias = document.querySelectorAll('.transferencia-item');
            transferencias.forEach(t => {
                const select = t.querySelector('.funcionario-destino');
                const valorAtual = select.value;

                // Limpar opções
                select.innerHTML = '<option value="">Selecione um funcionário...</option>';

                // Adicionar funcionários disponíveis
                funcionarios.forEach(f => {
                    // Excluir funcionário de origem e funcionários já selecionados (exceto o atual)
                    if (f.id != funcionarioOrigemId &&
                        (!funcionariosJaSelecionados.includes(f.id.toString()) || f.id.toString() === valorAtual)) {
                        const option = document.createElement('option');
                        option.value = f.id;
                        option.textContent = f.nome_completo;
                        if (f.id.toString() === valorAtual) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    }
                });
            });
        }

        function sugerirQuantidade(index) {
            const transferencias = document.querySelectorAll('.transferencia-item');
            const registrosDisponiveis = funcionarioOrigemData.registros_disponiveis;

            // Calcular quantidade já alocada em outras transferências
            let quantidadeAlocada = 0;
            transferencias.forEach(t => {
                if (t.dataset.index != index) {
                    const quantidade = parseInt(t.querySelector('.quantidade-registros').value) || 0;
                    quantidadeAlocada += quantidade;
                }
            });

            const registrosRestantes = registrosDisponiveis - quantidadeAlocada;

            // Contar transferências sem quantidade definida (incluindo a atual)
            let transferenciasSemQuantidade = 0;
            transferencias.forEach(t => {
                const quantidade = parseInt(t.querySelector('.quantidade-registros').value) || 0;
                if (quantidade === 0 || t.dataset.index == index) {
                    transferenciasSemQuantidade++;
                }
            });

            const sugestao = transferenciasSemQuantidade > 0 ?
                Math.floor(registrosRestantes / transferenciasSemQuantidade) :
                registrosRestantes;

            const input = document.querySelector(`[data-index="${index}"] .quantidade-registros`);
            input.value = Math.max(1, Math.min(sugestao, registrosRestantes));

            atualizarResumo();
            atualizarMaximosDisponiveis();
        }

        function validarQuantidadeMaxima(input) {
            const transferencia = input.closest('.transferencia-item');
            const index = transferencia.dataset.index;
            const valor = parseInt(input.value) || 0;

            // Calcular registros alocados em outras transferências
            let registrosAlocadosOutras = 0;
            const transferencias = document.querySelectorAll('.transferencia-item');
            transferencias.forEach(outra => {
                if (outra.dataset.index !== index) {
                    const quantidade = parseInt(outra.querySelector('.quantidade-registros').value) || 0;
                    registrosAlocadosOutras += quantidade;
                }
            });

            const registrosDisponiveis = funcionarioOrigemData.registros_disponiveis - registrosAlocadosOutras;

            if (valor > registrosDisponiveis) {
                input.value = registrosDisponiveis;

                // Mostrar feedback visual
                input.classList.add('is-invalid');

                // Mostrar tooltip ou mensagem
                if (!input.nextElementSibling || !input.nextElementSibling.classList.contains('invalid-feedback')) {
                    const feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    feedback.textContent = `Máximo disponível: ${registrosDisponiveis} registros`;
                    input.parentNode.appendChild(feedback);
                }

                // Remover feedback após 3 segundos
                setTimeout(() => {
                    input.classList.remove('is-invalid');
                    const feedback = input.nextElementSibling;
                    if (feedback && feedback.classList.contains('invalid-feedback')) {
                        feedback.remove();
                    }
                }, 3000);
            } else {
                input.classList.remove('is-invalid');
                const feedback = input.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.remove();
                }
            }
        }

        function atualizarResumo() {
            const transferencias = document.querySelectorAll('.transferencia-item');
            let totalATransferir = 0;
            let transferenciasValidas = 0;

            transferencias.forEach(t => {
                const funcionarioDestino = t.querySelector('.funcionario-destino').value;
                const quantidade = parseInt(t.querySelector('.quantidade-registros').value) || 0;

                if (funcionarioDestino && quantidade > 0) {
                    totalATransferir += quantidade;
                    transferenciasValidas++;
                }
            });

            const registrosRestantes = funcionarioOrigemData ? funcionarioOrigemData.registros_disponiveis - totalATransferir : 0;
            const isValido = totalATransferir <= (funcionarioOrigemData ? funcionarioOrigemData.registros_disponiveis : 0) &&
                           totalATransferir > 0 && transferenciasValidas > 0;

            document.getElementById('total_a_transferir').textContent = totalATransferir;
            document.getElementById('total_transferencias').textContent = transferenciasValidas;
            document.getElementById('registros_restantes').textContent = Math.max(0, registrosRestantes);

            const statusElement = document.getElementById('status_validacao');
            if (isValido) {
                statusElement.textContent = '✓';
                statusElement.className = 'contador-registros text-success';
            } else {
                statusElement.textContent = '⚠';
                statusElement.className = 'contador-registros text-warning';
            }

            document.getElementById('btn_executar_transferencia').disabled = !isValido;
        }

        function validarFormulario() {
            const funcionarioOrigem = document.getElementById('funcionario_origem').value;
            if (!funcionarioOrigem) {
                alert('Por favor, selecione um funcionário de origem.');
                return false;
            }

            const transferencias = document.querySelectorAll('.transferencia-item');
            if (transferencias.length === 0) {
                alert('Por favor, configure pelo menos uma transferência.');
                return false;
            }

            let totalATransferir = 0;
            let transferenciasValidas = 0;

            for (let t of transferencias) {
                const funcionarioDestino = t.querySelector('.funcionario-destino').value;
                const quantidade = parseInt(t.querySelector('.quantidade-registros').value) || 0;

                if (!funcionarioDestino) {
                    alert('Por favor, selecione um funcionário de destino para todas as transferências.');
                    return false;
                }

                if (quantidade <= 0) {
                    alert('Por favor, informe uma quantidade válida para todas as transferências.');
                    return false;
                }

                if (funcionarioDestino === funcionarioOrigem) {
                    alert('O funcionário de destino não pode ser o mesmo que o de origem.');
                    return false;
                }

                totalATransferir += quantidade;
                transferenciasValidas++;
            }

            if (totalATransferir > funcionarioOrigemData.registros_disponiveis) {
                alert(`Total a transferir (${totalATransferir}) excede os registros disponíveis (${funcionarioOrigemData.registros_disponiveis}).`);
                return false;
            }

            // Verificar funcionários duplicados
            const funcionariosDestino = Array.from(transferencias).map(t => t.querySelector('.funcionario-destino').value);
            const funcionariosUnicos = [...new Set(funcionariosDestino)];
            if (funcionariosDestino.length !== funcionariosUnicos.length) {
                alert('Não é possível transferir para o mesmo funcionário em múltiplas operações. Combine as quantidades.');
                return false;
            }

            // Confirmação final
            const funcionarioOrigemNome = funcionarios.find(f => f.id == funcionarioOrigem).nome_completo;
            let detalhesTransferencia = `Confirma a transferência de ${totalATransferir} registros de "${funcionarioOrigemNome}"?\n\nDetalhes:\n`;

            transferencias.forEach(t => {
                const funcionarioDestinoId = t.querySelector('.funcionario-destino').value;
                const funcionarioDestinoNome = funcionarios.find(f => f.id == funcionarioDestinoId).nome_completo;
                const quantidade = t.querySelector('.quantidade-registros').value;
                detalhesTransferencia += `• ${quantidade} registros para "${funcionarioDestinoNome}"\n`;
            });

            detalhesTransferencia += `\nRegistros que permanecerão com "${funcionarioOrigemNome}": ${funcionarioOrigemData.registros_disponiveis - totalATransferir}`;

            return confirm(detalhesTransferencia);
        }

        function limparTransferencias() {
            const container = document.getElementById('transferencias_container');
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-arrow-right fa-3x mb-3"></i>
                    <p>Clique em "Adicionar Transferência" para configurar as transferências</p>
                </div>
            `;
            contadorTransferencias = 0;
            atualizarResumo();
        }

        function resetarFormulario() {
            if (confirm('Tem certeza que deseja resetar todo o formulário?')) {
                document.getElementById('formTransferencia').reset();
                resetarFuncionarioOrigem();
                limparTransferencias();
            }
        }
    </script>
</body>
</html>
