<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Restaurar Dados de Funcionários e Técnicos</h1>";
echo "<hr>";

try {
    echo "<h3>1. Verificando estado atual...</h3>";
    
    // Verificar estrutura atual
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field IN ('funcionario', 'tecnico_responsavel')");
    $stmt->execute();
    $colunas_atuais = $stmt->fetchAll();
    
    echo "<h4>Estrutura atual:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Padrão</th></tr>";
    foreach ($colunas_atuais as $coluna) {
        echo "<tr>";
        echo "<td><strong>{$coluna['Field']}</strong></td>";
        echo "<td><code>{$coluna['Type']}</code></td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>" . ($coluna['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar dados atuais
    $stmt = $pdo_mci->prepare("
        SELECT 
            COUNT(*) as total,
            COUNT(funcionario) as funcionarios_com_id,
            COUNT(tecnico_responsavel) as tecnicos_com_id
        FROM cad_registros
    ");
    $stmt->execute();
    $stats = $stmt->fetch();
    
    echo "<h4>Estatísticas atuais:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Métrica</th><th>Valor</th></tr>";
    echo "<tr><td>Total de registros</td><td>{$stats['total']}</td></tr>";
    echo "<tr><td>Funcionários com ID</td><td>{$stats['funcionarios_com_id']}</td></tr>";
    echo "<tr><td>Técnicos com ID</td><td>{$stats['tecnicos_com_id']}</td></tr>";
    echo "<tr><td>Funcionários perdidos</td><td>" . ($stats['total'] - $stats['funcionarios_com_id']) . "</td></tr>";
    echo "<tr><td>Técnicos perdidos</td><td>" . ($stats['total'] - $stats['tecnicos_com_id']) . "</td></tr>";
    echo "</table>";
    
    echo "<h3>2. Verificando possibilidades de recuperação...</h3>";
    
    // Verificar se existem backups ou logs
    $backup_options = [];
    
    // Opção 1: Verificar se existe backup automático do MySQL
    echo "<h4>Opções de recuperação:</h4>";
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px;'>";
    echo "<h5>📋 Métodos possíveis:</h5>";
    echo "<ol>";
    echo "<li><strong>Backup do banco de dados</strong> - Se você tem um backup recente</li>";
    echo "<li><strong>Logs do MySQL</strong> - Se o binlog estiver ativo</li>";
    echo "<li><strong>Reimportação</strong> - Reimportar os dados originais das planilhas</li>";
    echo "<li><strong>Recuperação manual</strong> - Inserir dados conhecidos manualmente</li>";
    echo "</ol>";
    echo "</div>";
    
    // Verificar se há arquivos de importação recentes
    echo "<h3>3. Verificando importações recentes...</h3>";
    
    $stmt = $pdo_mci->prepare("
        SELECT * FROM cad_importacoes 
        ORDER BY data_importacao DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $importacoes = $stmt->fetchAll();
    
    if (!empty($importacoes)) {
        echo "<h4>Importações recentes encontradas:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Arquivo</th><th>Data</th><th>Registros</th><th>Status</th></tr>";
        foreach ($importacoes as $imp) {
            echo "<tr>";
            echo "<td>{$imp['id']}</td>";
            echo "<td>" . htmlspecialchars($imp['nome_arquivo']) . "</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($imp['data_importacao'])) . "</td>";
            echo "<td>{$imp['registros_importados']}</td>";
            echo "<td>{$imp['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h5>💡 Sugestão:</h5>";
        echo "<p>Se você ainda tem os arquivos Excel originais, a melhor opção é:</p>";
        echo "<ol>";
        echo "<li>Fazer backup da estrutura atual (só para garantir)</li>";
        echo "<li>Restaurar a estrutura original das colunas</li>";
        echo "<li>Reimportar os dados das planilhas originais</li>";
        echo "<li>Fazer o mapeamento gradual dos nomes para IDs</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $acao = $_POST['acao'] ?? '';
        
        if ($acao == 'restaurar_estrutura') {
            echo "<h3>4. Restaurando estrutura original...</h3>";
            
            try {
                // Remover chaves estrangeiras se existirem
                echo "<p>Passo 1: Removendo chaves estrangeiras...</p>";
                try {
                    $pdo_mci->exec("ALTER TABLE cad_registros DROP FOREIGN KEY fk_registros_funcionario");
                    echo "<p>✅ FK funcionario removida</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ FK funcionario não encontrada</p>";
                }
                
                try {
                    $pdo_mci->exec("ALTER TABLE cad_registros DROP FOREIGN KEY fk_registros_tecnico");
                    echo "<p>✅ FK tecnico removida</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ FK tecnico não encontrada</p>";
                }
                
                // Alterar colunas para VARCHAR
                echo "<p>Passo 2: Convertendo colunas para VARCHAR...</p>";
                $pdo_mci->exec("ALTER TABLE cad_registros MODIFY funcionario VARCHAR(255) NULL COMMENT 'Nome do funcionário'");
                $pdo_mci->exec("ALTER TABLE cad_registros MODIFY tecnico_responsavel VARCHAR(255) NULL COMMENT 'Nome do técnico responsável'");
                echo "<p>✅ Colunas convertidas para VARCHAR</p>";
                
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4 style='color: #155724;'>✅ Estrutura Restaurada!</h4>";
                echo "<p style='color: #155724;'>As colunas agora estão como VARCHAR e prontas para receber nomes novamente.</p>";
                echo "<p style='color: #155724;'><strong>Próximo passo:</strong> Reimporte os dados das planilhas originais.</p>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro ao restaurar estrutura: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
        } elseif ($acao == 'limpar_dados') {
            echo "<h3>4. Limpando dados atuais...</h3>";
            
            try {
                $stmt = $pdo_mci->prepare("DELETE FROM cad_registros");
                $stmt->execute();
                $deleted = $stmt->rowCount();
                
                echo "<p>✅ $deleted registros removidos</p>";
                
                // Resetar auto_increment
                $pdo_mci->exec("ALTER TABLE cad_registros AUTO_INCREMENT = 1");
                echo "<p>✅ Auto increment resetado</p>";
                
                echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4 style='color: #155724;'>✅ Dados Limpos!</h4>";
                echo "<p style='color: #155724;'>Tabela limpa e pronta para reimportação dos dados originais.</p>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erro ao limpar dados: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    } else {
        // Mostrar opções de recuperação
        echo "<h3>4. Opções de Recuperação</h3>";
        
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24;'>⚠️ ATENÇÃO</h4>";
        echo "<p style='color: #721c24;'>Escolha a opção mais adequada para sua situação:</p>";
        echo "</div>";
        
        echo "<div class='row' style='display: flex; gap: 20px;'>";
        
        // Opção 1: Restaurar estrutura
        echo "<div style='flex: 1; background-color: #e7f3ff; padding: 15px; border-radius: 5px;'>";
        echo "<h5>🔄 Opção 1: Restaurar Estrutura</h5>";
        echo "<p>Converte as colunas de volta para VARCHAR (texto) mantendo os IDs existentes como NULL.</p>";
        echo "<p><strong>Use quando:</strong> Você vai reimportar os dados das planilhas originais</p>";
        echo "<form method='POST' style='margin-top: 10px;'>";
        echo "<input type='hidden' name='acao' value='restaurar_estrutura'>";
        echo "<button type='submit' style='background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Restaurar Estrutura</button>";
        echo "</form>";
        echo "</div>";
        
        // Opção 2: Limpar tudo
        echo "<div style='flex: 1; background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h5>🗑️ Opção 2: Limpar Dados</h5>";
        echo "<p>Remove todos os registros da tabela para começar do zero.</p>";
        echo "<p><strong>Use quando:</strong> Você quer recomeçar completamente</p>";
        echo "<form method='POST' style='margin-top: 10px;'>";
        echo "<input type='hidden' name='acao' value='limpar_dados'>";
        echo "<div style='margin: 10px 0;'>";
        echo "<label><input type='checkbox' required> Confirmo que quero limpar todos os dados</label>";
        echo "</div>";
        echo "<button type='submit' style='background-color: #dc3545; color: white; padding: 10px 15px; border: none; border-radius: 5px;'>Limpar Dados</button>";
        echo "</form>";
        echo "</div>";
        
        echo "</div>";
        
        echo "<div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h5>💡 Recomendação</h5>";
        echo "<p>Se você tem as planilhas originais:</p>";
        echo "<ol>";
        echo "<li><strong>Restaurar Estrutura</strong> (Opção 1)</li>";
        echo "<li><strong>Reimportar</strong> os dados das planilhas via <a href='importar.php'>página de importação</a></li>";
        echo "<li><strong>Mapear gradualmente</strong> os nomes para IDs usando uma nova ferramenta</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<p><a href='gerenciar.php'>Ver registros atuais</a> | <a href='importar.php'>Importar dados</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a verificação</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<style>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.row {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }
}
</style>
