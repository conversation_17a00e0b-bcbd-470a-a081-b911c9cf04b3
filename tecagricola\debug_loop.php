<?php
require_once '../auth_check.php';

echo "<h2>🔄 Debug - Loop de Renderização</h2>";

// Executar exatamente a mesma consulta do dashboard
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

echo "<h3>📊 Dados do Array</h3>";
echo "<p><strong>Total de elementos:</strong> " . count($tecnicos_dados) . "</p>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Array Index</th><th>ID</th><th>Nome</th><th>Meta</th><th>Atualizados</th><th>Progresso</th></tr>";

foreach ($tecnicos_dados as $index => $tecnico) {
    echo "<tr>";
    echo "<td><strong>[$index]</strong></td>";
    echo "<td>" . $tecnico['id'] . "</td>";
    echo "<td>" . htmlspecialchars($tecnico['nome']) . "</td>";
    echo "<td>" . $tecnico['meta_total'] . "</td>";
    echo "<td>" . $tecnico['atualizados_total'] . "</td>";
    echo "<td>" . $tecnico['progresso_total'] . "%</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>🔄 Simulação do Loop do Dashboard</h3>";
echo "<p>Simulando exatamente o loop <code>foreach (\$tecnicos_dados as \$tecnico)</code>:</p>";

echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 10px 0; background: #f8f9fa;'>";
echo "<h4>Loop foreach:</h4>";

$tecnico_index = 1;
foreach ($tecnicos_dados as $tecnico) {
    echo "<div style='border: 1px solid #28a745; margin: 10px 0; padding: 10px; background: white;'>";
    echo "<strong>Iteração $tecnico_index:</strong><br>";
    echo "• ID: " . $tecnico['id'] . "<br>";
    echo "• Nome: " . htmlspecialchars($tecnico['nome']) . "<br>";
    echo "• Meta: " . $tecnico['atualizados_total'] . "/" . $tecnico['meta_total'] . "<br>";
    echo "• Progresso: " . $tecnico['progresso_total'] . "%<br>";
    echo "• Classe CSS: tecnico-" . $tecnico_index . "<br>";
    echo "• Avatar: " . strtoupper(substr($tecnico['nome'], 0, 1)) . "<br>";
    echo "</div>";
    $tecnico_index++;
}

echo "</div>";

echo "<h3>🎯 Verificação Específica</h3>";

// Verificar se Felipe (ID 40) está no array
$felipe_no_array = false;
$sidnei_no_array = false;
$maycon_count = 0;
$sidnei_count = 0;

foreach ($tecnicos_dados as $index => $tecnico) {
    if ($tecnico['id'] == 40) {
        $felipe_no_array = true;
        echo "<p style='color: green;'>✅ <strong>Felipe (ID 40) encontrado no array no índice [$index]:</strong> " . htmlspecialchars($tecnico['nome']) . "</p>";
    }
    
    if ($tecnico['id'] == 67) {
        $sidnei_count++;
        echo "<p style='color: blue;'>ℹ️ <strong>Sidnei (ID 67) encontrado no array no índice [$index] (ocorrência $sidnei_count):</strong> " . htmlspecialchars($tecnico['nome']) . "</p>";
    }
    
    if ($tecnico['id'] == 58) {
        $maycon_count++;
        echo "<p style='color: blue;'>ℹ️ <strong>Maycon (ID 58) encontrado no array no índice [$index] (ocorrência $maycon_count):</strong> " . htmlspecialchars($tecnico['nome']) . "</p>";
    }
}

if (!$felipe_no_array) {
    echo "<p style='color: red;'>❌ <strong>Felipe (ID 40) NÃO está no array!</strong></p>";
}

if ($sidnei_count > 1) {
    echo "<p style='color: red;'>❌ <strong>Sidnei aparece $sidnei_count vezes no array!</strong></p>";
} elseif ($sidnei_count == 1) {
    echo "<p style='color: green;'>✅ <strong>Sidnei aparece apenas 1 vez no array.</strong></p>";
}

if ($maycon_count > 1) {
    echo "<p style='color: red;'>❌ <strong>Maycon aparece $maycon_count vezes no array!</strong></p>";
} elseif ($maycon_count == 1) {
    echo "<p style='color: green;'>✅ <strong>Maycon aparece apenas 1 vez no array.</strong></p>";
}

echo "<h3>🔍 Análise do Problema</h3>";

if (count($tecnicos_dados) == 5) {
    echo "<p style='color: green;'>✅ <strong>Array tem 5 elementos (correto)</strong></p>";
    
    if ($felipe_no_array && $sidnei_count == 1 && $maycon_count == 1) {
        echo "<p style='color: green;'>✅ <strong>Dados do array estão corretos</strong></p>";
        echo "<p style='color: orange;'>⚠️ <strong>O problema deve estar na renderização visual ou cache do navegador</strong></p>";
        
        echo "<h4>Possíveis causas:</h4>";
        echo "<ul>";
        echo "<li>Cache do navegador não está sendo limpo</li>";
        echo "<li>Auto-refresh está causando inconsistências</li>";
        echo "<li>CSS está ocultando algum card</li>";
        echo "<li>JavaScript está modificando o DOM</li>";
        echo "</ul>";
        
        echo "<h4>Soluções recomendadas:</h4>";
        echo "<ul>";
        echo "<li>Forçar refresh com Ctrl+F5</li>";
        echo "<li>Abrir em aba anônima/privada</li>";
        echo "<li>Verificar console do navegador para erros</li>";
        echo "<li>Desabilitar temporariamente o auto-refresh</li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>❌ <strong>Problema nos dados do array</strong></p>";
    }
} else {
    echo "<p style='color: red;'>❌ <strong>Array tem " . count($tecnicos_dados) . " elementos (deveria ter 5)</strong></p>";
}

echo "<hr>";
echo "<p><strong>Timestamp:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><a href='dashboard.php?t=" . time() . "' target='_blank'>🚀 Dashboard (com timestamp)</a></p>";
echo "<p><a href='debug_loop.php'>🔄 Executar novamente</a></p>";
?>
