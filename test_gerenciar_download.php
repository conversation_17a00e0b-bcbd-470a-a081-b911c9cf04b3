<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Download Gerenciar - MCI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        body { background-color: #f8f9fa; padding: 20px; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        .btn-excel { background-color: #28a745; border-color: #28a745; color: white; }
        .btn-excel:hover { background-color: #218838; border-color: #1e7e34; color: white; }
        .test-section { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-primary mb-4">
            <i class="fas fa-bug"></i> 
            Diagnóstico do Problema de Download - Gerenciar.php
        </h1>
        
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> Problema Identificado</h5>
            <p class="mb-0">
                O botão de download na página gerenciar.php carrega mas não faz o download. 
                Vamos diagnosticar e corrigir o problema.
            </p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-search"></i> Testes de Diagnóstico
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="test-section">
                            <h6><i class="fas fa-link"></i> Teste 1: Links Diretos</h6>
                            <p>Teste os links diretos para o arquivo de export:</p>
                            <div class="d-grid gap-2">
                                <a href="export_excel.php?download=excel" class="btn btn-primary btn-sm" target="_blank">
                                    Export Original
                                </a>
                                <a href="export_excel_debug.php?download=excel" class="btn btn-info btn-sm" target="_blank">
                                    Export Debug
                                </a>
                            </div>
                        </div>
                        
                        <div class="test-section">
                            <h6><i class="fas fa-code"></i> Teste 2: JavaScript Console</h6>
                            <p>Abra o console do navegador (F12) e clique no botão abaixo:</p>
                            <button type="button" class="btn btn-excel btn-sm" onclick="testDownloadFunction()">
                                <i class="fas fa-file-excel"></i> Testar Função JS
                            </button>
                            <small class="text-muted d-block mt-2">Verifique mensagens no console</small>
                        </div>
                        
                        <div class="test-section">
                            <h6><i class="fas fa-network-wired"></i> Teste 3: Network Tab</h6>
                            <p>Abra a aba Network (F12 → Network) e clique no botão:</p>
                            <button type="button" class="btn btn-excel btn-sm" onclick="testNetworkRequest()">
                                <i class="fas fa-download"></i> Testar Request
                            </button>
                            <small class="text-muted d-block mt-2">Verifique se a requisição é feita</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tools"></i> Possíveis Soluções
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="test-section">
                            <h6><i class="fas fa-shield-alt"></i> Problema 1: Autenticação</h6>
                            <p>Se o auth_check.php está bloqueando:</p>
                            <ul class="small">
                                <li>Verificar se está logado no sistema</li>
                                <li>Verificar permissões de usuário</li>
                                <li>Testar com usuário administrador</li>
                            </ul>
                        </div>
                        
                        <div class="test-section">
                            <h6><i class="fas fa-database"></i> Problema 2: Banco de Dados</h6>
                            <p>Se há erro na query:</p>
                            <ul class="small">
                                <li>Verificar conexão com banco</li>
                                <li>Verificar se tabelas existem</li>
                                <li>Verificar sintaxe da query</li>
                            </ul>
                        </div>
                        
                        <div class="test-section">
                            <h6><i class="fas fa-code"></i> Problema 3: JavaScript</h6>
                            <p>Se a função JS não executa:</p>
                            <ul class="small">
                                <li>Verificar erros no console</li>
                                <li>Verificar se variáveis PHP estão corretas</li>
                                <li>Verificar se URL está sendo construída</li>
                            </ul>
                        </div>
                        
                        <div class="test-section">
                            <h6><i class="fas fa-server"></i> Problema 4: Headers HTTP</h6>
                            <p>Se headers estão incorretos:</p>
                            <ul class="small">
                                <li>Verificar Content-Type</li>
                                <li>Verificar Content-Disposition</li>
                                <li>Verificar se não há output antes dos headers</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-clipboard-list"></i> Checklist de Diagnóstico
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ Testes a Realizar:</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check1">
                                    <label class="form-check-label" for="check1">
                                        Testar link direto export_excel.php
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check2">
                                    <label class="form-check-label" for="check2">
                                        Verificar console JavaScript (F12)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check3">
                                    <label class="form-check-label" for="check3">
                                        Verificar aba Network (F12)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check4">
                                    <label class="form-check-label" for="check4">
                                        Testar com usuário administrador
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>🔍 O que Verificar:</h6>
                                <ul class="small">
                                    <li><strong>Console:</strong> Mensagens de erro JavaScript</li>
                                    <li><strong>Network:</strong> Se requisição é enviada</li>
                                    <li><strong>Response:</strong> Se servidor responde</li>
                                    <li><strong>Headers:</strong> Se headers de download estão corretos</li>
                                    <li><strong>Autenticação:</strong> Se usuário tem permissão</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-link"></i> Links Úteis
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="gerenciar.php" class="btn btn-primary w-100 mb-2" target="_blank">
                                    <i class="fas fa-list"></i> Gerenciar (Original)
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="test_export_direct.php" class="btn btn-info w-100 mb-2" target="_blank">
                                    <i class="fas fa-cog"></i> Teste Direto
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="demo_download_excel.php" class="btn btn-success w-100 mb-2" target="_blank">
                                    <i class="fas fa-play"></i> Demo Funcionando
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="test_download_excel.php" class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                    <i class="fas fa-book"></i> Documentação
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-lightbulb"></i> Próximos Passos</h6>
            <ol class="mb-0">
                <li>Execute os testes de diagnóstico acima</li>
                <li>Verifique o console do navegador para erros</li>
                <li>Teste os links diretos para o export</li>
                <li>Reporte os resultados para correção específica</li>
            </ol>
        </div>
    </div>

    <script>
        function testDownloadFunction() {
            console.log('=== TESTE DA FUNÇÃO DOWNLOAD ===');
            
            try {
                // Simular a função do gerenciar.php
                const params = new URLSearchParams();
                
                // Filtros simulados (vazios)
                const status = '';
                const pa = '';
                const associado = '';
                const funcionario = '';
                const tecnico = '';
                const mesRenda = '';
                const sort = 'data_cadastro';
                const dir = 'desc';
                
                console.log('Filtros:', {status, pa, associado, funcionario, tecnico, mesRenda, sort, dir});
                
                if (status) params.append('status', status);
                if (pa) params.append('pa', pa);
                if (associado) params.append('associado', associado);
                if (funcionario) params.append('funcionario', funcionario);
                if (tecnico) params.append('tecnico', tecnico);
                if (mesRenda) params.append('mes_renda', mesRenda);
                if (sort) params.append('sort', sort);
                if (dir) params.append('dir', dir);
                
                params.append('download', 'excel');
                
                const finalUrl = 'export_excel_debug.php?' + params.toString();
                console.log('URL construída:', finalUrl);
                
                console.log('Redirecionando...');
                window.location.href = finalUrl;
                
            } catch (error) {
                console.error('ERRO na função:', error);
            }
        }

        function testNetworkRequest() {
            console.log('=== TESTE DE REQUISIÇÃO NETWORK ===');
            console.log('Abra a aba Network no DevTools e observe a requisição');
            
            fetch('export_excel_debug.php?download=excel&test=network')
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    return response.text();
                })
                .then(data => {
                    console.log('Response data (primeiros 200 chars):', data.substring(0, 200));
                })
                .catch(error => {
                    console.error('Erro na requisição:', error);
                });
        }
    </script>
</body>
</html>
