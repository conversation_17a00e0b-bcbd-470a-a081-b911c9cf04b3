<?php
require_once 'auth_check.php';

// Verificar se é uma requisição de download
if (!isset($_GET['download']) || $_GET['download'] !== 'excel') {
    header('Location: gerenciar.php');
    exit;
}

// Função para formatar CPF/CNPJ
function formatarCpfCnpj($numero) {
    $numero = preg_replace('/\D/', '', $numero);
    
    if (strlen($numero) == 11) {
        // CPF
        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $numero);
    } elseif (strlen($numero) == 14) {
        // CNPJ
        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $numero);
    }
    
    return $numero;
}

// Função para buscar nome do PA
function buscarNomePA($pa_numero, $pdo_sicoob) {
    try {
        $stmt = $pdo_sicoob->prepare("SELECT nome FROM pontos_atendimento WHERE numero = ?");
        $stmt->execute([$pa_numero]);
        $result = $stmt->fetchColumn();
        return $result ?: '';
    } catch (Exception $e) {
        return '';
    }
}

// Função para buscar nome do usuário
function buscarNomeUsuario($usuario_id, $pdo_sicoob) {
    try {
        $stmt = $pdo_sicoob->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
        $stmt->execute([$usuario_id]);
        $result = $stmt->fetchColumn();
        return $result ?: '';
    } catch (Exception $e) {
        return '';
    }
}

try {
    // Receber filtros da URL (mesmos do gerenciar.php)
    $filtro_status = $_GET['status'] ?? '';
    $filtro_pa = $_GET['pa'] ?? '';
    $filtro_associado = $_GET['associado'] ?? '';
    $filtro_funcionario = $_GET['funcionario'] ?? '';
    $filtro_tecnico = $_GET['tecnico'] ?? '';
    $filtro_mes_renda = $_GET['mes_renda'] ?? '';
    $sort_column = $_GET['sort'] ?? 'data_cadastro';
    $sort_direction = $_GET['dir'] ?? 'desc';

    // Validar colunas de ordenação
    $valid_columns = [
        'pa' => 'cr.pa',
        'nome_cliente' => 'cr.nome_cliente',
        'data_ultima_atualizacao_renda' => 'cr.data_ultima_atualizacao_renda',
        'funcionario' => 'cr.funcionario',
        'data_solicitacao_laudo' => 'cr.data_solicitacao_laudo',
        'tecnico_responsavel' => 'cr.tecnico_responsavel',
        'data_atual_sisbr' => 'cr.data_atual_sisbr',
        'status' => 'cr.status',
        'data_cadastro' => 'cr.data_cadastro'
    ];

    $order_column = $valid_columns[$sort_column] ?? $valid_columns['data_cadastro'];
    $order_direction = strtolower($sort_direction) === 'asc' ? 'ASC' : 'DESC';

    // Construir query com filtros (SEM JOINs problemáticos)
    $where_conditions = [];
    $params = [];

    if (!empty($filtro_status)) {
        // O filtro já vem como ID do status (não nome)
        $where_conditions[] = "cr.status = ?";
        $params[] = $filtro_status;
    }

    if (!empty($filtro_pa)) {
        $where_conditions[] = "cr.pa = ?";
        $params[] = $filtro_pa;
    }

    if (!empty($filtro_associado)) {
        $where_conditions[] = "cr.nome_cliente LIKE ?";
        $params[] = "%$filtro_associado%";
    }

    if (!empty($filtro_funcionario)) {
        $where_conditions[] = "cr.funcionario = ?";
        $params[] = $filtro_funcionario;
    }

    if (!empty($filtro_tecnico)) {
        $where_conditions[] = "cr.tecnico_responsavel = ?";
        $params[] = $filtro_tecnico;
    }

    if (!empty($filtro_mes_renda)) {
        $where_conditions[] = "DATE_FORMAT(cr.data_ultima_atualizacao_renda, '%Y-%m') = ?";
        $params[] = $filtro_mes_renda;
    }

    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }

    // Query principal SEM JOINs problemáticos
    $sql = "
        SELECT 
            cr.id,
            cr.pa,
            cr.nome_cliente,
            cr.numero_cpf_cnpj,
            cr.data_ultima_atualizacao_renda,
            cr.funcionario,
            cr.data_solicitacao_laudo,
            cr.tecnico_responsavel,
            cr.data_atual_sisbr,
            cr.status,
            cr.data_cadastro,
            cr.observacoes
        FROM cad_registros cr
        $where_clause
        ORDER BY $order_column $order_direction
    ";

    $stmt = $pdo_mci->prepare($sql);
    $stmt->execute($params);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Buscar nomes dos status
    $stmt_status = $pdo_mci->query("SELECT id, nome FROM cad_status");
    $status_map = [];
    while ($row = $stmt_status->fetch()) {
        $status_map[$row['id']] = $row['nome'];
    }

    // Configurar headers para download do Excel
    $filename = 'relatorio_mci_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    // Abrir output para escrita
    $output = fopen('php://output', 'w');
    
    // Adicionar BOM para UTF-8 (para Excel reconhecer acentos)
    fwrite($output, "\xEF\xBB\xBF");

    // Cabeçalhos das colunas
    $headers = [
        'ID',
        'PA',
        'Nome do PA',
        'Nome do Cliente',
        'CPF/CNPJ',
        'Última Atualização Renda',
        'Funcionário ID',
        'Funcionário Nome',
        'Data Solicitação Laudo',
        'Técnico ID',
        'Técnico Nome',
        'Data Atualização SISBR',
        'Status',
        'Data Cadastro',
        'Observações'
    ];

    fputcsv($output, $headers, ';');

    // Dados dos registros
    foreach ($registros as $registro) {
        // Buscar nomes relacionados (sem JOIN para evitar conflito de collation)
        $nome_pa = buscarNomePA($registro['pa'], $pdo_sicoob);
        $nome_funcionario = $registro['funcionario'] ? buscarNomeUsuario($registro['funcionario'], $pdo_sicoob) : '';
        $nome_tecnico = $registro['tecnico_responsavel'] ? buscarNomeUsuario($registro['tecnico_responsavel'], $pdo_sicoob) : '';
        $nome_status = $status_map[$registro['status']] ?? '';

        $row = [
            $registro['id'],
            $registro['pa'],
            $nome_pa,
            $registro['nome_cliente'],
            formatarCpfCnpj($registro['numero_cpf_cnpj']),
            !empty($registro['data_ultima_atualizacao_renda']) ? date('d/m/Y', strtotime($registro['data_ultima_atualizacao_renda'])) : '',
            $registro['funcionario'] ?? '',
            $nome_funcionario,
            !empty($registro['data_solicitacao_laudo']) ? date('d/m/Y', strtotime($registro['data_solicitacao_laudo'])) : '',
            $registro['tecnico_responsavel'] ?? '',
            $nome_tecnico,
            !empty($registro['data_atual_sisbr']) ? date('d/m/Y', strtotime($registro['data_atual_sisbr'])) : '',
            $nome_status,
            !empty($registro['data_cadastro']) ? date('d/m/Y H:i:s', strtotime($registro['data_cadastro'])) : '',
            $registro['observacoes'] ?? ''
        ];

        fputcsv($output, $row, ';');
    }

    fclose($output);
    exit;

} catch (Exception $e) {
    // Em caso de erro, redirecionar de volta para gerenciar.php
    error_log("Erro no export Excel Real: " . $e->getMessage());
    header('Location: gerenciar.php?error=export_failed');
    exit;
}
?>
