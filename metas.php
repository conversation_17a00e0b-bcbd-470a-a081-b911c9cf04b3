<?php
require_once 'auth_check.php';

// Verificar se usuário tem permissão de gestor ou administrador
if (!in_array($_SESSION['mci_permission_level'], ['gestor', 'administrador'])) {
    header('Location: access_denied.php');
    exit;
}

// Determinar tipo de análise (cadastro ou tecagricola)
$tipo_analise = $_GET['tipo'] ?? 'cadastro';
if (!in_array($tipo_analise, ['cadastro', 'tecagricola'])) {
    $tipo_analise = 'cadastro';
}

// Processar ações POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_meta':
                $porcentagem = floatval($_POST['porcentagem_meta']);
                
                if ($porcentagem <= 0 || $porcentagem > 100) {
                    throw new Exception('Porcentagem deve estar entre 0.01 e 100');
                }
                
                // Desativar meta atual
                $stmt = $pdo_mci->prepare("UPDATE mci_metas SET ativo = FALSE WHERE ativo = TRUE");
                $stmt->execute();
                
                // Criar nova meta
                $stmt = $pdo_mci->prepare("
                    INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) 
                    VALUES (?, TRUE, ?)
                ");
                $stmt->execute([$porcentagem, $_SESSION['user_id']]);

                // Log para sistema centralizado
                require_once 'classes/Logger.php';
                $logger = new MciLogger();
                $detalhes_log = "MCI - Atualização de meta: Nova porcentagem definida para {$porcentagem}%";
                $logger->log('MCI - Configuração de meta', $detalhes_log);

                $message = "Meta atualizada para {$porcentagem}% com sucesso!";
                break;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Buscar meta atual
$stmt = $pdo_mci->prepare("SELECT porcentagem_meta FROM mci_metas WHERE ativo = TRUE ORDER BY id DESC LIMIT 1");
$stmt->execute();
$meta_atual = $stmt->fetch();
$porcentagem_meta = $meta_atual ? $meta_atual['porcentagem_meta'] : 75.00;

// Buscar dados da API Intranet para fotos (se necessário)
$usuarios_intranet = [];
if ($tipo_analise === 'cadastro') {
    try {
        require_once 'cadastro/config_api.php';
        require_once 'classes/Logger.php';

        $logger = new MciLogger();
        $intranetAPI = getIntranetAPI($logger);
        $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
    } catch (Exception $e) {
        // Se falhar, continua sem fotos
        $usuarios_intranet = [];
    }
}

// Inicializar variáveis
$funcionarios = [];
$tecnicos = [];

if ($tipo_analise === 'cadastro') {
    // Buscar dados dinâmicos dos funcionários considerando "Solicitado" e "Atualizado"
    $funcionarios_ids = [17, 18, 19, 20, 21, 22];
    $funcionarios_dados = [];

    foreach ($funcionarios_ids as $funcionario_id) {
        // Buscar dados do funcionário
        $query_funcionario = "
            SELECT u.id, u.nome_completo, u.email
            FROM sicoob_access_control.usuarios u
            WHERE u.id = ? AND u.ativo = TRUE
        ";

        $stmt = $pdo_mci->prepare($query_funcionario);
        $stmt->execute([$funcionario_id]);
        $funcionario = $stmt->fetch();

        if ($funcionario) {
            // Calcular métricas considerando apenas "Solicitação de Laudo" como desempenho para equipe de cadastro
            $query_metricas = "
                SELECT
                    COUNT(r.id) as total_registros,
                    SUM(CASE
                        WHEN r.data_solicitacao_laudo IS NOT NULL
                        AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                        THEN 1 ELSE 0
                    END) as atualizados_ano,
                    SUM(CASE
                        WHEN r.data_ultima_atualizacao_renda IS NOT NULL
                        AND DATE_FORMAT(DATE_ADD(r.data_ultima_atualizacao_renda, INTERVAL 11 MONTH), '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                        THEN 1 ELSE 0
                    END) as registros_vencendo_mes,
                    SUM(CASE
                        WHEN r.data_solicitacao_laudo IS NOT NULL
                        AND DATE_FORMAT(r.data_solicitacao_laudo, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                        THEN 1 ELSE 0
                    END) as atualizados_mes
                FROM cad_registros r
                WHERE r.funcionario = ?
                AND r.status NOT IN (SELECT id FROM cad_status WHERE nome = 'Removido')
            ";

            $stmt = $pdo_mci->prepare($query_metricas);
            $stmt->execute([$funcionario_id]);
            $metricas = $stmt->fetch();

            if ($metricas['total_registros'] > 0) {
                // Buscar dados da API Intranet
                $foto_url = null;
                $nome_api = $funcionario['nome_completo'];

                if (!empty($funcionario['email']) && !empty($usuarios_intranet)) {
                    $email_key = strtolower(trim($funcionario['email']));
                    $usuario_intranet = $usuarios_intranet[$email_key] ?? null;

                    if ($usuario_intranet) {
                        $foto_url = $usuario_intranet['foto_url'] ?? null;
                        // Usar nome da API quando disponível
                        if (!empty($usuario_intranet['nome'])) {
                            $nome_api = $usuario_intranet['nome'];
                        }
                    }
                }

                $funcionarios_dados[$funcionario_id] = [
                    'nome' => $nome_api,
                    'email' => $funcionario['email'],
                    'foto_url' => $foto_url,
                    'total' => $metricas['total_registros'],
                    'ano' => $metricas['atualizados_ano'],
                    'mes_vence' => $metricas['registros_vencendo_mes'],
                    'mes_atual' => $metricas['atualizados_mes']
                ];
            }
        }
    }

    $funcionarios = [];
    foreach ($funcionarios_dados as $id => $dados) {
        $funcionarios[] = [
            'id' => $id,
            'nome_completo' => $dados['nome'],
            'email' => $dados['email'],
            'foto_url' => $dados['foto_url'],
            'total_registros' => $dados['total'],
            'meta_anual' => ceil($dados['total'] * $porcentagem_meta / 100),
            'atualizados_ano' => $dados['ano'],
            'registros_vencendo_mes' => $dados['mes_vence'],
            'atualizados_mes' => $dados['mes_atual'],
            'meta_mensal' => ceil($dados['mes_vence'] * $porcentagem_meta / 100),
            'progresso_anual' => ceil($dados['total'] * $porcentagem_meta / 100) > 0 ?
                round(($dados['ano'] / ceil($dados['total'] * $porcentagem_meta / 100)) * 100, 1) : 0,
            'progresso_mensal' => ceil($dados['mes_vence'] * $porcentagem_meta / 100) > 0 ?
                round(($dados['mes_atual'] / ceil($dados['mes_vence'] * $porcentagem_meta / 100)) * 100, 1) : 0
        ];
    }

    // Ordenar por nome
    usort($funcionarios, function($a, $b) {
        return strcmp($a['nome_completo'], $b['nome_completo']);
    });

} else {
    // Lógica para técnicos agrícolas
    // IMPORTANTE: Filtro alinhado com sistema de cadastro - apenas registros do ano atual
    // Buscar dados da API Intranet para técnicos também
    try {
        require_once 'cadastro/config_api.php';
        require_once 'classes/Logger.php';

        $logger = new MciLogger();
        $intranetAPI = getIntranetAPI($logger);
        $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
    } catch (Exception $e) {
        $usuarios_intranet = [];
    }

    $stmt = $pdo_mci->prepare("
        SELECT
            u.id,
            u.nome_completo as nome,
            u.email,
            COUNT(CASE 
                WHEN YEAR(r.data_solicitacao_laudo) = YEAR(NOW()) 
                THEN 1 END) as meta_total,
            COUNT(CASE 
                WHEN s.nome = 'Atualizado' 
                AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                THEN 1 END) as atualizados_total,
            ROUND(
                (COUNT(CASE 
                    WHEN s.nome = 'Atualizado' 
                    AND YEAR(r.data_solicitacao_laudo) = YEAR(NOW())
                    THEN 1 END) / 
                 COUNT(CASE 
                    WHEN YEAR(r.data_solicitacao_laudo) = YEAR(NOW()) 
                    THEN 1 END)) * 100, 1
            ) as progresso_total
        FROM sicoob_access_control.usuarios u
        INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
        LEFT JOIN cad_status s ON r.status = s.id
        WHERE u.ativo = 1 
        AND r.tecnico_responsavel IS NOT NULL
        AND r.data_solicitacao_laudo IS NOT NULL
        GROUP BY u.id, u.nome_completo, u.email
        HAVING meta_total > 0
        ORDER BY meta_total DESC, u.nome_completo
    ");
    $stmt->execute();
    $tecnicos_dados = $stmt->fetchAll();

    foreach ($tecnicos_dados as $tecnico) {
        // Buscar dados da API Intranet para técnicos
        $foto_url = null;
        $nome_api = $tecnico['nome'];

        if (!empty($tecnico['email']) && !empty($usuarios_intranet)) {
            $email_key = strtolower(trim($tecnico['email']));
            $usuario_intranet = $usuarios_intranet[$email_key] ?? null;

            if ($usuario_intranet) {
                $foto_url = $usuario_intranet['foto_url'] ?? null;
                // Usar nome da API quando disponível
                if (!empty($usuario_intranet['nome'])) {
                    $nome_api = $usuario_intranet['nome'];
                }
            }
        }

        $tecnicos[] = [
            'id' => $tecnico['id'],
            'nome_completo' => $nome_api,
            'email' => $tecnico['email'],
            'foto_url' => $foto_url,
            'total_registros' => $tecnico['meta_total'],
            'meta_anual' => $tecnico['meta_total'], // Para técnicos, meta é o total de registros
            'atualizados_ano' => $tecnico['atualizados_total'],
            'progresso_anual' => $tecnico['progresso_total'],
            // Para técnicos não temos métricas mensais específicas
            'registros_vencendo_mes' => 0,
            'atualizados_mes' => 0,
            'meta_mensal' => 0,
            'progresso_mensal' => 0
        ];
    }
}

// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        <?php if ($tipo_analise === 'cadastro'): ?>
            Gestão de Metas e Equipe de Cadastro
        <?php else: ?>
            Análise de Técnicos Agrícolas
        <?php endif; ?>
        - <?php echo MCI_PROJECT_NAME; ?>
    </title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
        }

        body { background-color: #f8f9fa; }
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .btn-primary { background-color: var(--sicoob-verde-escuro); border-color: var(--sicoob-verde-escuro); }
        .btn-primary:hover { background-color: var(--sicoob-turquesa); border-color: var(--sicoob-turquesa); }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        
        .progress-card {
            transition: transform 0.2s ease;
        }
        
        .progress-card:hover {
            transform: translateY(-2px);
        }
        
        .meta-badge {
            font-size: 1.2rem;
            padding: 0.5rem 1rem;
        }
        
        .progress-bar-custom {
            background: linear-gradient(90deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
        }
        
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-left: 4px solid var(--sicoob-turquesa);
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .funcionario-name {
            font-weight: 600;
            color: var(--sicoob-verde-escuro);
        }
        
        .progress-text {
            font-size: 0.85rem;
            font-weight: 600;
        }

        /* Classes personalizadas Sicoob */
        .btn-sicoob-primary {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
        }

        .btn-sicoob-primary:hover {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white;
        }

        .btn-outline-sicoob {
            border-color: var(--sicoob-verde-escuro);
            color: var(--sicoob-verde-escuro);
            background-color: transparent;
        }

        .btn-outline-sicoob:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
        }

        .bg-sicoob-primary {
            background-color: var(--sicoob-verde-escuro) !important;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--sicoob-turquesa);
        }

        .user-avatar.placeholder {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <?php if ($tipo_analise === 'cadastro'): ?>
                    <i class="fas fa-chart-line"></i> <?php echo MCI_PROJECT_NAME; ?> - Metas e Equipe
                <?php else: ?>
                    <i class="fas fa-seedling"></i> <?php echo MCI_PROJECT_NAME; ?> - Técnicos Agrícolas
                <?php endif; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <!-- Atalhos para Dashboards -->
                <a class="nav-link" href="cadastro/dashboard.php" target="_blank">
                    <i class="fas fa-tv"></i> Dashboard Cadastro
                </a>
                <a class="nav-link" href="tecagricola/dashboard.php" target="_blank">
                    <i class="fas fa-seedling"></i> Dashboard Técnicos
                </a>

                <a class="nav-link" href="gerenciar.php">
                    <i class="fas fa-list"></i> Registros
                </a>
                <?php if ($_SESSION['mci_permission_level'] === 'administrador'): ?>
                <a class="nav-link" href="permissions.php">
                    <i class="fas fa-shield-alt"></i> Permissões
                </a>
                <?php endif; ?>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Alertas -->
        <?php if (isset($message)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Seletor de Tipo de Análise -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title mb-3">
                            <i class="fas fa-filter"></i> Tipo de Análise
                        </h6>
                        <div class="btn-group w-100" role="group">
                            <a href="?tipo=cadastro" class="btn <?php echo $tipo_analise === 'cadastro' ? 'btn-sicoob-primary' : 'btn-outline-sicoob'; ?>">
                                <i class="fas fa-users"></i> Equipe de Cadastro
                            </a>
                            <a href="?tipo=tecagricola" class="btn <?php echo $tipo_analise === 'tecagricola' ? 'btn-sicoob-primary' : 'btn-outline-sicoob'; ?>">
                                <i class="fas fa-seedling"></i> Técnicos Agrícolas
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card de Meta Atual -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="fas fa-target"></i>
                            <?php if ($tipo_analise === 'cadastro'): ?>
                                Meta Atual
                            <?php else: ?>
                                Meta dos Técnicos
                            <?php endif; ?>
                        </h5>
                        <?php if ($tipo_analise === 'cadastro'): ?>
                            <h2 class="text-primary">
                                <span class="badge bg-sicoob-primary meta-badge"><?php echo number_format($porcentagem_meta, 1); ?>%</span>
                            </h2>
                            <p class="text-muted">Porcentagem de cadastros a serem atualizados</p>
                            <button class="btn btn-primary btn-sm" onclick="editarMeta()">
                                <i class="fas fa-edit"></i> Alterar Meta
                            </button>
                        <?php else: ?>
                            <h2 class="text-primary">
                                <span class="badge bg-success meta-badge">100%</span>
                            </h2>
                            <p class="text-muted">Meta: Atualizar todos os registros atribuídos</p>
                            <small class="text-info">
                                <i class="fas fa-info-circle"></i>
                                Técnicos têm meta fixa de 100% dos registros
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <?php if ($tipo_analise === 'cadastro'): ?>
                                <i class="fas fa-users"></i> Equipe de Cadastro
                            <?php else: ?>
                                <i class="fas fa-seedling"></i> Técnicos Agrícolas
                            <?php endif; ?>
                        </h5>
                        <h2 class="text-primary">
                            <?php echo $tipo_analise === 'cadastro' ? count($funcionarios) : count($tecnicos); ?>
                        </h2>
                        <p class="text-muted">
                            <?php echo $tipo_analise === 'cadastro' ? 'Funcionários' : 'Técnicos'; ?> com registros designados
                        </p>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Atualizado em <?php echo date('d/m/Y H:i:s'); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabela de Funcionários e Metas -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <?php if ($tipo_analise === 'cadastro'): ?>
                        <i class="fas fa-chart-bar"></i> Progresso da Equipe de Cadastro (<?php echo count($funcionarios); ?> funcionários)
                    <?php else: ?>
                        <i class="fas fa-seedling"></i> Progresso dos Técnicos Agrícolas (<?php echo count($tecnicos); ?> técnicos)
                    <?php endif; ?>
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <?php if ($tipo_analise === 'cadastro'): ?>
                                    <th>Funcionário</th>
                                    <th>Total Registros</th>
                                    <th>Meta Mensal</th>
                                    <th>Progresso Mensal</th>
                                    <th>Meta Anual</th>
                                    <th>Progresso Anual</th>
                                <?php else: ?>
                                    <th>Técnico Agrícola</th>
                                    <th>Total Registros</th>
                                    <th>Meta</th>
                                    <th>Progresso</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($tipo_analise === 'cadastro'): ?>
                                <?php foreach ($funcionarios as $funcionario): ?>
                                <tr class="progress-card">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($funcionario['foto_url']): ?>
                                                <img src="<?php echo htmlspecialchars($funcionario['foto_url']); ?>"
                                                     alt="<?php echo htmlspecialchars($funcionario['nome_completo']); ?>"
                                                     class="user-avatar me-3">
                                            <?php else: ?>
                                                <div class="user-avatar placeholder me-3">
                                                    <?php echo strtoupper(substr($funcionario['nome_completo'], 0, 1)); ?>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="funcionario-name"><?php echo htmlspecialchars($funcionario['nome_completo']); ?></div>
                                                <small class="text-muted">ID: <?php echo $funcionario['id']; ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo number_format($funcionario['total_registros']); ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo $funcionario['meta_mensal']; ?></strong>
                                        <small class="text-muted d-block">
                                            (<?php echo number_format($porcentagem_meta, 1); ?>% de <?php echo $funcionario['registros_vencendo_mes']; ?>)
                                        </small>
                                    </td>
                                    <td>
                                        <div class="progress mb-1" style="height: 20px;">
                                            <div class="progress-bar progress-bar-custom" style="width: <?php echo min(100, $funcionario['progresso_mensal']); ?>%">
                                            </div>
                                        </div>
                                        <div class="progress-text">
                                            <?php echo $funcionario['atualizados_mes']; ?>/<?php echo $funcionario['meta_mensal']; ?>
                                            (<?php echo $funcionario['progresso_mensal']; ?>%)
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?php echo $funcionario['meta_anual']; ?></strong>
                                        <small class="text-muted d-block">
                                            (<?php echo number_format($porcentagem_meta, 1); ?>% de <?php echo $funcionario['total_registros']; ?>)
                                        </small>
                                    </td>
                                    <td>
                                        <div class="progress mb-1" style="height: 20px;">
                                            <div class="progress-bar progress-bar-custom" style="width: <?php echo min(100, $funcionario['progresso_anual']); ?>%">
                                            </div>
                                        </div>
                                        <div class="progress-text">
                                            <?php echo $funcionario['atualizados_ano']; ?>/<?php echo $funcionario['meta_anual']; ?>
                                            (<?php echo $funcionario['progresso_anual']; ?>%)
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <?php foreach ($tecnicos as $tecnico): ?>
                                <tr class="progress-card">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($tecnico['foto_url']): ?>
                                                <img src="<?php echo htmlspecialchars($tecnico['foto_url']); ?>"
                                                     alt="<?php echo htmlspecialchars($tecnico['nome_completo']); ?>"
                                                     class="user-avatar me-3">
                                            <?php else: ?>
                                                <div class="user-avatar placeholder me-3">
                                                    <?php echo strtoupper(substr($tecnico['nome_completo'], 0, 1)); ?>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="funcionario-name"><?php echo htmlspecialchars($tecnico['nome_completo']); ?></div>
                                                <small class="text-muted">ID: <?php echo $tecnico['id']; ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo number_format($tecnico['total_registros']); ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo $tecnico['meta_anual']; ?></strong>
                                        <small class="text-muted d-block">
                                            (100% dos registros atribuídos)
                                        </small>
                                    </td>
                                    <td>
                                        <div class="progress mb-1" style="height: 20px;">
                                            <div class="progress-bar progress-bar-custom" style="width: <?php echo min(100, $tecnico['progresso_anual']); ?>%">
                                            </div>
                                        </div>
                                        <div class="progress-text">
                                            <?php echo $tecnico['atualizados_ano']; ?>/<?php echo $tecnico['meta_anual']; ?>
                                            (<?php echo $tecnico['progresso_anual']; ?>%)
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Resumo Geral -->
        <div class="row mt-4">
            <?php
            if ($tipo_analise === 'cadastro') {
                $dados_resumo = $funcionarios;
            } else {
                $dados_resumo = $tecnicos;
            }

            $total_meta_anual = array_sum(array_column($dados_resumo, 'meta_anual'));
            $total_atualizados_ano = array_sum(array_column($dados_resumo, 'atualizados_ano'));
            $total_meta_mensal = array_sum(array_column($dados_resumo, 'meta_mensal'));
            $total_atualizados_mes = array_sum(array_column($dados_resumo, 'atualizados_mes'));

            $progresso_geral_anual = $total_meta_anual > 0 ? round(($total_atualizados_ano / $total_meta_anual) * 100, 1) : 0;
            $progresso_geral_mensal = $total_meta_mensal > 0 ? round(($total_atualizados_mes / $total_meta_mensal) * 100, 1) : 0;
            ?>

            <?php if ($tipo_analise === 'cadastro'): ?>
                <div class="col-md-6">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-calendar-week"></i> Progresso Mensal Geral
                            </h6>
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar progress-bar-custom" style="width: <?php echo min(100, $progresso_geral_mensal); ?>%">
                                    <?php echo $progresso_geral_mensal; ?>%
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><strong><?php echo number_format($total_atualizados_mes); ?></strong> atualizados</span>
                                <span>Meta: <strong><?php echo number_format($total_meta_mensal); ?></strong></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-calendar-alt"></i> Progresso Anual Geral
                            </h6>
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar progress-bar-custom" style="width: <?php echo min(100, $progresso_geral_anual); ?>%">
                                    <?php echo $progresso_geral_anual; ?>%
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><strong><?php echo number_format($total_atualizados_ano); ?></strong> atualizados</span>
                                <span>Meta: <strong><?php echo number_format($total_meta_anual); ?></strong></span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="col-12">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-seedling"></i> Progresso Geral dos Técnicos Agrícolas
                            </h6>
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar progress-bar-custom" style="width: <?php echo min(100, $progresso_geral_anual); ?>%">
                                    <?php echo $progresso_geral_anual; ?>%
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><strong><?php echo number_format($total_atualizados_ano); ?></strong> registros atualizados</span>
                                <span>Meta: <strong><?php echo number_format($total_meta_anual); ?></strong> registros</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Meta dos técnicos: 100% dos registros atribuídos com status "Atualizado"
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal Editar Meta (apenas para cadastro) -->
    <?php if ($tipo_analise === 'cadastro'): ?>
    <div class="modal fade" id="editMetaModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-target"></i> Definir Meta
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editMetaForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_meta">

                        <div class="mb-3">
                            <label class="form-label">Porcentagem da Meta</label>
                            <div class="input-group">
                                <input type="number" name="porcentagem_meta" id="porcentagem_meta"
                                       class="form-control" min="0.01" max="100" step="0.01"
                                       value="<?php echo $porcentagem_meta; ?>" required>
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="form-text">
                                Defina a porcentagem de cadastros que cada funcionário deve atualizar
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Como funciona:</h6>
                            <ul class="mb-0">
                                <li><strong>Meta Anual:</strong> Porcentagem aplicada sobre todos os registros do funcionário</li>
                                <li><strong>Meta Mensal:</strong> Porcentagem aplicada sobre registros que vencem no mês (11 meses após última atualização de renda)</li>
                                <li><strong>Exemplo:</strong> Registros atualizados em 07/2024 entram na meta de 06/2025</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Salvar Meta
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editarMeta() {
            new bootstrap.Modal(document.getElementById('editMetaModal')).show();
        }

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
