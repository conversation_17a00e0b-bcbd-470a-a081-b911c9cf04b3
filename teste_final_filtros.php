<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Final Filtros - MCI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        body { background-color: #f8f9fa; padding: 20px; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
        .card-header { background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); color: white; }
        .text-primary { color: var(--sicoob-verde-escuro) !important; }
        .problem { background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0; }
        .solution { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-primary mb-4">
            <i class="fas fa-check-circle"></i> 
            Correção Final - Filtros Status e PA
        </h1>
        
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> Problema Específico Identificado</h5>
            <p class="mb-0">
                <strong>Causa:</strong> Múltiplos campos `select[name="status"]` na página gerenciar.php<br>
                <strong>Conflito:</strong> Formulário de filtros vs Modal de edição<br>
                <strong>Resultado:</strong> JavaScript capturava campo errado
            </p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bug"></i> Problema Identificado
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="problem">
                            <h6><i class="fas fa-code"></i> Seletor Ambíguo</h6>
                            <p><strong>Código Anterior:</strong></p>
                            <pre class="small"><code>const formFiltros = document.querySelector('form[method="GET"]');
const status = formFiltros?.querySelector('select[name="status"]')?.value;</code></pre>
                            
                            <p><strong>Problema:</strong></p>
                            <ul class="small">
                                <li>Página tem múltiplos `form[method="GET"]`</li>
                                <li>Formulário de filtros: linha 889</li>
                                <li>Possível outro formulário GET na página</li>
                                <li>JavaScript capturava o primeiro encontrado</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tools"></i> Correção Aplicada
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="solution">
                            <h6><i class="fas fa-code"></i> Seletor Específico</h6>
                            <p><strong>Código Corrigido:</strong></p>
                            <pre class="small"><code>const formFiltros = document.querySelector('form[method="GET"].row.g-2.align-items-end');
const status = formFiltros?.querySelector('select[name="status"]')?.value;</code></pre>
                            
                            <p><strong>Solução:</strong></p>
                            <ul class="small">
                                <li>Seletor específico com classes CSS</li>
                                <li>Identifica exatamente o formulário de filtros</li>
                                <li>Evita conflito com outros formulários</li>
                                <li>Garante captura dos campos corretos</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-search"></i> Análise da Estrutura HTML
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-filter"></i> Formulário de Filtros (Linha 889)</h6>
                                <pre class="small"><code>&lt;form method="GET" class="row g-2 align-items-end"&gt;
    &lt;div class="col-lg-1 col-md-2"&gt;
        &lt;select name="status" class="form-select form-select-sm"&gt;
            &lt;option value=""&gt;Todos&lt;/option&gt;
            &lt;option value="Pendente"&gt;Pendente&lt;/option&gt;
            ...
        &lt;/select&gt;
    &lt;/div&gt;
    &lt;div class="col-lg-1 col-md-2"&gt;
        &lt;select name="pa" class="form-select form-select-sm"&gt;
            &lt;option value=""&gt;Todos&lt;/option&gt;
            &lt;option value="1"&gt;1&lt;/option&gt;
            ...
        &lt;/select&gt;
    &lt;/div&gt;
&lt;/form&gt;</code></pre>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-edit"></i> Modal de Edição (Linha 1208)</h6>
                                <pre class="small"><code>&lt;form method="POST" id="editForm"&gt;
    &lt;div class="col-md-6"&gt;
        &lt;select name="status" id="edit_status" class="form-select" disabled&gt;
            &lt;option value="1"&gt;Pendente&lt;/option&gt;
            &lt;option value="2"&gt;Solicitado&lt;/option&gt;
            ...
        &lt;/select&gt;
    &lt;/div&gt;
&lt;/form&gt;</code></pre>
                                <p class="small text-muted">
                                    ⚠️ Este formulário é POST, não GET, mas pode haver outros
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-play"></i> Teste a Correção Final
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-clipboard-list"></i> Procedimento de Teste:</h6>
                            <ol>
                                <li><strong>Acesse</strong> a página gerenciar.php</li>
                                <li><strong>Aplique filtro Status</strong> = "Pendente"</li>
                                <li><strong>Clique</strong> em "Filtrar" para aplicar</li>
                                <li><strong>Abra o console</strong> do navegador (F12)</li>
                                <li><strong>Clique</strong> em "Download Excel"</li>
                                <li><strong>Verifique no console</strong>:
                                    <ul>
                                        <li>Se "Formulário de filtros encontrado" mostra o formulário correto</li>
                                        <li>Se "Status" = "Pendente" nos filtros capturados</li>
                                    </ul>
                                </li>
                                <li><strong>Verifique o arquivo</strong> baixado contém apenas registros pendentes</li>
                            </ol>
                        </div>
                        
                        <div class="text-center">
                            <a href="gerenciar.php" class="btn btn-primary btn-lg me-3" target="_blank">
                                <i class="fas fa-list"></i> Testar na Página Gerenciar
                            </a>
                            <a href="debug_javascript_filtros.php" class="btn btn-info btn-lg" target="_blank">
                                <i class="fas fa-bug"></i> Debug JavaScript
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-list-check"></i> Checklist de Verificação
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-tag"></i> Filtro Status</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check1">
                                    <label class="form-check-label" for="check1">
                                        Console mostra Status capturado corretamente
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check2">
                                    <label class="form-check-label" for="check2">
                                        Download contém apenas registros do status selecionado
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <h6><i class="fas fa-building"></i> Filtro PA</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check3">
                                    <label class="form-check-label" for="check3">
                                        Console mostra PA capturado corretamente
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check4">
                                    <label class="form-check-label" for="check4">
                                        Download contém apenas registros do PA selecionado
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <h6><i class="fas fa-layer-group"></i> Outros Filtros</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check5">
                                    <label class="form-check-label" for="check5">
                                        Funcionário continua funcionando
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check6">
                                    <label class="form-check-label" for="check6">
                                        Associado continua funcionando
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check7">
                                    <label class="form-check-label" for="check7">
                                        Técnico continua funcionando
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-code"></i> Código JavaScript Final
                        </h6>
                    </div>
                    <div class="card-body">
                        <pre class="small"><code>function downloadExcel() {
    try {
        console.log('Iniciando download Excel...');
        
        // Construir URL com os filtros atuais da interface
        const params = new URLSearchParams();
        
        // Capturar filtros atuais dos campos da interface (especificamente do formulário de filtros)
        // Usar seletor mais específico para evitar conflito com modal
        const formFiltros = document.querySelector('form[method="GET"].row.g-2.align-items-end');
        const status = formFiltros?.querySelector('select[name="status"]')?.value || '';
        const pa = formFiltros?.querySelector('select[name="pa"]')?.value || '';
        const associado = formFiltros?.querySelector('input[name="associado"]')?.value || '';
        const funcionario = formFiltros?.querySelector('select[name="funcionario"]')?.value || '';
        const tecnico = formFiltros?.querySelector('select[name="tecnico"]')?.value || '';
        const mesRenda = formFiltros?.querySelector('select[name="mes_renda"]')?.value || '';
        
        // Capturar ordenação atual (das variáveis PHP)
        const sort = '<?php echo htmlspecialchars($sort_column); ?>';
        const dir = '<?php echo htmlspecialchars($sort_direction); ?>';
        
        console.log('Filtros capturados da interface:', {status, pa, associado, funcionario, tecnico, mesRenda, sort, dir});
        
        // Debug adicional
        console.log('Formulário de filtros encontrado:', formFiltros);
        console.log('Campos encontrados:', {
            statusField: formFiltros?.querySelector('select[name="status"]'),
            paField: formFiltros?.querySelector('select[name="pa"]'),
            associadoField: formFiltros?.querySelector('input[name="associado"]'),
            funcionarioField: formFiltros?.querySelector('select[name="funcionario"]'),
            tecnicoField: formFiltros?.querySelector('select[name="tecnico"]'),
            mesRendaField: formFiltros?.querySelector('select[name="mes_renda"]')
        });
        
        if (status) params.append('status', status);
        if (pa) params.append('pa', pa);
        if (associado) params.append('associado', associado);
        if (funcionario) params.append('funcionario', funcionario);
        if (tecnico) params.append('tecnico', tecnico);
        if (mesRenda) params.append('mes_renda', mesRenda);
        if (sort) params.append('sort', sort);
        if (dir) params.append('dir', dir);
        
        // Adicionar parâmetro para indicar que é download
        params.append('download', 'excel');
        
        // Construir URL final
        const finalUrl = 'export_excel.php?' + params.toString();
        console.log('URL final:', finalUrl);
        
        // Mostrar feedback visual
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando...';
        btn.disabled = true;
        
        // Redirecionar para download (usando export real para evitar problema de collation)
        const realUrl = finalUrl.replace('export_excel.php', 'export_excel_real.php');
        console.log('URL real (sem JOIN):', realUrl);
        window.location.href = realUrl;
        
        // Restaurar botão após um tempo
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 3000);
        
    } catch (error) {
        console.error('Erro no download Excel:', error);
        alert('Erro ao gerar relatório. Verifique o console para mais detalhes.');
    }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5><i class="fas fa-check-circle"></i> Correção Específica Aplicada!</h5>
            <p class="mb-0">
                O JavaScript foi corrigido com seletor mais específico para capturar exatamente o formulário de filtros, 
                evitando conflito com outros formulários na página. 
                <strong>Agora os filtros de Status e PA devem funcionar corretamente!</strong>
            </p>
        </div>
    </div>
</body>
</html>
