<?php
// Teste direto da API sem AJAX
require_once 'auth_check.php';

echo "<h1>🧪 Teste Direto da API get_funcionario_registros.php</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .info { color: #17a2b8; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 5px; border: 1px solid #ddd; }
    .card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
</style>";

try {
    // Buscar funcionários disponíveis
    echo "<div class='card'>";
    echo "<h2 class='info'>1. Funcionários Disponíveis</h2>";
    $stmt_funcionarios = $pdo_sicoob->prepare("
        SELECT u.id, u.nome_completo
        FROM usuarios u
        INNER JOIN usuario_setor us ON u.id = us.usuario_id
        WHERE us.setor_id = 8 AND u.ativo = TRUE
        ORDER BY u.nome_completo
    ");
    $stmt_funcionarios->execute();
    $funcionarios = $stmt_funcionarios->fetchAll();
    
    foreach ($funcionarios as $func) {
        echo "- ID: {$func['id']} - {$func['nome_completo']}<br>";
    }
    echo "</div>";

    // Testar com o primeiro funcionário
    if (count($funcionarios) > 0) {
        $funcionario_teste = $funcionarios[0];
        echo "<div class='card'>";
        echo "<h2 class='info'>2. Testando com: {$funcionario_teste['nome_completo']} (ID: {$funcionario_teste['id']})</h2>";
        
        $funcionario_id = $funcionario_teste['id'];
        
        // Buscar total de registros do funcionário
        $stmt_total = $pdo_mci->prepare("
            SELECT COUNT(*) as total
            FROM cad_registros cr
            WHERE cr.funcionario = ?
        ");
        $stmt_total->execute([$funcionario_id]);
        $total_registros = $stmt_total->fetchColumn();
        echo "<p class='success'>✅ Total de registros: $total_registros</p>";
        
        // Buscar registros disponíveis (não removidos)
        $stmt_disponiveis = $pdo_mci->prepare("
            SELECT COUNT(*) as disponiveis
            FROM cad_registros cr
            INNER JOIN cad_status cs ON cr.status = cs.id
            WHERE cr.funcionario = ? 
            AND cs.nome != 'Removido'
        ");
        $stmt_disponiveis->execute([$funcionario_id]);
        $registros_disponiveis = $stmt_disponiveis->fetchColumn();
        echo "<p class='success'>✅ Registros disponíveis: $registros_disponiveis</p>";
        
        // Buscar distribuição por status
        $stmt_status = $pdo_mci->prepare("
            SELECT cs.nome as status, COUNT(*) as quantidade
            FROM cad_registros cr
            INNER JOIN cad_status cs ON cr.status = cs.id
            WHERE cr.funcionario = ?
            GROUP BY cs.nome
            ORDER BY quantidade DESC
        ");
        $stmt_status->execute([$funcionario_id]);
        $distribuicao_status = $stmt_status->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Distribuição por Status:</h4>";
        foreach ($distribuicao_status as $status) {
            echo "- {$status['status']}: {$status['quantidade']} registros<br>";
        }
        
        // Buscar últimas atualizações
        echo "<h4>Testando query das últimas atualizações:</h4>";
        $stmt_ultimas = $pdo_mci->prepare("
            SELECT 
                cr.nome_cliente,
                cr.numero_cpf_cnpj,
                cs.nome as status,
                cr.data_atualizacao
            FROM cad_registros cr
            INNER JOIN cad_status cs ON cr.status = cs.id
            WHERE cr.funcionario = ?
            AND cs.nome != 'Removido'
            ORDER BY cr.data_atualizacao DESC
            LIMIT 5
        ");
        $stmt_ultimas->execute([$funcionario_id]);
        $ultimas_atualizacoes = $stmt_ultimas->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p class='success'>✅ Query das últimas atualizações executada com sucesso!</p>";
        echo "<p>Encontradas " . count($ultimas_atualizacoes) . " atualizações recentes.</p>";
        
        if (count($ultimas_atualizacoes) > 0) {
            echo "<h5>Últimas 5 atualizações:</h5>";
            foreach ($ultimas_atualizacoes as $atualizacao) {
                $cpf_formatado = strlen($atualizacao['numero_cpf_cnpj']) == 11 ? 
                    preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $atualizacao['numero_cpf_cnpj']) :
                    preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $atualizacao['numero_cpf_cnpj']);
                
                echo "- {$atualizacao['nome_cliente']} ($cpf_formatado) - Status: {$atualizacao['status']}<br>";
            }
        }
        
        // Simular resposta da API
        $response = [
            'success' => true,
            'data' => [
                'funcionario_id' => $funcionario_id,
                'total_registros' => intval($total_registros),
                'registros_disponiveis' => intval($registros_disponiveis),
                'registros_removidos' => intval($total_registros - $registros_disponiveis),
                'distribuicao_status' => $distribuicao_status,
                'ultimas_atualizacoes' => $ultimas_atualizacoes
            ]
        ];
        
        echo "<h4>Resposta JSON que seria retornada:</h4>";
        echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        echo "</div>";
        
        echo "<div class='card'>";
        echo "<h2 class='success'>✅ Teste Concluído com Sucesso!</h2>";
        echo "<p>A API está funcionando corretamente. O erro anterior foi corrigido.</p>";
        echo "<p><strong>Próximos passos:</strong></p>";
        echo "<ul>";
        echo "<li>✅ Teste a página principal: <a href='transferencia_massa.php' target='_blank'>transferencia_massa.php</a></li>";
        echo "<li>✅ Teste o AJAX: <a href='test_ajax.php' target='_blank'>test_ajax.php</a></li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<p class='error'>❌ Nenhum funcionário encontrado no setor 8</p>";
    }
    
} catch (Exception $e) {
    echo "<div class='card'>";
    echo "<h2 class='error'>❌ Erro no Teste</h2>";
    echo "<p>Erro: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>
