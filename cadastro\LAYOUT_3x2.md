# 📺 Dashboard MCI - Layout 3x2 para TV

## 🎯 Configuração Otimizada

O dashboard foi ajustado para **sempre exibir 6 funcionários** em um layout **3 colunas x 2 linhas**, garantindo que todos os cards apareçam completamente na tela sem necessidade de scroll.

## 📐 Estrutura do Layout

```
┌─────────────────────────────────────────────────────────┐
│                    HEADER DASHBOARD                     │
├─────────────────────────────────────────────────────────┤
│                  MÉTRICA GERAL ANUAL                    │
│                  [Barra de Progresso]                   │
├─────────────────────────────────────────────────────────┤
│  [Card 1]    │    [Card 2]    │    [Card 3]    │
│              │                │                │
├──────────────┼────────────────┼────────────────┤
│  [Card 4]    │    [Card 5]    │    [Card 6]    │
│              │                │                │
└─────────────────────────────────────────────────────────┘
```

## 🔧 Ajustes Implementados

### **1. Grid Fixo 3x2**
```css
.funcionarios-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 0.8rem;
    flex: 1;
    overflow: hidden;
    height: 100%;
}
```

### **2. Cards com Altura Fixa**
```css
.funcionario-card {
    height: 100%;
    min-height: 0;
    justify-content: center;
}
```

### **3. Elementos Compactos**
- **Avatar**: 70px (padrão) → 80px (1920px+) → 90px (4K)
- **Padding**: Reduzido para 1rem
- **Fontes**: Responsivas com `clamp()`
- **Margens**: Minimizadas

### **4. Sem Scroll**
```css
body {
    overflow: hidden;
    height: 100vh;
}
```

## 📊 Responsividade

### **Resolução Padrão (até 1920px)**
- Avatar: 70px
- Fonte nome: 0.8-1rem
- Fonte métrica: 0.9-1.1rem

### **Full HD+ (1920px+)**
- Avatar: 80px
- Fonte nome: até 1.1rem
- Fonte métrica: até 1.2rem

### **4K (3840px+)**
- Avatar: 90px
- Fonte nome: até 1.3rem
- Fonte métrica: até 1.4rem

### **Telas Menores (< 1200px)**
- Avatar: 60px
- Fontes reduzidas proporcionalmente

## 🎨 Elementos Visuais

### **Foto como Progresso**
- Borda circular preenchida conforme percentual
- Cálculo: `progresso_anual * 3.6` graus
- Cores: Gradiente Sicoob (turquesa)

### **Layout Compacto**
- Header: 1rem padding
- Cards: 1rem padding
- Gap: 0.8rem entre cards
- Margens: Minimizadas

## 📋 Funcionários Suportados

O sistema está configurado para **exatamente 6 funcionários**:
- IDs: [17, 18, 19, 20, 21, 22]
- Layout: 3 cards na linha superior, 3 na inferior
- Todos visíveis simultaneamente

## 🔍 Teste de Layout

### **Arquivo de Teste**
`/mci/cadastro/test_layout.html`

### **Como Testar**
1. Abra o arquivo de teste
2. Pressione F11 para fullscreen
3. Verifique se todos os 6 cards aparecem
4. Teste em diferentes resoluções

### **Indicadores de Tela**
- Canto superior esquerdo mostra resolução atual
- Viewport dimensions em tempo real

## 🚀 Configuração para TV

### **Passos Recomendados**
1. **Navegador**: Chrome ou Firefox
2. **Modo**: Fullscreen (F11)
3. **Zoom**: 100%
4. **Configuração TV**: Modo "PC" ou "Game"

### **Resoluções Testadas**
- ✅ 1920x1080 (Full HD)
- ✅ 2560x1440 (2K)
- ✅ 3840x2160 (4K)

### **Orientação**
- Apenas modo **Landscape** (horizontal)
- Layout não otimizado para Portrait

## 🎯 Métricas Exibidas

### **Card Individual**
- **Foto**: Com borda de progresso
- **Nome**: Funcionário
- **Meta Anual**: X/Y formato
- **Percentual**: XX.X%

### **Métrica Geral**
- **Título**: "Meta Anual da Equipe"
- **Valor**: Total da meta
- **Progresso**: Barra horizontal
- **Percentual**: Equipe completa

## 🔄 Auto-Refresh

- **Intervalo**: 5 minutos
- **Pausa**: Quando página não visível
- **Animação**: Efeito de pulse antes do refresh

## 📝 Notas Importantes

### **Limitações**
- Máximo 6 funcionários no layout atual
- Não responsivo para mobile (otimizado para TV)
- Requer resolução mínima 1024x768

### **Extensibilidade**
Para mais funcionários, seria necessário:
- Alterar para layout 4x2 (8 funcionários)
- Ou implementar paginação/carrossel
- Ou reduzir tamanho dos cards

---

**Dashboard MCI - Layout 3x2 Otimizado para TV**  
*Visualização completa sem scroll - 6 funcionários simultâneos*
