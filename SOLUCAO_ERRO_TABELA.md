# Solução para Erro: Table 'mci.mci_configuracoes' doesn't exist

## 🚨 Problema Identificado

O erro `Fatal error: Uncaught PDOException: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'mci.mci_configuracoes' doesn't exist` ocorre porque:

1. O código estava tentando acessar uma tabela `mci_configuracoes` que não existe
2. A tabela correta é `mci_metas`
3. O sistema não foi completamente instalado na outra máquina

## ✅ Soluções Implementadas

### 1. Correção do Código
- **Arquivo corrigido**: `mci/gerenciar.php` (linha 452)
- **Mudança**: Substituído `mci_configuracoes` por `mci_metas`
- **Proteção**: Adicionada verificação de existência da tabela
- **Fallback**: Valor padrão de 75% caso a tabela não exista

### 2. Script de Verificação Automática
- **Arquivo criado**: `mci/verificar_instalacao.php`
- **Função**: Verifica e corrige automaticamente problemas de instalação
- **Recursos**:
  - Verifica conexão com banco
  - Verifica existência de todas as tabelas necessárias
  - Cria tabela `mci_metas` se não existir
  - Insere meta padrão automaticamente
  - Verifica e cria diretórios necessários
  - Verifica permissões de escrita

## 🔧 Como Resolver na Outra Máquina

### Opção 1: Executar Script de Verificação (RECOMENDADO)
```
1. Acesse: http://localhost/d/mci/verificar_instalacao.php
2. O script irá:
   - Identificar problemas automaticamente
   - Corrigir o que for possível
   - Mostrar relatório detalhado
3. Siga as instruções mostradas na tela
```

### Opção 2: Criação Manual da Tabela
Se preferir criar manualmente, execute este SQL no banco `mci`:

```sql
-- Criar tabela mci_metas
CREATE TABLE IF NOT EXISTS mci_metas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    porcentagem_meta DECIMAL(5,2) NOT NULL COMMENT 'Porcentagem da meta (ex: 75.00 para 75%)',
    ativo BOOLEAN DEFAULT TRUE COMMENT 'Meta ativa/inativa',
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Data de criação',
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Data de atualização',
    usuario_criacao INT COMMENT 'ID do usuário que criou',
    usuario_atualizacao INT COMMENT 'ID do usuário que atualizou',
    
    INDEX idx_ativo (ativo),
    INDEX idx_data_criacao (data_criacao)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabela de configuração de metas do MCI';

-- Inserir meta padrão
INSERT INTO mci_metas (porcentagem_meta, ativo, usuario_criacao) 
VALUES (75.00, TRUE, 1);
```

### Opção 3: Executar Script de Criação Existente
```
1. Acesse: http://localhost/d/mci/criar_tabela_metas.php
2. Siga as instruções na tela
```

## 🔍 Verificações Pós-Correção

Após aplicar a solução, verifique:

1. **Teste de Conexão**:
   ```
   http://localhost/d/mci/test_connection.php
   ```

2. **Acesso ao Sistema**:
   ```
   http://localhost/d/mci/index.php
   ```

3. **Página de Gerenciamento**:
   ```
   http://localhost/d/mci/gerenciar.php
   ```

4. **Dashboards**:
   ```
   http://localhost/d/mci/cadastro/dashboard.php
   http://localhost/d/mci/tecagricola/dashboard.php
   ```

## 🛡️ Prevenção de Problemas Futuros

### 1. Verificação Automática
O código agora inclui verificações automáticas que:
- Verificam se a tabela existe antes de usá-la
- Usam valores padrão em caso de erro
- Não quebram o sistema se houver problemas

### 2. Script de Instalação Completa
Para novas instalações, sempre execute:
```
http://localhost/d/mci/verificar_instalacao.php
```

### 3. Documentação de Instalação
Consulte os arquivos:
- `mci/INSTALL.md` - Instruções completas de instalação
- `mci/README.md` - Documentação geral do sistema

## 📋 Checklist de Instalação

Para garantir instalação completa em qualquer máquina:

- [ ] Banco de dados `mci` criado
- [ ] Tabelas principais criadas (`cad_registros`, `cad_status`, `cad_logs`, etc.)
- [ ] Tabela `mci_metas` criada com meta padrão
- [ ] Diretórios `logs` e `uploads` criados com permissões
- [ ] Conexão com banco `sicoob_access_control` funcionando
- [ ] Sistema de autenticação funcionando
- [ ] Dashboards acessíveis

## 🆘 Suporte

Se o problema persistir:

1. Execute `verificar_instalacao.php` e anote os erros
2. Verifique os logs em `mci/logs/`
3. Teste a conexão com `test_connection.php`
4. Verifique se o banco `mci` existe e tem as permissões corretas

## 📝 Notas Técnicas

- **Tabela correta**: `mci_metas` (não `mci_configuracoes`)
- **Campo de meta**: `porcentagem_meta` (DECIMAL 5,2)
- **Meta padrão**: 75.00%
- **Proteção**: Código agora é resistente a tabelas ausentes
- **Fallback**: Sistema funciona mesmo sem a tabela de metas
