<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

echo "<h1>Corrigir Estrutura da Coluna Status</h1>";
echo "<hr>";

try {
    echo "<h3>1. Analisando estrutura atual...</h3>";
    
    // Verificar estrutura atual da coluna status
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field = 'status'");
    $stmt->execute();
    $column_info = $stmt->fetch();
    
    if (!$column_info) {
        echo "<p style='color: red;'>❌ Coluna status não encontrada!</p>";
        exit;
    }
    
    echo "<p><strong>Estrutura atual:</strong></p>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th></tr>";
    echo "<tr>";
    echo "<td>{$column_info['Field']}</td>";
    echo "<td><code>{$column_info['Type']}</code></td>";
    echo "<td>{$column_info['Null']}</td>";
    echo "<td>{$column_info['Key']}</td>";
    echo "<td>" . ($column_info['Default'] ?? 'NULL') . "</td>";
    echo "</tr>";
    echo "</table>";
    
    // Verificar se é ENUM
    $is_enum = strpos($column_info['Type'], 'enum') !== false;
    $is_int = strpos($column_info['Type'], 'int') !== false;
    
    echo "<h3>2. Verificando dados atuais...</h3>";
    
    // Mostrar distribuição atual
    $stmt = $pdo_mci->prepare("
        SELECT status, COUNT(*) as total 
        FROM cad_registros 
        GROUP BY status 
        ORDER BY status
    ");
    $stmt->execute();
    $distribuicao = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Valor Status</th><th>Total Registros</th></tr>";
    foreach ($distribuicao as $dist) {
        echo "<tr>";
        echo "<td><code>" . htmlspecialchars($dist['status'] ?? 'NULL') . "</code></td>";
        echo "<td><strong>{$dist['total']}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['corrigir_estrutura'])) {
        echo "<h3>3. Executando correção da estrutura...</h3>";
        
        // Passo 1: Remover chave estrangeira se existir
        echo "<p>Passo 1: Removendo chaves estrangeiras existentes...</p>";
        try {
            $stmt = $pdo_mci->prepare("
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = 'mci' 
                AND TABLE_NAME = 'cad_registros' 
                AND COLUMN_NAME = 'status' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            $stmt->execute();
            $constraints = $stmt->fetchAll();
            
            foreach ($constraints as $constraint) {
                $pdo_mci->exec("ALTER TABLE cad_registros DROP FOREIGN KEY {$constraint['CONSTRAINT_NAME']}");
                echo "<p>✅ Chave estrangeira {$constraint['CONSTRAINT_NAME']} removida</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Erro ao remover chaves estrangeiras: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Passo 2: Criar coluna temporária INT
        echo "<p>Passo 2: Criando coluna temporária...</p>";
        try {
            $pdo_mci->exec("ALTER TABLE cad_registros ADD COLUMN status_temp INT NOT NULL DEFAULT 1");
            echo "<p>✅ Coluna temporária criada</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao criar coluna temporária: " . htmlspecialchars($e->getMessage()) . "</p>";
            exit;
        }
        
        // Passo 3: Migrar dados
        echo "<p>Passo 3: Migrando dados...</p>";
        
        // Mapear valores ENUM para IDs
        $mapeamento = [
            'pendente' => 1,
            'em_andamento' => 1, // Mapear para Pendente
            'concluido' => 2,    // Mapear para Atualizado
            'cancelado' => 1,    // Mapear para Pendente
            'Pendente' => 1,
            'Atualizado' => 2
        ];
        
        foreach ($mapeamento as $valor_antigo => $id_novo) {
            try {
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros 
                    SET status_temp = ? 
                    WHERE status = ?
                ");
                $stmt->execute([$id_novo, $valor_antigo]);
                $affected = $stmt->rowCount();
                
                if ($affected > 0) {
                    echo "<p>✅ Migrados $affected registros de '$valor_antigo' para ID $id_novo</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Erro ao migrar '$valor_antigo': " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        // Migrar valores NULL ou vazios para Pendente
        try {
            $stmt = $pdo_mci->prepare("
                UPDATE cad_registros 
                SET status_temp = 1 
                WHERE status IS NULL OR status = ''
            ");
            $stmt->execute();
            $affected = $stmt->rowCount();
            
            if ($affected > 0) {
                echo "<p>✅ Migrados $affected registros NULL/vazios para Pendente (ID 1)</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Erro ao migrar valores NULL: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Migrar valores numéricos já corretos
        try {
            $stmt = $pdo_mci->prepare("
                UPDATE cad_registros 
                SET status_temp = CAST(status AS UNSIGNED)
                WHERE status REGEXP '^[0-9]+$' AND CAST(status AS UNSIGNED) IN (1, 2)
            ");
            $stmt->execute();
            $affected = $stmt->rowCount();
            
            if ($affected > 0) {
                echo "<p>✅ Migrados $affected registros numéricos válidos</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Erro ao migrar valores numéricos: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Passo 4: Remover coluna antiga
        echo "<p>Passo 4: Removendo coluna antiga...</p>";
        try {
            $pdo_mci->exec("ALTER TABLE cad_registros DROP COLUMN status");
            echo "<p>✅ Coluna antiga removida</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao remover coluna antiga: " . htmlspecialchars($e->getMessage()) . "</p>";
            exit;
        }
        
        // Passo 5: Renomear coluna temporária
        echo "<p>Passo 5: Renomeando coluna temporária...</p>";
        try {
            $pdo_mci->exec("ALTER TABLE cad_registros CHANGE status_temp status INT NOT NULL DEFAULT 1 COMMENT 'ID do status (referência para cad_status)'");
            echo "<p>✅ Coluna renomeada</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao renomear coluna: " . htmlspecialchars($e->getMessage()) . "</p>";
            exit;
        }
        
        // Passo 6: Adicionar chave estrangeira
        echo "<p>Passo 6: Adicionando chave estrangeira...</p>";
        try {
            $pdo_mci->exec("
                ALTER TABLE cad_registros 
                ADD CONSTRAINT fk_registros_status 
                FOREIGN KEY (status) REFERENCES cad_status(id) 
                ON UPDATE CASCADE ON DELETE RESTRICT
            ");
            echo "<p>✅ Chave estrangeira adicionada</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao adicionar chave estrangeira: " . htmlspecialchars($e->getMessage()) . "</p>";
            
            // Verificar se há valores inválidos
            $stmt = $pdo_mci->prepare("
                SELECT DISTINCT r.status 
                FROM cad_registros r 
                LEFT JOIN cad_status s ON r.status = s.id 
                WHERE s.id IS NULL
            ");
            $stmt->execute();
            $invalidos = $stmt->fetchAll();
            
            if (!empty($invalidos)) {
                echo "<p style='color: orange;'>⚠️ Valores inválidos encontrados:</p>";
                foreach ($invalidos as $inv) {
                    echo "<p>- Status: " . htmlspecialchars($inv['status']) . "</p>";
                }
                
                // Corrigir valores inválidos
                echo "<p>Corrigindo valores inválidos...</p>";
                $pdo_mci->exec("UPDATE cad_registros SET status = 1 WHERE status NOT IN (1, 2)");
                
                // Tentar adicionar chave estrangeira novamente
                try {
                    $pdo_mci->exec("
                        ALTER TABLE cad_registros 
                        ADD CONSTRAINT fk_registros_status 
                        FOREIGN KEY (status) REFERENCES cad_status(id) 
                        ON UPDATE CASCADE ON DELETE RESTRICT
                    ");
                    echo "<p>✅ Chave estrangeira adicionada após correção</p>";
                } catch (Exception $e2) {
                    echo "<p style='color: red;'>❌ Ainda não foi possível adicionar chave estrangeira: " . htmlspecialchars($e2->getMessage()) . "</p>";
                }
            }
        }
        
        // Passo 7: Adicionar índice
        echo "<p>Passo 7: Adicionando índice...</p>";
        try {
            $pdo_mci->exec("ALTER TABLE cad_registros ADD INDEX idx_status (status)");
            echo "<p>✅ Índice adicionado</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Índice já existe ou erro: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "<h3>4. Verificando resultado final...</h3>";
        
        // Verificar estrutura final
        $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field = 'status'");
        $stmt->execute();
        $column_final = $stmt->fetch();
        
        echo "<p><strong>Estrutura final:</strong></p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th></tr>";
        echo "<tr>";
        echo "<td>{$column_final['Field']}</td>";
        echo "<td><code>{$column_final['Type']}</code></td>";
        echo "<td>{$column_final['Null']}</td>";
        echo "<td>{$column_final['Key']}</td>";
        echo "<td>" . ($column_final['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
        echo "</table>";
        
        // Verificar chave estrangeira
        $stmt = $pdo_mci->prepare("
            SELECT CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'mci' 
            AND TABLE_NAME = 'cad_registros' 
            AND COLUMN_NAME = 'status' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $stmt->execute();
        $fk_final = $stmt->fetch();
        
        if ($fk_final) {
            echo "<p style='color: green;'><strong>Chave Estrangeira:</strong> ✅ {$fk_final['CONSTRAINT_NAME']} → {$fk_final['REFERENCED_TABLE_NAME']}.{$fk_final['REFERENCED_COLUMN_NAME']}</p>";
        } else {
            echo "<p style='color: red;'><strong>Chave Estrangeira:</strong> ❌ Não configurada</p>";
        }
        
        // Mostrar distribuição final
        echo "<h4>Distribuição final dos dados:</h4>";
        $stmt = $pdo_mci->prepare("
            SELECT 
                r.status,
                s.nome as status_nome,
                s.cor as status_cor,
                COUNT(*) as total
            FROM cad_registros r
            LEFT JOIN cad_status s ON r.status = s.id
            GROUP BY r.status, s.nome, s.cor
            ORDER BY r.status
        ");
        $stmt->execute();
        $distribuicao_final = $stmt->fetchAll();
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID Status</th><th>Nome Status</th><th>Total Registros</th></tr>";
        foreach ($distribuicao_final as $dist) {
            if ($dist['status_nome']) {
                $status_display = "<span style='background-color: {$dist['status_cor']}; color: white; padding: 2px 8px; border-radius: 3px;'>{$dist['status_nome']}</span>";
            } else {
                $status_display = "<em style='color: red;'>Status inválido</em>";
            }
            
            echo "<tr>";
            echo "<td><code>{$dist['status']}</code></td>";
            echo "<td>$status_display</td>";
            echo "<td><strong>{$dist['total']}</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724;'>🎉 Estrutura Corrigida com Sucesso!</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ Coluna status convertida para INT NOT NULL</li>";
        echo "<li>✅ Chave estrangeira configurada</li>";
        echo "<li>✅ Dados migrados corretamente</li>";
        echo "<li>✅ Índice adicionado</li>";
        echo "<li>✅ Padrão definido como 1 (Pendente)</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        // Mostrar formulário de correção
        echo "<h3>3. Correção Necessária</h3>";
        
        if ($is_enum) {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4 style='color: #721c24;'>❌ Estrutura Incorreta Detectada</h4>";
            echo "<p style='color: #721c24;'>A coluna status está como ENUM, mas deveria ser INT com chave estrangeira.</p>";
            echo "<ul style='color: #721c24;'>";
            echo "<li>Tipo atual: ENUM - ❌ Incorreto</li>";
            echo "<li>Tipo necessário: INT NOT NULL - ✅ Correto</li>";
            echo "<li>Chave estrangeira: ❌ Não configurada</li>";
            echo "<li>Permite NULL: ❌ Não deveria permitir</li>";
            echo "</ul>";
            echo "</div>";
        } elseif ($is_int) {
            echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<h4 style='color: #856404;'>⚠️ Estrutura Parcialmente Correta</h4>";
            echo "<p style='color: #856404;'>A coluna é INT, mas pode estar faltando a chave estrangeira.</p>";
            echo "</div>";
        }
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🔧 Esta correção irá:</h4>";
        echo "<ul>";
        echo "<li>Converter a coluna status para INT NOT NULL DEFAULT 1</li>";
        echo "<li>Migrar todos os dados existentes para usar IDs (1, 2)</li>";
        echo "<li>Configurar chave estrangeira para cad_status</li>";
        echo "<li>Adicionar índice para performance</li>";
        echo "<li>Garantir que não aceite valores NULL</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<div style='margin: 20px 0;'>";
        echo "<label>";
        echo "<input type='checkbox' name='corrigir_estrutura' required> ";
        echo "<strong>Eu confirmo que desejo corrigir a estrutura da coluna status</strong>";
        echo "</label>";
        echo "</div>";
        
        echo "<button type='submit' style='background-color: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold;'>🔧 CORRIGIR ESTRUTURA</button>";
        echo " ";
        echo "<a href='validar_status_sistema.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>❌ Cancelar</a>";
        echo "</form>";
    }
    
    echo "<hr>";
    echo "<p><a href='validar_status_sistema.php'>Validar sistema</a> | <a href='gerenciar.php'>Ver registros</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a correção</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
