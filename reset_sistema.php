<?php
require_once 'auth_check.php';
require_once 'classes/Logger.php';

// Verificar se é admin
checkMciAccess('admin');

$logger = new MciLogger();

echo "<h1>Reset do Sistema MCI</h1>";
echo "<hr>";
echo "<div style='color: red; font-weight: bold;'>⚠️ ATENÇÃO: Esta ação irá apagar TODOS os dados do sistema!</div>";
echo "<hr>";

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirmar_reset'])) {
    try {
        echo "<h3>Executando reset do sistema...</h3>";
        
        // Log da ação
        $logger->log('Reset do sistema', 'Usuário iniciou reset completo do sistema MCI');
        
        // Desabilitar verificação de chaves estrangeiras temporariamente
        $pdo_mci->exec("SET FOREIGN_KEY_CHECKS = 0");
        
        // Limpar tabelas na ordem correta
        echo "<p>🗑️ Limpando tabela cad_erros_importacao...</p>";
        $pdo_mci->exec("DELETE FROM cad_erros_importacao");
        
        echo "<p>🗑️ Limpando tabela cad_logs...</p>";
        $pdo_mci->exec("DELETE FROM cad_logs");
        
        echo "<p>🗑️ Limpando tabela cad_importacoes...</p>";
        $pdo_mci->exec("DELETE FROM cad_importacoes");
        
        echo "<p>🗑️ Limpando tabela cad_registros...</p>";
        $pdo_mci->exec("DELETE FROM cad_registros");
        
        // Resetar auto_increment
        echo "<p>🔄 Resetando contadores...</p>";
        $pdo_mci->exec("ALTER TABLE cad_registros AUTO_INCREMENT = 1");
        $pdo_mci->exec("ALTER TABLE cad_logs AUTO_INCREMENT = 1");
        $pdo_mci->exec("ALTER TABLE cad_importacoes AUTO_INCREMENT = 1");
        $pdo_mci->exec("ALTER TABLE cad_erros_importacao AUTO_INCREMENT = 1");
        
        // Reabilitar verificação de chaves estrangeiras
        $pdo_mci->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        // Limpar arquivos de upload (opcional)
        if (isset($_POST['limpar_uploads'])) {
            echo "<p>🗑️ Limpando arquivos de upload...</p>";
            $upload_dir = 'uploads/';
            if (is_dir($upload_dir)) {
                $files = glob($upload_dir . '*');
                foreach ($files as $file) {
                    if (is_file($file) && basename($file) != '.htaccess') {
                        unlink($file);
                    }
                }
            }
        }
        
        // Limpar logs antigos (opcional)
        if (isset($_POST['limpar_logs'])) {
            echo "<p>🗑️ Limpando logs antigos...</p>";
            $log_dir = 'logs/';
            if (is_dir($log_dir)) {
                $files = glob($log_dir . '*.log');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
            }
        }
        
        echo "<hr>";
        echo "<h3 style='color: green;'>✅ Reset concluído com sucesso!</h3>";
        echo "<p>Todas as tabelas foram limpas e os contadores resetados.</p>";
        echo "<p><a href='index.php'>Voltar ao sistema</a></p>";
        
        // Log final
        $logger->logFile("Reset do sistema concluído com sucesso", "INFO");
        
    } catch (Exception $e) {
        echo "<h3 style='color: red;'>❌ Erro durante o reset</h3>";
        echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
        $logger->logFile("Erro durante reset: " . $e->getMessage(), "ERROR");
    }
    
} else {
    // Mostrar estatísticas atuais
    try {
        echo "<h3>Estatísticas Atuais</h3>";
        
        $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_registros");
        $stmt->execute();
        $total_registros = $stmt->fetchColumn();
        
        $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_importacoes");
        $stmt->execute();
        $total_importacoes = $stmt->fetchColumn();
        
        $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_logs");
        $stmt->execute();
        $total_logs = $stmt->fetchColumn();
        
        $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_erros_importacao");
        $stmt->execute();
        $total_erros = $stmt->fetchColumn();
        
        echo "<table border='1' cellpadding='10'>";
        echo "<tr><th>Tabela</th><th>Registros</th></tr>";
        echo "<tr><td>cad_registros</td><td>$total_registros</td></tr>";
        echo "<tr><td>cad_importacoes</td><td>$total_importacoes</td></tr>";
        echo "<tr><td>cad_logs</td><td>$total_logs</td></tr>";
        echo "<tr><td>cad_erros_importacao</td><td>$total_erros</td></tr>";
        echo "</table>";
        
        // Contar arquivos
        $upload_files = 0;
        if (is_dir('uploads/')) {
            $files = glob('uploads/*');
            $upload_files = count(array_filter($files, 'is_file'));
        }
        
        $log_files = 0;
        if (is_dir('logs/')) {
            $files = glob('logs/*.log');
            $log_files = count($files);
        }
        
        echo "<p><strong>Arquivos de upload:</strong> $upload_files</p>";
        echo "<p><strong>Arquivos de log:</strong> $log_files</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Erro ao buscar estatísticas: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Formulário de confirmação
    echo "<hr>";
    echo "<h3>Confirmar Reset</h3>";
    echo "<form method='POST'>";
    echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ Esta ação irá:</h4>";
    echo "<ul>";
    echo "<li>Apagar TODOS os registros de atualizações cadastrais</li>";
    echo "<li>Apagar TODOS os logs do sistema MCI</li>";
    echo "<li>Apagar TODAS as importações registradas</li>";
    echo "<li>Apagar TODOS os erros de importação</li>";
    echo "<li>Resetar os contadores das tabelas</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><input type='checkbox' name='limpar_uploads'> Limpar também arquivos de upload</label><br>";
    echo "<label><input type='checkbox' name='limpar_logs'> Limpar também arquivos de log</label>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<label>";
    echo "<input type='checkbox' name='confirmar_reset' required> ";
    echo "<strong>Eu entendo que esta ação é irreversível e confirmo que desejo apagar todos os dados</strong>";
    echo "</label>";
    echo "</div>";
    
    echo "<button type='submit' style='background-color: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold;'>🗑️ EXECUTAR RESET</button>";
    echo " ";
    echo "<a href='index.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>❌ Cancelar</a>";
    echo "</form>";
}

echo "<hr>";
echo "<p><strong>Nota:</strong> Apenas administradores podem executar esta ação.</p>";
?>
