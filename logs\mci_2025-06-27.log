[2025-06-27 00:00:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:00:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:00:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:00:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:01:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:01:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:01:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:01:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:04:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:04:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:04:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:04:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:05:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:05:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:05:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:05:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:05:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:05:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:06:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:06:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:08:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:08:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:09:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:09:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:10:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:10:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:10:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:10:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:10:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:10:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:11:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:11:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:13:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:13:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:14:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:14:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:14:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:14:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:14:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:14:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:15:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:15:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:16:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:16:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:19:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:19:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:19:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:19:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:20:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:20:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:20:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:20:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:20:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:20:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:21:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:21:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:23:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:23:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:24:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:24:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:24:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:24:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:25:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:25:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:25:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:25:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:26:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:26:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:28:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:28:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:29:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:29:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:29:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:29:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:29:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:29:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:30:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:30:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:30:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:30:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:33:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:33:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:33:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:33:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:34:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:34:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:34:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:34:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:35:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:35:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:35:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:35:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:38:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:38:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:38:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:38:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:39:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:39:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:39:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:39:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:39:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:39:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:40:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:40:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:43:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:43:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:43:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:43:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:44:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:44:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:44:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:44:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:44:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:44:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:45:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:45:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:47:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:47:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:48:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:48:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:48:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:48:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:48:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:48:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:49:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:49:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:50:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:50:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:52:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:52:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:53:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:53:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:53:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:53:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:53:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:53:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:54:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:54:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:54:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:54:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:57:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:57:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:57:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:57:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:58:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:58:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:58:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:58:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:59:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:59:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 00:59:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 00:59:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:02:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:02:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:02:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:02:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:03:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:03:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:03:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:03:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:03:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:03:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:04:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:04:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:07:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:07:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:07:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:07:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:08:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:08:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:08:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:08:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:08:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:08:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:09:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:09:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:11:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:11:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:12:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:12:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:12:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:12:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:13:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:13:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:13:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:13:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:14:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:14:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:16:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:16:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:17:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:17:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:17:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:17:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:17:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:17:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:18:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:18:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:18:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:18:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:21:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:21:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:21:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:21:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:22:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:22:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:22:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:22:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:23:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:23:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:23:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:23:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:26:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:26:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:26:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:26:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:27:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:27:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:27:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:27:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:27:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:27:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:28:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:28:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:31:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:31:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:31:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:31:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:32:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:32:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:32:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:32:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:32:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:32:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:33:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:33:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:35:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:35:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:36:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:36:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:36:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:36:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:37:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:37:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:37:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:37:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:38:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:38:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:40:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:40:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:41:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:41:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:41:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:41:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:41:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:41:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:42:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:42:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:42:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:42:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:45:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:45:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:45:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:45:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:46:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:46:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:46:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:46:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:47:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:47:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:47:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:47:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:50:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:50:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:50:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:50:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:51:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:51:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:51:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:51:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:51:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:51:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:52:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:52:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:55:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:55:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:55:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:55:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:56:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:56:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:56:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:56:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:56:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:56:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:57:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:57:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 01:59:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 01:59:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:00:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:00:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:00:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:00:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:01:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:01:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:01:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:01:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:02:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:02:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:04:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:04:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:05:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:05:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:05:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:05:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:05:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:05:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:06:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:06:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:06:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:06:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:09:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:09:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:09:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:09:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:10:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:10:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:10:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:10:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:11:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:11:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:11:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:11:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:14:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:14:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:14:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:14:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:15:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:15:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:15:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:15:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:15:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:15:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:16:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:16:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:19:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:19:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:19:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:19:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:20:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:20:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:20:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:20:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:20:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:20:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:21:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:21:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:23:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:23:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:24:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:24:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:24:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:24:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:25:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:25:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:25:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:25:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:26:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:26:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:28:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:28:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:29:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:29:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:29:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:29:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:29:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:29:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:30:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:30:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:30:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:30:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:33:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:33:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:33:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:33:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:34:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:34:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:34:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:34:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:35:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:35:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:35:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:35:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:38:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:38:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:38:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:38:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:39:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:39:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:39:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:39:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:39:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:39:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:40:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:40:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:43:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:43:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:43:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:43:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:44:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:44:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:44:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:44:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:44:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:44:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:45:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:45:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:47:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:47:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:48:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:48:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:48:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:48:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:49:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:49:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:49:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:49:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:50:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:50:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:52:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:52:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:53:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:53:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:53:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:53:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:53:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:53:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:54:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:54:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:54:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:54:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:57:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:57:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:57:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:57:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:58:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:58:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:58:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:58:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:59:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:59:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 02:59:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 02:59:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:02:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:02:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:02:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:02:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:03:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:03:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:03:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:03:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:03:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:03:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:04:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:04:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:07:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:07:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:07:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:07:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:08:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:08:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:08:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:08:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:08:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:08:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:09:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:09:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:11:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:11:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:12:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:12:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:12:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:12:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:13:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:13:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:13:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:13:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:14:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:14:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:16:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:16:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:17:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:17:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:17:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:17:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:17:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:17:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:18:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:18:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:18:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:18:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:21:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:21:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:21:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:21:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:22:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:22:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:22:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:22:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:23:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:23:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:23:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:23:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:26:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:26:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:26:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:26:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:27:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:27:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:27:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:27:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:27:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:27:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:28:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:28:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:31:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:31:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:31:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:31:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:32:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:32:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:32:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:32:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:32:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:32:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:33:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:33:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:35:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:35:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:36:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:36:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:36:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:36:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:37:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:37:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:37:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:37:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:38:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:38:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:40:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:40:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:41:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:41:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:41:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:41:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:41:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:41:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:42:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:42:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:42:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:42:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:45:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:45:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:45:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:45:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:46:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:46:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:46:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:46:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:47:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:47:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:47:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:47:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:50:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:50:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:50:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:50:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:51:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:51:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:51:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:51:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:51:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:51:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:52:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:52:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:55:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:55:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:55:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:55:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:56:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:56:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:56:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:56:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:56:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:56:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:57:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:57:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 03:59:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 03:59:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:00:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:00:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:00:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:00:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:01:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:01:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:01:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:01:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:02:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:02:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:04:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:04:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:05:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:05:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:05:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:05:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:05:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:05:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:06:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:06:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:06:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:06:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:09:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:09:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:09:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:09:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:10:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:10:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:10:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:10:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:11:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:11:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:11:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:11:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:14:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:14:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:14:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:14:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:15:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:15:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:15:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:15:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:15:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:15:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:16:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:16:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:19:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:19:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:19:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:19:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:20:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:20:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:20:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:20:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:20:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:20:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:21:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:21:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:23:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:23:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:24:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:24:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:24:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:24:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:25:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:25:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:25:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:25:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:26:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:26:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:28:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:28:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:29:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:29:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:29:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:29:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:29:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:29:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:30:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:30:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:30:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:30:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:33:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:33:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:33:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:33:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:34:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:34:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:34:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:34:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:35:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:35:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:35:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:35:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:38:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:38:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:38:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:38:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:39:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:39:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:39:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:39:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:39:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:39:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:40:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:40:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:43:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:43:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:43:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:43:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:44:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:44:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:44:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:44:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:44:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:44:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:45:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:45:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:47:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:47:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:48:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:48:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:48:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:48:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:49:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:49:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:49:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:49:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:50:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:50:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:52:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:52:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:53:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:53:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:53:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:53:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:53:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:53:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:54:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:54:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:54:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:54:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:57:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:57:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:57:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:57:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:58:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:58:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:58:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:58:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:59:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:59:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 04:59:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 04:59:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:02:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:02:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:02:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:02:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:03:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:03:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:03:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:03:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:03:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:03:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:04:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:04:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:07:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:07:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:07:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:07:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:08:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:08:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:08:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:08:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:08:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:08:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:09:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:09:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:11:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:11:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:12:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:12:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:12:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:12:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:13:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:13:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:13:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:13:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:14:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:14:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:16:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:16:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:17:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:17:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:17:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:17:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:17:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:17:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:18:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:18:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:18:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:18:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:21:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:21:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:21:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:21:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:22:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:22:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:22:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:22:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:23:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:23:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:23:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:23:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:26:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:26:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:26:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:26:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:27:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:27:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:27:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:27:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:27:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:27:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:28:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:28:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:31:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:31:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:31:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:31:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:32:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:32:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:32:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:32:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:32:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:32:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:33:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:33:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:35:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:35:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:36:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:36:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:36:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:36:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:37:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:37:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:37:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:37:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:38:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:38:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:40:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:40:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:41:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:41:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:41:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:41:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:41:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:41:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:42:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:42:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:42:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:42:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:45:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:45:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:45:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:45:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:46:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:46:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:46:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:46:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:47:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:47:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:47:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:47:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:50:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:50:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:50:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:50:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:51:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:51:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:51:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:51:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:51:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:51:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:52:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:52:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:55:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:55:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:55:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:55:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:56:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:56:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:56:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:56:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:56:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:56:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:57:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:57:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 05:59:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 05:59:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:00:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:00:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:00:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:00:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:01:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:01:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:01:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:01:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:02:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:02:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:04:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:04:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:05:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:05:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:05:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:05:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:05:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:05:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:06:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:06:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:06:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:06:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:09:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:09:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:09:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:09:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:10:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:10:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:10:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:10:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:11:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:11:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:11:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:11:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:14:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:14:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:14:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:14:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:15:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:15:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:15:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:15:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:15:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:15:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:16:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:16:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:19:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:19:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:19:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:19:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:20:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:20:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:20:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:20:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:20:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:20:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:21:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:21:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:23:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:23:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:24:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:24:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:24:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:24:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:25:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:25:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:25:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:25:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:26:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:26:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:28:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:28:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:29:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:29:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:29:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:29:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:29:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:29:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:30:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:30:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:30:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:30:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:33:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:33:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:33:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:33:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:34:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:34:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:34:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:34:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:35:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:35:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:35:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:35:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:38:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:38:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:38:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:38:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:39:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:39:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:39:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:39:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:39:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:39:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:40:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:40:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:43:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:43:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:43:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:43:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:44:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:44:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:44:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:44:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:44:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:44:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:45:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:45:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:47:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:47:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:48:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:48:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:48:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:48:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:49:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:49:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:49:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:49:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:50:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:50:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:52:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:52:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:53:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:53:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:53:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:53:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:53:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:53:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:54:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:54:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:54:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:54:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:57:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:57:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:57:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:57:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:58:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:58:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:58:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:58:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:59:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:59:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 06:59:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 06:59:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:02:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:02:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:02:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:02:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:03:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:03:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:03:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:03:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:03:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:03:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:04:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:04:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:07:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:07:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:07:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:07:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:08:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:08:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:08:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:08:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:08:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:08:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:09:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:09:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:11:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:11:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:12:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:12:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:12:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:12:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:13:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:13:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:13:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:13:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:14:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:14:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:16:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:16:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:17:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:17:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:17:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:17:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:17:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:17:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:18:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:18:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:18:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:18:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:21:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:21:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:21:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:21:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:22:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:22:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:22:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:22:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:23:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:23:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:23:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:23:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:26:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:26:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:26:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:26:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:27:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:27:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:27:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:27:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:27:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:27:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:28:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:28:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:31:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:31:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:31:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:31:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:32:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:32:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:32:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:32:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:32:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:32:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:33:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:33:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:35:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:35:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:36:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:36:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:36:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:36:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:37:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:37:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:37:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:37:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:38:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:38:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:40:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:40:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:41:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:41:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:41:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:41:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:41:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:41:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:42:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:42:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:42:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:42:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:45:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:45:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:46:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:46:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:46:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:46:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:46:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:46:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:47:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:47:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:47:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:47:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:50:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:50:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:50:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:50:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:51:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:51:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:51:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:51:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:51:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:51:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:52:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:52:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:55:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:55:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:55:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:55:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:56:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:56:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:56:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:56:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:56:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:56:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:57:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:57:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 07:57:47] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40202 | Funcionário: Cristiano Prazer | Solicitação Laudo: 15/04/2025 | Técnico: Maycon de Souza Dias | SISBR: 08/05/2025
[2025-06-27 07:59:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 07:59:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:00:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:00:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:00:40] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40230 | Funcionário: Cristiano Prazer | Solicitação Laudo: 22/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 29/04/2025
[2025-06-27 08:00:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:00:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:01:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:01:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:01:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:01:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:02:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:02:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:02:59] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40248 | Funcionário: Cristiano Prazer | Solicitação Laudo: 29/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 19/05/2025
[2025-06-27 08:03:42] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40232 | Funcionário: Cristiano Prazer | Solicitação Laudo: 29/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 21/05/2025
[2025-06-27 08:04:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:04:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:05:13] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40281 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 20/05/2025
[2025-06-27 08:05:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:05:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:05:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:05:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:05:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:05:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:06:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:06:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:06:42] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40283 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 22/05/2025
[2025-06-27 08:06:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:06:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:09:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:09:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:10:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:10:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:10:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:10:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:10:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:10:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:11:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:11:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:11:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:11:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:14:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:14:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:14:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:14:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:15:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:15:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:15:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:15:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:16:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:16:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:16:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:16:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:19:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:19:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:19:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:19:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:20:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:20:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:20:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:20:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:20:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:20:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:21:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:21:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:23:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:23:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:24:24] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40308 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 27/05/2025
[2025-06-27 08:24:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:24:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:24:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:24:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:25:02] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40307 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 21/05/2025
[2025-06-27 08:25:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:25:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:25:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:25:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:26:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:26:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:27:04] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40343 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 27/05/2025
[2025-06-27 08:27:09] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: DEGMAR ALVES TEIXEIRA (CPF/CNPJ: 06945629635) | Justificativa: ATUALIZADO EM 05/2025.
[2025-06-27 08:27:52] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40342 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 22/05/2025
[2025-06-27 08:28:05] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: PEDRO DUTRA GOMES (CPF/CNPJ: 55579892691) | Justificativa: ATUALIZADO EM 05/2025.
[2025-06-27 08:28:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:28:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:29:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:29:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:29:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:29:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:29:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:29:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:30:07] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40359 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 21/05/2025
[2025-06-27 08:30:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:30:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:30:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:30:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:33:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:33:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:34:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:34:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:34:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:34:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:34:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:34:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:35:05] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40449 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 22/05/2025
[2025-06-27 08:35:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:35:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:35:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:35:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:38:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:38:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:38:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:38:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:39:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:39:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:39:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:39:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:40:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:40:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:40:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:40:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:43:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:43:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:43:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:43:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:44:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:44:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:44:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:44:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:44:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:44:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:45:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:45:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:47:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:47:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:48:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:48:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:49:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:49:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:49:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:49:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:49:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:49:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:50:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:50:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:52:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:52:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:53:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:53:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:53:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:53:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:53:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:53:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:54:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:54:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:54:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:54:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:57:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:57:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:58:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:58:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:58:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:58:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:58:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:58:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:59:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:59:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 08:59:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 08:59:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:02:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:02:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:02:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:02:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:03:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:03:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:03:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:03:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:04:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:04:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:04:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:04:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:07:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:07:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:07:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:07:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:08:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:08:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:08:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:08:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:08:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:08:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:09:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:09:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:10:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:10:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:11:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:11:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:12:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:12:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:13:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:13:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:13:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:13:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:13:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:13:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:14:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:14:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:16:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:16:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:17:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:17:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:17:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:17:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:17:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:17:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:18:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:18:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:19:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:19:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:21:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:21:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:22:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:22:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:22:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:22:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:22:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:22:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:23:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:23:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:23:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:23:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:26:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:26:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:26:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:26:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:27:04] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40086 | Funcionário: Cristiano Prazer | Solicitação Laudo: 08/04/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 09:27:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:27:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:27:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:27:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:28:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:28:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:28:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:28:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:31:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:31:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:31:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:31:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:32:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:32:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:32:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:32:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:32:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:32:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:33:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:33:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:36:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:36:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:36:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:36:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:37:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:37:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:37:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:37:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:37:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:37:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:38:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:38:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:40:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:40:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:41:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:41:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:41:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:41:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:41:55] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39679 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 29/01/2025 | Técnico: Maycon de Souza Dias | SISBR: 27/06/2025
[2025-06-27 09:42:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:42:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:42:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:42:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:43:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:43:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:45:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:45:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:46:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:46:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:46:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:46:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:46:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:46:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:47:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:47:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:47:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:47:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:49:14] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40396 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 14/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 09:50:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:50:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:50:32] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40186 | Funcionário: Cristiano Prazer | Solicitação Laudo: 08/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 09:50:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:50:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:51:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:51:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:51:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:51:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:51:50] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40402 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 14/05/2025 | Técnico: Sidnei Gama | SISBR: 02/06/2025
[2025-06-27 09:52:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:52:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:52:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:52:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:55:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:55:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:55:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:55:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:56:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:56:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:56:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:56:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:56:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:56:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 09:57:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 09:57:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:00:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:00:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:00:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:00:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:01:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:01:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:01:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:01:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:01:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:01:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:02:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:02:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:04:11] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40204 | Funcionário: Cristiano Prazer | Solicitação Laudo: 15/04/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 10:04:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:04:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:05:18] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40413 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 15/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 10:05:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:05:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:05:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:05:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:06:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:06:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:06:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:06:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:07:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:07:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:09:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:09:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:10:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:10:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:10:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:10:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:10:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:10:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:11:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:11:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:11:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40414 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 15/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 10:11:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:11:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:12:07] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40203 | Funcionário: Cristiano Prazer | Solicitação Laudo: 15/04/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 10:14:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:14:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:14:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:14:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:15:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:15:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:15:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:15:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:16:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:16:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:16:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:16:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:19:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:19:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:19:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:19:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:20:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:20:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:20:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:20:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:20:56] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40210 | Funcionário: Cristiano Prazer | Solicitação Laudo: 15/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 10:20:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:20:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:21:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:21:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:24:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:24:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:24:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:24:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:24:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40352 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 14/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 27/06/2025
[2025-06-27 10:25:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:25:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:25:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:25:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:25:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:25:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:26:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:26:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:28:26] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40209 | Funcionário: Cristiano Prazer | Solicitação Laudo: 15/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 10:28:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:28:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:29:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:29:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:29:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:29:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:30:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:30:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:30:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:30:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:31:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:31:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:33:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:33:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:34:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:34:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:34:16] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40208 | Funcionário: Cristiano Prazer | Solicitação Laudo: 15/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 10:34:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:34:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:34:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:34:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:35:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:35:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:35:33] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40358 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 14/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 27/06/2025
[2025-06-27 10:35:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:35:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:38:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:38:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:39:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:39:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:39:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:39:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:39:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:39:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:40:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:40:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:40:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:40:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:43:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:43:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:43:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:43:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:44:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:44:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:44:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:44:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:45:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:45:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:45:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:45:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:46:05] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40219 | Funcionário: Cristiano Prazer | Solicitação Laudo: 15/04/2025 | Técnico: Felipe Jorge Dutra | SISBR: 27/06/2025
[2025-06-27 10:48:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:48:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:48:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:48:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:49:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:49:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:49:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:49:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:49:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:49:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:50:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:50:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:52:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:52:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:53:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:53:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:53:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:53:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:54:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:54:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:54:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:54:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:55:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:55:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:57:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:57:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:58:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:58:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:58:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:58:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:58:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:58:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:59:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:59:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 10:59:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 10:59:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:02:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:02:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:03:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:03:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:03:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:03:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:03:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:03:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:04:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:04:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:04:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:04:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:07:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:07:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:07:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:07:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:08:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:08:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:08:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:08:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:09:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:09:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:09:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:09:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:09:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:09:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:09:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:09:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:12:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:12:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:12:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:12:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:13:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:13:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:13:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:13:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:13:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:13:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:14:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:14:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:16:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:16:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:17:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:17:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:17:47] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40371 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 27/06/2025
[2025-06-27 11:18:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:18:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:18:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:18:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:18:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:18:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:19:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:19:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:21:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:21:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:22:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:22:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:22:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:22:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:22:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:22:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:23:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:23:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:24:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:24:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:26:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:26:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:27:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:27:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:27:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:27:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:27:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:27:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:28:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:28:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:28:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:28:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:31:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:31:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:31:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:31:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:32:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:32:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:32:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:32:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:33:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:33:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:33:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:33:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:36:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:36:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:36:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:36:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:37:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:37:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:37:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:37:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:37:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:37:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:38:13] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40303 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 11:38:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:38:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:41:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:41:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:41:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:41:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:42:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:42:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:42:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:42:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:42:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:42:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:43:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:43:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:45:28] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40265 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 11:45:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:45:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:46:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:46:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:46:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:46:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:47:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:47:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:47:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:47:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:48:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:48:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:50:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:50:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:51:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:51:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:51:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:51:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:51:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:51:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:52:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:52:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:52:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:52:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:55:24] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40266 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 11:55:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:55:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:55:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:55:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:56:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:56:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:56:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:56:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:57:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:57:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 11:57:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 11:57:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:00:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:00:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:00:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:00:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:01:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:01:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:01:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:01:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:01:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:01:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:02:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:02:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:04:05] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40268 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 12:05:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:05:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:05:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:05:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:05:34] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40524 | Funcionário: Jussara Cristina Queiros Soares | Solicitação Laudo: 17/06/2025 | Técnico: Daniel Carlos Muniz | SISBR: 27/06/2025
[2025-06-27 12:06:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:06:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:06:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:06:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:06:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:06:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:07:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:07:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:09:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:09:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:10:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:10:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:10:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:10:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:11:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:11:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:11:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:11:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:12:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:12:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:14:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:14:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:14:43] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: FLAVIO INACIO VALENTIM (CPF/CNPJ: 04421813613) | Justificativa: ERRO CADASTRO
[2025-06-27 12:15:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:15:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:15:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:15:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:15:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:15:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:16:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:16:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:16:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:16:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:19:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:19:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:19:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:19:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:20:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:20:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:20:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:20:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:21:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:21:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:21:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:21:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:24:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:24:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:24:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:24:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:25:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:25:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:25:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:25:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:25:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:25:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:26:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:26:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:26:38] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40269 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 12:27:53] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: TIAGO DE ANDRADE GOMES (CPF/CNPJ: 04109304660) | Justificativa: PROPRIEDADE NAO É MAIS DO ASSOCIADO. LAUDO NEGADO.
[2025-06-27 12:29:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:29:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:29:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:29:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:30:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:30:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:30:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:30:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:30:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:30:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:31:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:31:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:33:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:33:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:34:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:34:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:34:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:34:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:35:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:35:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:35:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:35:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:36:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:36:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:38:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:38:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:39:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:39:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:39:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:39:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:39:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:39:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:40:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:40:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:40:33] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40083 | Funcionário: Jussara Cristina Queiros Soares | Solicitação Laudo: 28/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 12:40:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:40:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:43:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:43:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:44:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:44:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:44:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:44:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:44:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:44:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:45:07] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: VALERIA FERREIRA DA SILVA (CPF/CNPJ: 29174720813) | Justificativa: EXCLUÍDO PELO PA.
[2025-06-27 12:45:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:45:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:45:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:45:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:48:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:48:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:48:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:48:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:49:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:49:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:49:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:49:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:50:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:50:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:50:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:50:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:52:20] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39943 | Funcionário: Jussara Cristina Queiros Soares | Solicitação Laudo: 25/03/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 12:53:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:53:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:53:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:53:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:54:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:54:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:54:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:54:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:54:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:54:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:55:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:55:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:56:35] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40270 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 12:57:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:57:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:58:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:58:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:58:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:58:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:59:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:59:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 12:59:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 12:59:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:00:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:00:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:02:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:02:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:03:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:03:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:03:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:03:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:03:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:03:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:04:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:04:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:04:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:04:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:07:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:07:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:08:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:08:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:08:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:08:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:08:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:08:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:09:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:09:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:09:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:09:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:12:06] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40272 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 14/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 13:12:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:12:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:12:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:12:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:13:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:13:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:13:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:13:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:14:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:14:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:14:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:14:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:17:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:17:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:17:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:17:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:18:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:18:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:18:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:18:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:18:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:18:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:19:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:19:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:21:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:21:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:22:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:22:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:23:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:23:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:23:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:23:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:23:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:23:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:24:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:24:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:26:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:26:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:27:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:27:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:27:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:27:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:27:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:27:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:28:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:28:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:29:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:29:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:31:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:31:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:32:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:32:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:32:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:32:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:32:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:32:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:33:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:33:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:33:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:33:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:36:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:36:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:36:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:36:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:37:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:37:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:37:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:37:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:38:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:38:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:38:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:38:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:41:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:41:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:41:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:41:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:42:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:42:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:42:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:42:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:42:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:42:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:43:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:43:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:45:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:45:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:46:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:46:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:47:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:47:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:47:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:47:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:47:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:47:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:48:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:48:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:50:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:50:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:51:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:51:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:51:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:51:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:51:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:51:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:52:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:52:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:53:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:53:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:55:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:55:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:56:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:56:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:56:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:56:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:56:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:56:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:56:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:56:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:57:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:57:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:57:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:57:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:59:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:59:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:59:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:59:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 13:59:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 13:59:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:00:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:00:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:00:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:00:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:01:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:01:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:01:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:01:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:02:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:02:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:02:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:02:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:05:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:05:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:05:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:05:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:06:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:06:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:06:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:06:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:06:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:06:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:07:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:07:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:10:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:10:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:10:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:10:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:11:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:11:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:11:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:11:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:11:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:11:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:12:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:12:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:14:45] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40229 | Funcionário: Cristiano Prazer | Solicitação Laudo: 22/04/2025 | Técnico: Leonardo Lopes De Oliveira
[2025-06-27 14:14:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:14:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:15:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:15:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:15:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:15:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:16:00] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40229 | Funcionário: Cristiano Prazer | Solicitação Laudo: 22/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 14:16:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:16:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:16:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:16:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:17:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:17:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:19:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:19:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:20:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:20:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:20:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:20:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:20:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:20:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:21:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:21:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:21:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:21:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:24:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:24:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:24:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:24:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:25:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:25:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:25:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:25:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:26:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:26:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:26:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:26:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:29:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:29:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:29:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:29:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:30:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:30:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:30:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:30:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:30:33] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40225 | Funcionário: Cristiano Prazer | Solicitação Laudo: 22/04/2025 | Técnico: Daniel Carlos Muniz | SISBR: 27/06/2025
[2025-06-27 14:30:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:30:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:31:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:31:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:34:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:34:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:34:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:34:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:35:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:35:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:35:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:35:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:35:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:35:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:36:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:36:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:38:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:38:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:39:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:39:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:39:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:39:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:40:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:40:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:40:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:40:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:41:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:41:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:43:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:43:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:44:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:44:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:44:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:44:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:44:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:44:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:45:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:45:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:45:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:45:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:48:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:48:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:48:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:48:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:49:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:49:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:49:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:49:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:50:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:50:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:50:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:50:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:53:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:53:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:53:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:53:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:54:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:54:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:54:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:54:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:54:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:54:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:55:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:55:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:58:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:58:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:58:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:58:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:59:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:59:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:59:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:59:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 14:59:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 14:59:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:00:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:00:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:02:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:02:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:03:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:03:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:03:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:03:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:04:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:04:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:04:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:04:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:05:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:05:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:07:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:07:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:08:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:08:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:08:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:08:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:08:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:08:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:09:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:09:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:09:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:09:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:12:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:12:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:13:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:13:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:13:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:13:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:13:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:13:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:14:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:14:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:14:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:14:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:16:47] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: ANTONIO CARLOS DE SOUZA (CPF/CNPJ: 00625707656) | Justificativa: Gestão de outra cooperativa.
[2025-06-27 15:17:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:17:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:17:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:17:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:18:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:18:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:18:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:18:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:19:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:19:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:19:08] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: FERNANDO VIEIRA (CPF/CNPJ: 04707007694) | Justificativa: Atualizado, 27/06/2025.
[2025-06-27 15:19:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:19:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:22:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:22:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:22:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:22:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:23:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:23:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:23:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:23:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:23:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:23:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:24:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:24:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:26:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:26:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:26:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:26:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:27:16] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40241 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 21/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 15:27:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:27:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:27:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:27:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:28:04] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40242 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 18/06/2025
[2025-06-27 15:28:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:28:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:28:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:28:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:29:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:29:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:31:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:31:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:32:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:32:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:32:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:32:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:32:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:32:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:33:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:33:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:33:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:33:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:36:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:36:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:37:00] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40247 | Funcionário: Cristiano Prazer | Solicitação Laudo: 29/04/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 15:37:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:37:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:37:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:37:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:37:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:37:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:38:13] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40244 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 15:38:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:38:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:38:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:38:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:41:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:41:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:41:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:41:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:42:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:42:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:42:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:42:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:43:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:43:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:43:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:43:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:44:41] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40245 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 15:45:08] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40260 | Funcionário: Cristiano Prazer | Solicitação Laudo: 06/05/2025 | Técnico: Daniel Carlos Muniz | SISBR: 27/06/2025
[2025-06-27 15:46:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:46:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:46:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:46:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:47:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:47:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:47:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:47:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:47:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:47:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:48:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:48:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:49:17] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40280 | Funcionário: Cristiano Prazer | Solicitação Laudo: 06/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 27/06/2025
[2025-06-27 15:50:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:50:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:51:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:51:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:51:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:51:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:52:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:52:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:52:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:52:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:53:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:53:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:55:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:55:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:56:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:56:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:56:35] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40246 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 28/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 15:56:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:56:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:56:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:56:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:57:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:57:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 15:57:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 15:57:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:00:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:00:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:01:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:01:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:01:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:01:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:01:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:01:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:02:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:02:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:02:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:02:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:03:06] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40222 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 16:05:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:05:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:05:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:05:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:06:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:06:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:06:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:06:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:07:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:07:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:07:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:07:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:09:51] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40226 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 16:10:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:10:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:10:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:10:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:11:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:11:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:11:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:11:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:11:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:11:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:12:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:12:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:15:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:15:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:15:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:15:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:16:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:16:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:16:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:16:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:16:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:16:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:17:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:17:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:19:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:19:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:20:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:20:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:20:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:20:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:21:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:21:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:21:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:21:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:22:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:22:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:23:49] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40227 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 16:24:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:24:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:25:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:25:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:25:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:25:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:25:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:25:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:26:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:26:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:26:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:26:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:29:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:29:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:29:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:29:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:30:19] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40228 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 21/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 16:30:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:30:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:30:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:30:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:31:01] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: RENATO JOSE GOMES (CPF/CNPJ: 04389639625) | Justificativa: 04389639625
[2025-06-27 16:31:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:31:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:31:35] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: ARMANDO BUZIM GOMES JUNIOR (CPF/CNPJ: 16017355619) | Justificativa: ATUALIZADO
[2025-06-27 16:31:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:31:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:31:59] [INFO] MCI - Remoção de registro: MCI - Remoção de registro: GERALDO BENTO (CPF/CNPJ: 70325162620) | Justificativa: ATUAIZADO
[2025-06-27 16:34:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:34:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:34:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:34:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:35:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:35:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:35:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:35:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:35:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:35:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:36:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:36:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:38:14] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40276 | Funcionário: Cristiano Prazer | Solicitação Laudo: 06/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 27/06/2025
[2025-06-27 16:39:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:39:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:39:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:39:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:40:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:40:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:40:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:40:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:40:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:40:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:41:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:41:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:43:45] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40282 | Funcionário: Cristiano Prazer | Solicitação Laudo: 13/05/2025 | Técnico: Felipe Jorge Dutra | SISBR: 27/06/2025
[2025-06-27 16:43:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:43:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:44:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:44:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:44:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:44:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:45:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:45:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:45:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:45:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:45:55] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40199 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 16:46:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:46:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:48:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:48:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:49:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:49:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:49:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:49:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:49:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:49:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:50:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:50:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:50:38] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40275 | Funcionário: Cristiano Prazer | Solicitação Laudo: 06/05/2024 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 16:50:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:50:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:53:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:53:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:54:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:54:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:54:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:54:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:54:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:54:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:55:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:55:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:55:32] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40200 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 16:55:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:55:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:56:17] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40274 | Funcionário: Cristiano Prazer | Solicitação Laudo: 06/05/2025 | Técnico: Leonardo Lopes De Oliveira | SISBR: 27/06/2025
[2025-06-27 16:58:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:58:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:58:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:58:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:59:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:59:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:59:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:59:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 16:59:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 16:59:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:00:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:00:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:03:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:03:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:03:13] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40201 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 17:03:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:03:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:04:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:04:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:04:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:04:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:04:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:04:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:05:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:05:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:07:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:07:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:08:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:08:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:08:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:08:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:09:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:09:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:09:10] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40183 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 17:09:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:09:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:10:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:10:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:12:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:12:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:13:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:13:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:13:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:13:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:13:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:13:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:14:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:14:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:14:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:14:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:16:17] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40184 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 17:17:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:17:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:18:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:18:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:18:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:18:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:18:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:18:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:19:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:19:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:19:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:19:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:22:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:22:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:22:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:22:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:23:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:23:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:23:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:23:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:24:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:24:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:24:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:24:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:27:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:27:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:27:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:27:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:28:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:28:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:28:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:28:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:28:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:28:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:29:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:29:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:31:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:31:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:32:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:32:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:33:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:33:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:33:01] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40190 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 02/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 29/05/2025
[2025-06-27 17:33:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:33:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:33:39] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40191 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 02/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 21/03/2025
[2025-06-27 17:33:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:33:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:34:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:34:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:36:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:36:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:37:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:37:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:37:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:37:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:37:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:37:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:38:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:38:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:39:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:39:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:41:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:41:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:42:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:42:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:42:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:42:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:42:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:42:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:43:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:43:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:43:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:43:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:46:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:46:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:46:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:46:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:47:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:47:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:47:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:47:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:48:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:48:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:48:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:48:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:51:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:51:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:51:13] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40305 | Funcionário: Cristiano Prazer | Solicitação Laudo: 20/05/2025 | Técnico: Maycon de Souza Dias | SISBR: 27/06/2025
[2025-06-27 17:51:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:51:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:52:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:52:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:52:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:52:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:52:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:52:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:53:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:53:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:54:10] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 40197 | Funcionário: Emilay Cristina Guimaraes Nascimento | Solicitação Laudo: 07/05/2025 | Técnico: Sidnei Gama | SISBR: 27/06/2025
[2025-06-27 17:55:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:55:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:56:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:56:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:57:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:57:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:57:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:57:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:57:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:57:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 17:58:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 17:58:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:00:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:00:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:01:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:01:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:01:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:01:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:02:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:02:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:02:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:02:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:03:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:03:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:05:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:05:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:06:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:06:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:06:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:06:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:06:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:06:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:07:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:07:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:07:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:07:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:08:54] [INFO] MCI - Atualização de registro: MCI - Atualização de registro ID: 39726 | Funcionário: Luis Otavio Santos | Solicitação Laudo: 28/02/2025 | Técnico: Felipe Jorge Dutra | SISBR: 27/06/2025
[2025-06-27 18:10:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:10:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:10:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:10:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:11:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:11:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:11:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:11:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:12:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:12:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:12:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:12:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:15:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:15:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:15:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:15:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:16:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:16:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:16:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:16:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:16:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:16:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:17:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:17:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:20:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:20:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:20:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:20:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:21:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:21:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:21:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:21:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:21:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:21:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:22:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:22:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:24:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:24:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:25:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:25:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:25:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:25:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:26:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:26:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:26:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:26:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:27:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:27:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:29:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:29:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:30:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:30:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:30:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:30:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:30:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:30:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:31:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:31:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:31:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:31:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:34:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:34:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:34:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:34:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:35:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:35:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:35:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:35:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:36:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:36:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:36:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:36:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:39:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:39:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:39:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:39:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:40:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:40:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:40:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:40:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:40:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:40:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:41:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:41:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:44:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:44:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:44:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:44:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:45:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:45:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:45:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:45:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:45:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:45:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:46:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:46:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:48:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:48:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:49:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:49:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:49:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:49:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:50:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:50:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:50:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:50:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:51:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:51:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:53:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:53:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:54:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:54:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:54:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:54:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:54:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:54:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:55:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:55:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:55:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:55:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:58:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:58:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:59:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:59:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:59:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:59:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 18:59:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 18:59:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:00:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:00:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:00:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:00:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:03:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:03:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:03:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:03:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:04:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:04:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:04:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:04:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:05:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:05:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:05:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:05:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:08:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:08:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:08:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:08:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:09:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:09:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:09:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:09:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:09:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:09:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:10:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:10:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:12:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:12:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:13:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:13:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:13:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:13:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:14:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:14:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:14:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:14:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:15:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:15:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:17:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:17:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:18:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:18:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:18:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:18:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:18:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:18:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:19:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:19:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:19:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:19:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:22:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:22:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:23:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:23:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:23:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:23:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:23:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:23:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:24:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:24:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:24:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:24:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:27:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:27:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:27:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:27:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:28:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:28:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:28:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:28:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:29:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:29:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:29:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:29:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:32:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:32:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:32:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:32:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:33:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:33:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:33:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:33:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:33:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:33:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:34:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:34:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:36:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:36:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:37:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:37:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:38:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:38:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:38:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:38:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:38:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:38:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:39:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:39:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:41:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:41:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:42:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:42:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:42:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:42:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:42:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:42:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:43:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:43:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:44:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:44:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:46:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:46:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:47:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:47:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:47:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:47:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:47:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:47:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:48:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:48:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:48:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:48:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:51:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:51:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:51:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:51:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:52:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:52:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:52:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:52:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:53:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:53:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:53:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:53:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:56:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:56:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:56:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:56:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:57:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:57:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:57:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:57:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:57:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:57:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 19:58:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 19:58:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:01:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:01:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:01:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:01:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:02:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:02:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:02:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:02:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:02:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:02:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:03:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:03:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:05:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:05:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:06:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:06:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:06:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:06:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:07:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:07:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:07:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:07:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:08:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:08:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:10:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:10:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:11:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:11:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:11:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:11:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:11:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:11:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:12:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:12:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:12:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:12:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:15:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:15:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:15:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:15:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:16:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:16:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:16:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:16:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:17:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:17:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:17:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:17:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:20:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:20:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:20:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:20:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:21:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:21:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:21:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:21:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:21:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:21:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:22:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:22:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:25:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:25:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:25:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:25:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:26:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:26:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:26:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:26:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:26:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:26:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:27:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:27:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:29:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:29:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:30:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:30:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:30:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:30:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:31:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:31:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:31:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:31:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:32:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:32:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:34:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:34:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:35:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:35:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:35:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:35:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:35:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:35:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:36:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:36:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:36:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:36:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:39:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:39:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:39:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:39:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:40:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:40:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:40:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:40:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:41:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:41:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:41:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:41:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:44:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:44:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:44:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:44:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:45:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:45:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:45:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:45:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:45:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:45:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:46:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:46:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:49:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:49:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:49:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:49:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:50:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:50:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:50:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:50:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:50:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:50:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:51:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:51:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:53:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:53:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:54:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:54:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:54:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:54:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:55:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:55:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:55:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:55:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:56:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:56:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:58:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:58:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:59:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:59:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:59:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:59:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 20:59:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 20:59:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:00:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:00:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:00:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:00:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:03:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:03:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:03:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:03:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:04:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:04:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:04:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:04:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:05:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:05:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:05:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:05:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:08:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:08:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:08:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:08:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:09:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:09:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:09:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:09:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:09:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:09:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:10:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:10:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:13:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:13:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:13:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:13:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:14:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:14:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:14:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:14:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:14:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:14:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:15:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:15:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:17:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:17:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:18:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:18:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:18:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:18:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:19:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:19:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:19:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:19:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:20:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:20:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:22:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:22:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:23:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:23:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:23:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:23:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:23:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:23:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:24:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:24:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:24:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:24:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:27:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:27:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:28:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:28:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:28:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:28:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:28:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:28:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:29:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:29:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:29:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:29:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:32:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:32:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:32:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:32:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:33:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:33:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:33:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:33:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:34:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:34:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:34:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:34:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:37:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:37:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:37:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:37:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:38:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:38:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:38:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:38:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:38:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:38:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:39:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:39:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:41:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:41:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:42:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:42:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:42:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:42:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:43:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:43:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:43:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:43:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:44:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:44:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:46:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:46:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:47:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:47:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:47:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:47:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:47:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:47:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:48:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:48:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:48:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:48:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:51:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:51:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:52:01] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:52:01] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:52:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:52:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:52:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:52:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:53:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:53:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:53:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:53:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:56:18] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:56:18] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:56:49] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:56:49] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:57:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:57:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:57:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:57:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:58:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:58:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 21:58:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 21:58:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:01:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:01:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:01:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:01:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:02:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:02:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:02:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:02:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:02:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:02:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:03:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:03:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:05:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:05:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:06:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:06:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:06:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:06:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:07:06] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:07:06] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:07:37] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:07:37] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:08:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:08:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:10:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:10:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:11:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:11:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:11:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:11:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:11:54] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:11:54] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:12:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:12:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:12:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:12:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:15:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:15:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:16:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:16:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:16:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:16:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:16:42] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:16:42] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:17:13] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:17:13] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:17:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:17:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:20:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:20:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:20:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:20:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:21:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:21:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:21:30] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:21:30] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:22:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:22:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:22:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:22:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:25:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:25:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:25:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:25:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:26:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:26:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:26:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:26:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:26:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:26:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:27:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:27:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:29:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:29:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:30:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:30:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:30:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:30:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:31:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:31:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:31:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:31:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:32:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:32:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:34:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:34:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:35:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:35:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:35:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:35:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:35:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:35:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:36:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:36:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:36:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:36:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:39:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:39:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:40:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:40:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:40:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:40:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:40:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:40:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:41:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:41:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:41:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:41:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:44:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:44:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:44:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:44:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:45:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:45:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:45:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:45:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:46:02] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:46:02] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:46:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:46:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:49:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:49:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:49:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:49:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:50:10] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:50:10] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:50:19] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:50:19] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:50:50] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:50:50] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:51:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:51:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:53:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:53:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:54:27] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:54:27] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:54:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:54:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:55:07] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:55:07] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:55:38] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:55:38] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:56:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:56:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:58:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:58:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:59:15] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:59:15] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:59:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:59:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 22:59:55] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 22:59:55] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:00:26] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:00:26] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:00:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:00:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:03:32] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:03:32] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:04:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:04:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:04:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:04:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:04:43] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:04:43] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:05:14] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:05:14] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:05:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:05:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:08:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:08:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:08:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:08:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:09:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:09:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:09:31] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:09:31] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:10:03] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:10:03] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:10:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:10:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:13:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:13:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:13:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:13:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:14:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:14:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:14:20] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:14:20] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:14:51] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:14:51] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:15:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:15:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:17:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:17:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:18:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:18:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:18:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:18:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:19:08] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:19:08] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:19:39] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:19:39] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:20:11] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:20:11] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:22:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:22:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:23:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:23:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:23:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:23:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:23:56] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:23:56] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:24:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:24:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:24:59] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:24:59] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:27:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:27:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:28:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:28:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:28:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:28:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:28:44] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:28:44] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:29:16] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:29:16] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:29:47] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:29:47] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:32:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:32:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:32:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:32:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:33:24] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:33:24] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:33:33] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:33:33] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:34:04] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:34:04] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:34:35] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:34:35] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:37:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:37:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:37:41] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:37:41] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:38:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:38:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:38:21] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:38:21] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:38:52] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:38:52] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:39:23] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:39:23] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:41:58] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:41:58] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:42:29] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:42:29] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:43:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:43:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:43:09] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:43:09] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:43:40] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:43:40] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:44:12] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:44:12] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:46:46] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:46:46] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:47:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:47:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:47:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:47:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:47:57] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:47:57] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:48:28] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:48:28] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:49:00] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:49:00] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:51:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:51:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:52:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:52:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:52:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:52:36] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:52:45] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:52:45] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:53:17] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:53:17] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:53:48] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:53:48] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:56:22] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:56:22] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:56:53] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:56:53] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:57:25] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:57:25] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:57:34] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:57:34] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:58:05] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:58:05] [INFO] Cache de usuários da intranet criado com 290 usuários
[2025-06-27 23:58:36] [INFO] API da Intranet: 290 usuários ativos carregados (filtro de bloqueio removido)
[2025-06-27 23:58:36] [INFO] Cache de usuários da intranet criado com 290 usuários
