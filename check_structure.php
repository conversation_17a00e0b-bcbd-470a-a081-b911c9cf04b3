<?php
require_once 'config/database.php';

echo "Estrutura da tabela cad_registros:\n";
$stmt = $pdo_mci->query('DESCRIBE cad_registros');
while($row = $stmt->fetch()) {
    echo $row['Field'] . ' - ' . $row['Type'] . "\n";
}

echo "\nPrimeiros 3 registros:\n";
$stmt = $pdo_mci->query('SELECT * FROM cad_registros LIMIT 3');
$registros = $stmt->fetchAll();
foreach($registros as $registro) {
    echo "ID: " . $registro['id'] . "\n";
    foreach($registro as $campo => $valor) {
        if (!is_numeric($campo)) {
            echo "  $campo: $valor\n";
        }
    }
    echo "---\n";
}
?>
