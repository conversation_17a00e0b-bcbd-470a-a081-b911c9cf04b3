<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Layout Dashboard MCI - Técnicos Agrícolas</title>
    <style>
        :root {
            --sicoob-verde-escuro: #073B4C;
            --sicoob-verde-medio: #118AB2;
            --sicoob-verde-claro: #06D6A0;
            --sicoob-laranja: #F7931E;
            
            /* Cores da identidade visual para técnicos agrícolas */
            --cor-tecnico-1: #FF6B35; /* Laranja vibrante */
            --cor-tecnico-2: #F7931E; /* Laranja médio */
            --cor-tecnico-3: #FFD23F; /* <PERSON><PERSON> */
            --cor-tecnico-4: #06D6A0; /* Verde água */
            --cor-tecnico-5: #118AB2; /* Azul */
            --cor-tecnico-6: #073B4C; /* Azul escuro */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFD23F 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .test-container {
            padding: clamp(0.5rem, 1.5vw, 1.2rem);
            max-width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .test-header {
            text-align: center;
            margin-bottom: clamp(1rem, 2vw, 1.5rem);
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .test-metrics {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: clamp(1rem, 2.5vw, 2rem);
            text-align: center;
            margin-bottom: clamp(1rem, 2vw, 1.5rem);
            flex-shrink: 0;
        }

        .progress-bar-overview {
            height: 12px;
            background-color: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            margin-top: 1rem;
            display: flex;
        }

        .progress-segment {
            height: 100%;
            transition: width 0.8s ease;
        }

        .progress-segment:first-child {
            border-radius: 6px 0 0 6px;
        }

        .progress-segment:last-child {
            border-radius: 0 6px 6px 0;
        }

        /* Classes específicas para cada técnico */
        .tecnico-1 { --cor-tecnico: var(--cor-tecnico-1); }
        .tecnico-2 { --cor-tecnico: var(--cor-tecnico-2); }
        .tecnico-3 { --cor-tecnico: var(--cor-tecnico-3); }
        .tecnico-4 { --cor-tecnico: var(--cor-tecnico-4); }
        .tecnico-5 { --cor-tecnico: var(--cor-tecnico-5); }
        .tecnico-6 { --cor-tecnico: var(--cor-tecnico-6); }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: clamp(0.8rem, 1.5vw, 1.2rem);
            flex: 1;
            min-height: 0;
            overflow: hidden;
            height: 100%;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: clamp(0.8rem, 2vw, 1.5rem);
            border: 2px solid transparent;
            border-top: 3px solid var(--cor-tecnico, var(--sicoob-laranja));
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            text-align: center;
            height: 100%;
            min-height: 0;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .test-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            padding: 2px;
            background: linear-gradient(135deg, 
                var(--cor-tecnico, var(--sicoob-laranja)) 0%, 
                transparent 50%, 
                var(--cor-tecnico, var(--sicoob-laranja)) 100%);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            opacity: 0.3;
            z-index: -1;
        }

        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .test-card:hover::before {
            opacity: 0.5;
        }

        .avatar-container {
            position: relative;
            margin-bottom: clamp(0.5rem, 1.5vw, 1rem);
            flex-shrink: 0;
        }

        .avatar {
            width: clamp(60px, 8vw, 90px);
            height: clamp(60px, 8vw, 90px);
            border-radius: 50%;
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-laranja));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: clamp(1.2rem, 4vw, 2.2rem);
            font-weight: 700;
            position: relative;
            z-index: 2;
        }

        .progress-ring {
            position: absolute;
            top: -5px;
            left: -5px;
            width: calc(clamp(60px, 8vw, 90px) + 10px);
            height: calc(clamp(60px, 8vw, 90px) + 10px);
            border-radius: 50%;
            background: conic-gradient(
                var(--cor-tecnico, var(--sicoob-laranja)) 0deg,
                var(--cor-tecnico, var(--sicoob-laranja)) var(--progress-angle, 0deg),
                #e9ecef var(--progress-angle, 0deg),
                #e9ecef 360deg
            );
            z-index: 1;
        }

        .progress-ring::before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            width: clamp(60px, 8vw, 90px);
            height: clamp(60px, 8vw, 90px);
            border-radius: 50%;
            background: white;
        }

        .screen-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="screen-info">
        Resolução: <span id="resolution"></span><br>
        Viewport: <span id="viewport"></span>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h1>🌱 Dashboard MCI - Técnicos Agrícolas</h1>
            <p>Verificação de ajuste para TV</p>
        </div>

        <div class="test-metrics">
            <h3>Meta Total da Equipe</h3>
            <div style="font-size: 2rem; font-weight: 700; color: var(--sicoob-laranja); margin: 0.5rem 0;">520</div>
            <div style="color: #6c757d; margin-bottom: 1rem;">385 concluídos (74.0%)</div>
            <div class="progress-bar-overview">
                <div class="progress-segment" style="width: 18%; background-color: #FF6B35;" title="João Silva: 95 atualizações"></div>
                <div class="progress-segment" style="width: 16%; background-color: #F7931E;" title="Maria Santos: 82 atualizações"></div>
                <div class="progress-segment" style="width: 14%; background-color: #FFD23F;" title="Pedro Costa: 71 atualizações"></div>
                <div class="progress-segment" style="width: 13%; background-color: #06D6A0;" title="Ana Oliveira: 68 atualizações"></div>
                <div class="progress-segment" style="width: 12%; background-color: #118AB2;" title="Carlos Lima: 62 atualizações"></div>
                <div class="progress-segment" style="width: 1%; background-color: #073B4C;" title="Fernanda Rocha: 7 atualizações"></div>
            </div>
        </div>

        <div class="test-grid">
            <div class="test-card tecnico-1">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 288deg;"></div>
                    <div class="avatar">J</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">João Silva</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Total</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-tecnico); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">95/120</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">79.2%</div>
                </div>
            </div>

            <div class="test-card tecnico-2">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 252deg;"></div>
                    <div class="avatar">M</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Maria Santos</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Total</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-tecnico); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">82/115</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">71.3%</div>
                </div>
            </div>

            <div class="test-card tecnico-3">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 230deg;"></div>
                    <div class="avatar">P</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Pedro Costa</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Total</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-tecnico); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">71/110</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">64.5%</div>
                </div>
            </div>

            <div class="test-card tecnico-4">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 216deg;"></div>
                    <div class="avatar">A</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Ana Oliveira</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Total</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-tecnico); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">68/113</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">60.2%</div>
                </div>
            </div>

            <div class="test-card tecnico-5">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 194deg;"></div>
                    <div class="avatar">C</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Carlos Lima</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Total</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-tecnico); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">62/115</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">53.9%</div>
                </div>
            </div>

            <div class="test-card tecnico-6">
                <div class="avatar-container">
                    <div class="progress-ring" style="--progress-angle: 25deg;"></div>
                    <div class="avatar">F</div>
                </div>
                <h4 style="font-size: clamp(0.9rem, 2.2vw, 1.4rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.3rem, 1vw, 0.8rem);">Fernanda Rocha</h4>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: clamp(0.8rem, 1.8vw, 1.1rem); color: var(--sicoob-verde-escuro); font-weight: 600; margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">Meta Total</div>
                    <div style="font-size: clamp(1.1rem, 2.5vw, 1.6rem); font-weight: 700; color: var(--cor-tecnico); margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);">7/100</div>
                    <div style="font-size: clamp(1rem, 2.2vw, 1.4rem); font-weight: 600; color: var(--sicoob-verde-escuro);">7.0%</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateScreenInfo() {
            document.getElementById('resolution').textContent = screen.width + 'x' + screen.height;
            document.getElementById('viewport').textContent = window.innerWidth + 'x' + window.innerHeight;
        }

        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
