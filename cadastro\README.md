# Dashboard MCI - Atualizações Cadastrais

Dashboard em tempo real para monitoramento das métricas de atualizações cadastrais, otimizado para exibição em televisões e monitores grandes.

## 📋 Características

### 🎯 Funcionalidades Principais
- **Métricas em Tempo Real**: Exibe métricas mensais e anuais atualizadas
- **Integração com Intranet**: Busca fotos e dados dos funcionários via API
- **Gráficos Interativos**: Gráficos de rosca modernos com Chart.js
- **Auto-refresh**: Atualização automática a cada 5 minutos
- **Responsivo**: Adaptável para diferentes tamanhos de tela (TV, desktop, tablet)

### 📊 Métricas Exibidas
- **Métricas Gerais**:
  - Meta mensal total da equipe
  - Meta anual total da equipe
  - Progresso geral em percentual

- **Métricas Individuais** (por funcionário):
  - Meta mensal individual
  - Meta anual individual
  - Progresso em gráficos de rosca
  - Foto do funcionário (via API da Intranet)

## 🔧 Configuração

### Arquivos Principais
- `dashboard.php` - Dashboard principal
- `config_api.php` - Configurações e classe da API da Intranet

### Dependências
- PHP 7.4+
- Extensão cURL habilitada
- Acesso à API da Intranet Sicoob
- Bootstrap 5.3.0
- Chart.js (via CDN)
- Font Awesome 6.0.0

### Configuração da API
As configurações da API estão centralizadas no arquivo `config_api.php`:

```php
define('API_INTRANET_URL', 'https://intranet.sicoobcredilivre.com.br/api');
define('API_INTRANET_USER', 'UFL7GXZ14LU9NOR');
define('API_INTRANET_TOKEN', '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG');
```

## 🎨 Design e Layout

### Responsividade
- **Desktop/TV (1920px+)**: Grid de 3 colunas para funcionários
- **4K (3840px+)**: Grid de 4 colunas para funcionários
- **Tablet/Mobile**: Layout adaptativo com colunas flexíveis

### Cores (Identidade Sicoob)
- Verde Escuro: `#003641`
- Verde Médio: `#00AE9D`
- Verde Claro: `#C9D200`
- Turquesa: `#00AE9D`

### Elementos Visuais
- Gradientes modernos
- Sombras suaves
- Animações de hover
- Gráficos de rosca com percentuais centralizados

## 🔄 Funcionamento

### Fluxo de Dados
1. **Busca de Configurações**: Carrega porcentagem de meta do banco
2. **Integração API**: Busca dados dos usuários da Intranet
3. **Cálculo de Métricas**: Processa dados dos funcionários
4. **Renderização**: Exibe dashboard com gráficos
5. **Auto-refresh**: Recarrega página a cada 5 minutos

### Cálculo de Métricas
- **Meta Anual**: `total_registros * porcentagem_meta / 100`
- **Meta Mensal**: `registros_vencendo_mes * porcentagem_meta / 100`
- **Progresso**: `(concluidos / meta) * 100`

### Funcionários Monitorados
IDs configurados no código: `[17, 18, 19, 20, 21, 22]`

## 🚀 Instalação

1. **Copiar arquivos** para a pasta `mci/cadastro/`
2. **Verificar permissões** de acesso ao sistema MCI
3. **Testar conectividade** com a API da Intranet
4. **Acessar** via navegador: `/mci/cadastro/dashboard.php`

## 📱 Uso em TV

### Configurações Recomendadas
- **Resolução**: 1920x1080 ou superior
- **Navegador**: Chrome/Firefox em modo fullscreen
- **Zoom**: 100% (ajustar conforme necessário)
- **Auto-hide**: Ocultar barras do navegador

### Dicas para TV
- Use F11 para modo fullscreen
- Configure para não entrar em modo de economia de energia
- Posicione a TV em local com boa visibilidade para a equipe

## 🔍 Monitoramento

### Logs
- Logs são gravados via `MciLogger`
- Erros da API são registrados automaticamente
- Localização: `mci/logs/`

### Troubleshooting
- **API não responde**: Verificar conectividade e credenciais
- **Fotos não carregam**: Verificar URLs e permissões
- **Dados não atualizam**: Verificar logs e conexão com banco

## 🔒 Segurança

- Autenticação via sistema MCI
- Validação de permissões de acesso
- Sanitização de dados de entrada
- Timeout configurado para requisições API

## 📈 Performance

### Otimizações
- Cache de usuários da Intranet
- Consultas SQL otimizadas
- Carregamento assíncrono de recursos
- Pause de refresh quando página não visível

### Métricas de Performance
- Tempo de carregamento: < 3 segundos
- Uso de memória: Otimizado para execução contínua
- Requisições API: Minimizadas com cache

## 🛠️ Manutenção

### Atualizações Regulares
- Verificar logs de erro semanalmente
- Monitorar performance da API
- Atualizar credenciais quando necessário
- Revisar IDs de funcionários conforme mudanças na equipe

### Backup
- Fazer backup dos arquivos de configuração
- Documentar alterações nas configurações
- Manter histórico de versões

---

**Desenvolvido para o Sistema MCI - Sicoob Credilivre**  
*Dashboard otimizado para monitoramento em tempo real*
