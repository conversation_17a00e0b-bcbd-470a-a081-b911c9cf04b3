<?php
// <PERSON><PERSON> as configurações do banco de dados do projeto principal
require_once __DIR__ . '/../../config/database.php';

// Configurações específicas para o banco MCI
define('MCI_DB_HOST', DB_HOST);
define('MCI_DB_USER', DB_USER);
define('MCI_DB_PASS', DB_PASS);
define('MCI_DB_NAME', 'mci'); // Banco específico para MCI

try {
    $pdo_mci = new PDO("mysql:host=" . MCI_DB_HOST . ";dbname=" . MCI_DB_NAME, MCI_DB_USER, MCI_DB_PASS);
    $pdo_mci->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo_mci->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $pdo_mci->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
} catch(PDOException $e) {
    die("Erro de conexão com banco MCI: " . $e->getMessage());
}
?>
