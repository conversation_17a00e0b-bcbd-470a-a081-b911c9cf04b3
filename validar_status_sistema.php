<?php
require_once 'auth_check.php';

echo "<h1>Validação do Sistema de Status</h1>";
echo "<hr>";

try {
    echo "<h3>1. Verificando Tabela cad_status</h3>";
    
    // Verificar se a tabela existe
    $stmt = $pdo_mci->prepare("SHOW TABLES LIKE 'cad_status'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ Tabela cad_status não encontrada!</p>";
        echo "<p><a href='criar_tabela_status.php'>Criar tabela de status</a></p>";
        exit;
    }
    
    // Buscar status disponíveis
    $stmt = $pdo_mci->prepare("SELECT * FROM cad_status ORDER BY id");
    $stmt->execute();
    $status_disponiveis = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Descrição</th><th>Cor</th><th>Ativo</th></tr>";
    foreach ($status_disponiveis as $status) {
        $cor_preview = "<span style='background-color: {$status['cor']}; color: white; padding: 2px 8px; border-radius: 3px;'>{$status['nome']}</span>";
        $ativo = $status['ativo'] ? '✅' : '❌';
        echo "<tr>";
        echo "<td><strong>{$status['id']}</strong></td>";
        echo "<td>$cor_preview</td>";
        echo "<td>{$status['descricao']}</td>";
        echo "<td>{$status['cor']}</td>";
        echo "<td>$ativo</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>2. Verificando Coluna status em cad_registros</h3>";
    
    // Verificar estrutura da coluna
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field = 'status'");
    $stmt->execute();
    $column_info = $stmt->fetch();
    
    if (!$column_info) {
        echo "<p style='color: red;'>❌ Coluna status não encontrada!</p>";
        exit;
    }
    
    echo "<p><strong>Tipo:</strong> <code>{$column_info['Type']}</code></p>";
    echo "<p><strong>Padrão:</strong> <code>" . ($column_info['Default'] ?? 'NULL') . "</code></p>";
    echo "<p><strong>Permite NULL:</strong> " . ($column_info['Null'] == 'YES' ? 'Sim' : 'Não') . "</p>";
    
    // Verificar chave estrangeira
    $stmt = $pdo_mci->prepare("
        SELECT CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = 'mci' 
        AND TABLE_NAME = 'cad_registros' 
        AND COLUMN_NAME = 'status' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $stmt->execute();
    $fk_info = $stmt->fetch();
    
    if ($fk_info) {
        echo "<p><strong>Chave Estrangeira:</strong> ✅ {$fk_info['CONSTRAINT_NAME']} → {$fk_info['REFERENCED_TABLE_NAME']}.{$fk_info['REFERENCED_COLUMN_NAME']}</p>";
    } else {
        echo "<p><strong>Chave Estrangeira:</strong> ❌ Não configurada</p>";
    }
    
    echo "<h3>3. Analisando Dados dos Registros</h3>";
    
    // Verificar distribuição de status
    $stmt = $pdo_mci->prepare("
        SELECT 
            r.status,
            s.nome as status_nome,
            s.cor as status_cor,
            COUNT(*) as total
        FROM cad_registros r
        LEFT JOIN cad_status s ON r.status = s.id
        GROUP BY r.status, s.nome, s.cor
        ORDER BY r.status
    ");
    $stmt->execute();
    $distribuicao = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Valor Status</th><th>Nome Status</th><th>Total Registros</th><th>Situação</th></tr>";
    
    $total_registros = 0;
    $registros_validos = 0;
    $registros_invalidos = 0;
    
    foreach ($distribuicao as $dist) {
        $total_registros += $dist['total'];
        
        if ($dist['status_nome']) {
            $registros_validos += $dist['total'];
            $cor_preview = "<span style='background-color: {$dist['status_cor']}; color: white; padding: 2px 8px; border-radius: 3px;'>{$dist['status_nome']}</span>";
            $situacao = "✅ Válido";
        } else {
            $registros_invalidos += $dist['total'];
            $cor_preview = "<em>Indefinido</em>";
            $situacao = "❌ Inválido";
        }
        
        echo "<tr>";
        echo "<td><code>{$dist['status']}</code></td>";
        echo "<td>$cor_preview</td>";
        echo "<td><strong>{$dist['total']}</strong></td>";
        echo "<td>$situacao</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>4. Resumo da Validação</h3>";
    
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>📊 Estatísticas:</h4>";
    echo "<ul>";
    echo "<li><strong>Total de registros:</strong> " . number_format($total_registros) . "</li>";
    echo "<li><strong>Registros com status válido:</strong> " . number_format($registros_validos) . " (" . round(($registros_validos / $total_registros) * 100, 1) . "%)</li>";
    echo "<li><strong>Registros com status inválido:</strong> " . number_format($registros_invalidos) . " (" . round(($registros_invalidos / $total_registros) * 100, 1) . "%)</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($registros_invalidos == 0) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #155724;'>🎉 Sistema de Status Funcionando Perfeitamente!</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ Tabela cad_status configurada corretamente</li>";
        echo "<li>✅ Coluna status usando tipo INT</li>";
        echo "<li>✅ Chave estrangeira configurada</li>";
        echo "<li>✅ Todos os registros com status válidos</li>";
        echo "<li>✅ Mapeamento ID → Nome funcionando</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #721c24;'>⚠️ Problemas Encontrados</h4>";
        echo "<ul style='color: #721c24;'>";
        echo "<li>❌ $registros_invalidos registros com status inválido</li>";
        echo "<li>🔧 Correção necessária</li>";
        echo "</ul>";
        echo "<p style='color: #721c24;'><a href='corrigir_status_registros.php'>Executar correção automática</a></p>";
        echo "</div>";
    }
    
    echo "<h3>5. Teste de Funcionalidades</h3>";
    
    // Testar busca com JOIN
    echo "<h4>Teste de JOIN com cad_status:</h4>";
    $stmt = $pdo_mci->prepare("
        SELECT r.id, r.pa, r.nome_cliente, s.nome as status_nome, s.cor as status_cor
        FROM cad_registros r
        LEFT JOIN cad_status s ON r.status = s.id
        LIMIT 5
    ");
    $stmt->execute();
    $teste_join = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>PA</th><th>Cliente</th><th>Status</th></tr>";
    foreach ($teste_join as $teste) {
        if ($teste['status_nome']) {
            $status_display = "<span style='background-color: {$teste['status_cor']}; color: white; padding: 2px 8px; border-radius: 3px;'>{$teste['status_nome']}</span>";
        } else {
            $status_display = "<em style='color: red;'>Status inválido</em>";
        }
        
        echo "<tr>";
        echo "<td>{$teste['id']}</td>";
        echo "<td>{$teste['pa']}</td>";
        echo "<td>" . htmlspecialchars($teste['nome_cliente']) . "</td>";
        echo "<td>$status_display</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>6. Mapeamento de Status</h3>";
    
    echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h4>📋 Como deve funcionar:</h4>";
    echo "<table border='1' cellpadding='5' style='margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Uso</th></tr>";
    echo "<tr><td><strong>1</strong></td><td>Pendente</td><td>Registros recém-importados, aguardando processamento</td></tr>";
    echo "<tr><td><strong>2</strong></td><td>Atualizado</td><td>Registros já processados e atualizados</td></tr>";
    echo "</table>";
    echo "<p><strong>Importante:</strong></p>";
    echo "<ul>";
    echo "<li>Novos registros importados recebem automaticamente status = 1 (Pendente)</li>";
    echo "<li>Status pode ser alterado manualmente na interface de gerenciamento</li>";
    echo "<li>Filtros funcionam com base nos IDs (1, 2, etc.)</li>";
    echo "<li>Interface mostra nomes e cores da tabela cad_status</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<hr>";
    echo "<h3>Links Úteis</h3>";
    echo "<ul>";
    echo "<li><a href='gerenciar.php'>Gerenciar Registros</a> - Ver registros com status</li>";
    echo "<li><a href='importar.php'>Importar Dados</a> - Novos registros terão status 'Pendente'</li>";
    echo "<li><a href='corrigir_status_registros.php'>Corrigir Status</a> - Corrigir registros com status inválido</li>";
    echo "<li><a href='criar_tabela_status.php'>Recriar Estrutura</a> - Recriar tabela de status se necessário</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a validação</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
