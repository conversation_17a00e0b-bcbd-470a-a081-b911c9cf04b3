<?php
require_once '../auth_check.php';

echo "<h2>🔍 Debug - API Intranet para Técnicos</h2>";

// Buscar dados dos técnicos
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

echo "<h3>📊 Técnicos Encontrados</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Nome</th><th>Email</th><th>Meta</th></tr>";

foreach ($tecnicos_dados as $tecnico) {
    $destaque = ($tecnico['id'] == 58) ? 'style="background-color: #fff3cd;"' : '';
    echo "<tr $destaque>";
    echo "<td>" . $tecnico['id'] . "</td>";
    echo "<td>" . htmlspecialchars($tecnico['nome']) . "</td>";
    echo "<td>" . htmlspecialchars($tecnico['email']) . "</td>";
    echo "<td>" . $tecnico['meta_total'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Verificar configuração da API
echo "<h3>🔧 Configuração da API Intranet</h3>";

try {
    require_once '../cadastro/config_api.php';
    echo "<p>✅ <strong>Arquivo config_api.php carregado com sucesso</strong></p>";
    
    // Verificar se a função existe
    if (function_exists('getIntranetAPI')) {
        echo "<p>✅ <strong>Função getIntranetAPI() encontrada</strong></p>";
    } else {
        echo "<p>❌ <strong>Função getIntranetAPI() NÃO encontrada</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Erro ao carregar config_api.php:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Testar API da Intranet
echo "<h3>🌐 Teste da API Intranet</h3>";

try {
    require_once '../classes/Logger.php';
    $logger = new MciLogger();
    
    $intranetAPI = getIntranetAPI($logger);
    echo "<p>✅ <strong>API Intranet inicializada</strong></p>";
    
    // Buscar usuários da intranet
    $usuarios_intranet = [];
    try {
        $usuarios_intranet = $intranetAPI->criarMapaUsuariosPorEmail();
        echo "<p>✅ <strong>Cache de usuários criado com " . count($usuarios_intranet) . " usuários</strong></p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ <strong>Erro ao buscar usuários:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Erro ao inicializar API:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    $usuarios_intranet = [];
}

// Verificar cada técnico na API
echo "<h3>👤 Verificação Individual dos Técnicos</h3>";

foreach ($tecnicos_dados as $tecnico) {
    $destaque = ($tecnico['id'] == 58) ? 'style="border: 2px solid #ffc107; background-color: #fff3cd; padding: 15px;"' : 'style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;"';
    
    echo "<div $destaque>";
    echo "<h4>" . ($tecnico['id'] == 58 ? "🎯 " : "") . "Técnico ID " . $tecnico['id'] . ": " . htmlspecialchars($tecnico['nome']) . "</h4>";
    
    $email = $tecnico['email'];
    echo "<p><strong>Email:</strong> " . htmlspecialchars($email) . "</p>";
    
    if (empty($email)) {
        echo "<p style='color: red;'>❌ <strong>Email vazio - não pode buscar na API</strong></p>";
    } else {
        $email_key = strtolower(trim($email));
        echo "<p><strong>Email normalizado:</strong> " . htmlspecialchars($email_key) . "</p>";
        
        if (isset($usuarios_intranet[$email_key])) {
            $usuario_intranet = $usuarios_intranet[$email_key];
            echo "<p style='color: green;'>✅ <strong>Usuário encontrado na API Intranet!</strong></p>";
            
            echo "<div style='margin-left: 20px;'>";
            echo "<p><strong>Nome na API:</strong> " . htmlspecialchars($usuario_intranet['nome'] ?? 'N/A') . "</p>";
            echo "<p><strong>Email na API:</strong> " . htmlspecialchars($usuario_intranet['email'] ?? 'N/A') . "</p>";
            echo "<p><strong>Setor:</strong> " . htmlspecialchars($usuario_intranet['setor_nome'] ?? 'N/A') . "</p>";
            echo "<p><strong>Função:</strong> " . htmlspecialchars($usuario_intranet['funcao_nome'] ?? 'N/A') . "</p>";
            
            $foto_url = $usuario_intranet['foto_url'] ?? null;
            if ($foto_url) {
                echo "<p style='color: green;'><strong>Foto URL:</strong> " . htmlspecialchars($foto_url) . "</p>";
                echo "<p><strong>Teste da foto:</strong></p>";
                echo "<img src='" . htmlspecialchars($foto_url) . "' alt='Foto' style='width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 2px solid #28a745;' onerror=\"this.style.border='2px solid #dc3545'; this.alt='❌ Erro ao carregar';\">";
            } else {
                echo "<p style='color: orange;'>⚠️ <strong>Foto URL não disponível na API</strong></p>";
            }
            echo "</div>";
            
        } else {
            echo "<p style='color: red;'>❌ <strong>Usuário NÃO encontrado na API Intranet</strong></p>";
            
            // Buscar emails similares
            $emails_similares = [];
            foreach ($usuarios_intranet as $api_email => $api_user) {
                if (stripos($api_email, explode('@', $email_key)[0]) !== false) {
                    $emails_similares[] = $api_email;
                }
            }
            
            if (!empty($emails_similares)) {
                echo "<p><strong>Emails similares encontrados:</strong></p>";
                echo "<ul>";
                foreach ($emails_similares as $email_similar) {
                    echo "<li>" . htmlspecialchars($email_similar) . "</li>";
                }
                echo "</ul>";
            }
        }
    }
    
    echo "</div>";
}

// Verificar especificamente o Maycon
echo "<h3>🎯 Análise Específica - Maycon (ID 58)</h3>";

$maycon = null;
foreach ($tecnicos_dados as $tecnico) {
    if ($tecnico['id'] == 58) {
        $maycon = $tecnico;
        break;
    }
}

if ($maycon) {
    echo "<div style='border: 2px solid #007bff; padding: 15px; background-color: #e7f3ff;'>";
    echo "<h4>📋 Dados do Maycon</h4>";
    echo "<p><strong>ID:</strong> " . $maycon['id'] . "</p>";
    echo "<p><strong>Nome:</strong> " . htmlspecialchars($maycon['nome']) . "</p>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($maycon['email']) . "</p>";
    
    // Buscar manualmente na API
    if (!empty($usuarios_intranet)) {
        echo "<h5>🔍 Busca Manual na API</h5>";
        
        $email_maycon = strtolower(trim($maycon['email']));
        echo "<p><strong>Buscando por:</strong> " . htmlspecialchars($email_maycon) . "</p>";
        
        $encontrado = false;
        foreach ($usuarios_intranet as $api_email => $api_user) {
            if ($api_email === $email_maycon) {
                $encontrado = true;
                echo "<p style='color: green;'>✅ <strong>ENCONTRADO!</strong></p>";
                echo "<pre>" . print_r($api_user, true) . "</pre>";
                break;
            }
        }
        
        if (!$encontrado) {
            echo "<p style='color: red;'>❌ <strong>NÃO ENCONTRADO</strong></p>";
            
            // Listar alguns emails da API para comparação
            echo "<h5>📋 Primeiros 10 emails da API (para comparação):</h5>";
            $count = 0;
            foreach ($usuarios_intranet as $api_email => $api_user) {
                if ($count >= 10) break;
                echo "<p>" . htmlspecialchars($api_email) . " - " . htmlspecialchars($api_user['nome'] ?? 'N/A') . "</p>";
                $count++;
            }
        }
    }
    
    echo "</div>";
} else {
    echo "<p style='color: red;'>❌ <strong>Maycon (ID 58) não encontrado nos dados dos técnicos!</strong></p>";
}

echo "<hr>";
echo "<p><a href='dashboard.php'>🚀 Dashboard</a> | <a href='debug_tecnicos.php'>🔧 Debug Geral</a> | <a href='../'>⬅️ Voltar</a></p>";
?>
