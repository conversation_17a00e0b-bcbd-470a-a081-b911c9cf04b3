<?php
require_once 'auth_check.php';

echo "<h1>Teste Específico - Conversão de Datas Brasileiras</h1>";
echo "<hr>";

// Função corrigida para converter datas
function convertExcelDate($value) {
    if (empty($value)) {
        return null;
    }
    
    // Se for uma string, tentar converter
    if (is_string($value)) {
        $value = trim($value);
        if (empty($value)) {
            return null;
        }
        
        // FORMATO BRASILEIRO: dd/mm/yyyy (PRIORIDADE MÁXIMA)
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $value, $matches)) {
            $dia = intval($matches[1]);
            $mes = intval($matches[2]);
            $ano = intval($matches[3]);
            
            // Validar se a data é válida (dia, mês, ano)
            if ($dia >= 1 && $dia <= 31 && $mes >= 1 && $mes <= 12 && $ano >= 1900 && $ano <= 2100) {
                // Verificar se a data é realmente válida
                if (checkdate($mes, $dia, $ano)) {
                    // Formatar com zeros à esquerda
                    $dia_fmt = str_pad($dia, 2, '0', STR_PAD_LEFT);
                    $mes_fmt = str_pad($mes, 2, '0', STR_PAD_LEFT);
                    
                    return "$ano-$mes_fmt-$dia_fmt";
                }
            }
        }
    }
    
    return null;
}

echo "<h3>Teste com Datas Específicas do Problema</h3>";

$casos_teste = [
    [
        'entrada' => '02/01/2024',
        'esperado' => '2024-01-02',
        'descricao' => '2 de janeiro de 2024'
    ],
    [
        'entrada' => '15/03/2024',
        'esperado' => '2024-03-15',
        'descricao' => '15 de março de 2024'
    ],
    [
        'entrada' => '31/12/2023',
        'esperado' => '2023-12-31',
        'descricao' => '31 de dezembro de 2023'
    ],
    [
        'entrada' => '1/5/2024',
        'esperado' => '2024-05-01',
        'descricao' => '1 de maio de 2024'
    ],
    [
        'entrada' => '29/02/2024',
        'esperado' => '2024-02-29',
        'descricao' => '29 de fevereiro de 2024 (ano bissexto)'
    ],
    [
        'entrada' => '29/02/2023',
        'esperado' => null,
        'descricao' => '29 de fevereiro de 2023 (inválido - não é bissexto)'
    ]
];

echo "<table border='1' cellpadding='10'>";
echo "<tr style='background-color: #f8f9fa;'>";
echo "<th>Entrada</th>";
echo "<th>Resultado</th>";
echo "<th>Esperado</th>";
echo "<th>Descrição</th>";
echo "<th>Status</th>";
echo "</tr>";

$total_testes = 0;
$testes_corretos = 0;

foreach ($casos_teste as $caso) {
    $total_testes++;
    $resultado = convertExcelDate($caso['entrada']);
    $correto = ($resultado === $caso['esperado']);
    
    if ($correto) {
        $testes_corretos++;
    }
    
    $status_cor = $correto ? 'green' : 'red';
    $status_icone = $correto ? '✅' : '❌';
    
    echo "<tr>";
    echo "<td><strong>{$caso['entrada']}</strong></td>";
    echo "<td>" . ($resultado ?? 'NULL') . "</td>";
    echo "<td>" . ($caso['esperado'] ?? 'NULL') . "</td>";
    echo "<td>{$caso['descricao']}</td>";
    echo "<td style='color: $status_cor;'>$status_icone</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Resultado do Teste</h3>";
echo "<p><strong>Testes corretos:</strong> $testes_corretos de $total_testes</p>";

if ($testes_corretos === $total_testes) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "<h4>🎉 Todos os testes passaram!</h4>";
    echo "<p>A função de conversão de datas está funcionando corretamente.</p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h4>⚠️ Alguns testes falharam</h4>";
    echo "<p>A função precisa de mais ajustes.</p>";
    echo "</div>";
}

echo "<h3>Teste com Inserção no Banco</h3>";

if ($testes_corretos === $total_testes) {
    try {
        // Testar inserção real no banco
        $data_teste = [
            'pa' => 'TESTE_DATA',
            'nome_cliente' => 'Teste Conversão Data',
            'numero_cpf_cnpj' => '12345678901',
            'cnae' => '1234-5/67',
            'data_ultima_atualizacao_renda' => convertExcelDate('02/01/2024'),
            'sigla_tipo_pessoa' => 'PF',
            'profissao' => 'Teste',
            'deposito_total' => 1000.00,
            'saldo_devedor' => 150.00,
            'funcionario' => 'Sistema',
            'data_solicitacao_laudo' => convertExcelDate('15/03/2024'),
            'tecnico_responsavel' => 'Teste',
            'data_atual_sisbr' => convertExcelDate('31/12/2023'),
            'usuario_cadastro' => $_SESSION['user_id']
        ];
        
        // Verificar se já existe
        $stmt = $pdo_mci->prepare("SELECT id FROM cad_registros WHERE pa = ? AND nome_cliente = ?");
        $stmt->execute([$data_teste['pa'], $data_teste['nome_cliente']]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p>⚠️ Registro de teste já existe. Removendo para refazer o teste...</p>";
            $stmt_del = $pdo_mci->prepare("DELETE FROM cad_registros WHERE pa = ? AND nome_cliente = ?");
            $stmt_del->execute([$data_teste['pa'], $data_teste['nome_cliente']]);
        }
        
        // Inserir registro de teste
        $stmt = $pdo_mci->prepare("
            INSERT INTO cad_registros (
                pa, nome_cliente, numero_cpf_cnpj, cnae, data_ultima_atualizacao_renda,
                sigla_tipo_pessoa, profissao, deposito_total, saldo_devedor, funcionario,
                data_solicitacao_laudo, tecnico_responsavel, data_atual_sisbr, usuario_cadastro
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $data_teste['pa'], $data_teste['nome_cliente'], $data_teste['numero_cpf_cnpj'], 
            $data_teste['cnae'], $data_teste['data_ultima_atualizacao_renda'], 
            $data_teste['sigla_tipo_pessoa'], $data_teste['profissao'], $data_teste['deposito_total'], 
            $data_teste['saldo_devedor'], $data_teste['funcionario'], $data_teste['data_solicitacao_laudo'], 
            $data_teste['tecnico_responsavel'], $data_teste['data_atual_sisbr'], 
            $data_teste['usuario_cadastro']
        ]);
        
        $id_inserido = $pdo_mci->lastInsertId();
        echo "<p style='color: green;'>✅ Registro inserido com sucesso! ID: $id_inserido</p>";
        
        // Verificar como ficou no banco
        $stmt = $pdo_mci->prepare("
            SELECT pa, nome_cliente, data_ultima_atualizacao_renda, data_solicitacao_laudo, data_atual_sisbr 
            FROM cad_registros WHERE id = ?
        ");
        $stmt->execute([$id_inserido]);
        $registro = $stmt->fetch();
        
        echo "<h4>Dados no banco de dados:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Campo</th><th>Valor Original</th><th>Valor no Banco</th><th>Correto?</th></tr>";
        
        $verificacoes = [
            ['campo' => 'Data Última Atualização', 'original' => '02/01/2024', 'banco' => $registro['data_ultima_atualizacao_renda'], 'esperado' => '2024-01-02'],
            ['campo' => 'Data Solicitação Laudo', 'original' => '15/03/2024', 'banco' => $registro['data_solicitacao_laudo'], 'esperado' => '2024-03-15'],
            ['campo' => 'Data Atual SISBR', 'original' => '31/12/2023', 'banco' => $registro['data_atual_sisbr'], 'esperado' => '2023-12-31']
        ];
        
        foreach ($verificacoes as $verif) {
            $correto = ($verif['banco'] === $verif['esperado']);
            $status = $correto ? '✅' : '❌';
            $cor = $correto ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>{$verif['campo']}</td>";
            echo "<td>{$verif['original']}</td>";
            echo "<td>{$verif['banco']}</td>";
            echo "<td style='color: $cor;'>$status</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erro no teste de inserção: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

echo "<hr>";
echo "<h3>Conclusão</h3>";

if ($testes_corretos === $total_testes) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #155724;'>🎉 Correção Implementada com Sucesso!</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li>✅ Formato brasileiro dd/mm/yyyy é interpretado corretamente</li>";
    echo "<li>✅ 02/01/2024 agora vira 2024-01-02 (2 de janeiro)</li>";
    echo "<li>✅ Validação de datas funcionando</li>";
    echo "<li>✅ Inserção no banco funcionando</li>";
    echo "</ul>";
    echo "<p><strong>Agora você pode fazer a importação da sua planilha!</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24;'>⚠️ Ainda há problemas</h4>";
    echo "<p style='color: #721c24;'>A função precisa de mais ajustes antes da importação.</p>";
    echo "</div>";
}

echo "<p><a href='importar.php'>Testar importação</a> | <a href='index.php'>Voltar ao sistema</a></p>";
?>
