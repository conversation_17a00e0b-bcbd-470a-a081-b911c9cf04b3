<?php
require_once 'auth_check.php';

echo "<h1>Debug - Erros de Importação</h1>";
echo "<hr>";

try {
    // Buscar a última importação
    $stmt = $pdo_mci->prepare("
        SELECT * FROM cad_importacoes 
        ORDER BY data_importacao DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $ultima_importacao = $stmt->fetch();
    
    if (!$ultima_importacao) {
        echo "<p>Nenhuma importação encontrada.</p>";
        exit;
    }
    
    echo "<h3>Última Importação</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><td><strong>ID:</strong></td><td>{$ultima_importacao['id']}</td></tr>";
    echo "<tr><td><strong>Arquivo:</strong></td><td>{$ultima_importacao['nome_arquivo']}</td></tr>";
    echo "<tr><td><strong>Total Registros:</strong></td><td>{$ultima_importacao['total_registros']}</td></tr>";
    echo "<tr><td><strong>Importados:</strong></td><td>{$ultima_importacao['registros_importados']}</td></tr>";
    echo "<tr><td><strong>Erros:</strong></td><td>{$ultima_importacao['registros_erro']}</td></tr>";
    echo "<tr><td><strong>Status:</strong></td><td>{$ultima_importacao['status']}</td></tr>";
    echo "<tr><td><strong>Data:</strong></td><td>{$ultima_importacao['data_importacao']}</td></tr>";
    echo "</table>";
    
    // Buscar erros da última importação
    echo "<h3>Erros da Última Importação (primeiros 20)</h3>";
    $stmt = $pdo_mci->prepare("
        SELECT * FROM cad_erros_importacao 
        WHERE importacao_id = ? 
        ORDER BY linha 
        LIMIT 20
    ");
    $stmt->execute([$ultima_importacao['id']]);
    $erros = $stmt->fetchAll();
    
    if (empty($erros)) {
        echo "<p>Nenhum erro registrado.</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Linha</th><th>Campo</th><th>Valor</th><th>Erro</th></tr>";
        
        foreach ($erros as $erro) {
            echo "<tr>";
            echo "<td>{$erro['linha']}</td>";
            echo "<td>" . htmlspecialchars($erro['campo'] ?? '-') . "</td>";
            echo "<td>" . htmlspecialchars($erro['valor'] ?? '-') . "</td>";
            echo "<td>" . htmlspecialchars($erro['erro']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Verificar se há dados na tabela principal
    echo "<h3>Registros na Tabela Principal</h3>";
    $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_registros");
    $stmt->execute();
    $total_registros = $stmt->fetchColumn();
    echo "<p>Total de registros na tabela cad_registros: <strong>$total_registros</strong></p>";
    
    if ($total_registros > 0) {
        echo "<h4>Primeiros 5 registros:</h4>";
        $stmt = $pdo_mci->prepare("SELECT * FROM cad_registros ORDER BY id DESC LIMIT 5");
        $stmt->execute();
        $registros = $stmt->fetchAll();
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>PA</th><th>Nome Cliente</th><th>CPF/CNPJ</th><th>Status</th><th>Data Cadastro</th></tr>";
        
        foreach ($registros as $registro) {
            echo "<tr>";
            echo "<td>{$registro['id']}</td>";
            echo "<td>" . htmlspecialchars($registro['pa']) . "</td>";
            echo "<td>" . htmlspecialchars($registro['nome_cliente']) . "</td>";
            echo "<td>" . htmlspecialchars($registro['numero_cpf_cnpj']) . "</td>";
            echo "<td>{$registro['status']}</td>";
            echo "<td>{$registro['data_cadastro']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Testar conexão com banco
    echo "<h3>Teste de Conexão</h3>";
    echo "<p>✅ Conexão com banco MCI funcionando</p>";
    
    // Verificar estrutura da tabela
    echo "<h3>Estrutura da Tabela cad_registros</h3>";
    $stmt = $pdo_mci->prepare("DESCRIBE cad_registros");
    $stmt->execute();
    $colunas = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th></tr>";
    
    foreach ($colunas as $coluna) {
        echo "<tr>";
        echo "<td>{$coluna['Field']}</td>";
        echo "<td>{$coluna['Type']}</td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>{$coluna['Key']}</td>";
        echo "<td>" . ($coluna['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='importar.php'>Voltar para Importação</a> | <a href='index.php'>Página Principal</a></p>";
?>
