<?php
// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

require_once '../auth_check.php';

// Buscar dados dos técnicos agrícolas (mesma consulta do dashboard principal)
$stmt = $pdo_mci->prepare("
    SELECT
        u.id,
        u.nome_completo as nome,
        u.email,
        COUNT(r.id) as meta_total,
        COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) as atualizados_total,
        ROUND(
            (COUNT(CASE WHEN s.nome = 'Atualizado' THEN 1 END) / COUNT(r.id)) * 100, 1
        ) as progresso_total
    FROM sicoob_access_control.usuarios u
    INNER JOIN cad_registros r ON u.id = r.tecnico_responsavel
    LEFT JOIN cad_status s ON r.status = s.id
    WHERE u.ativo = 1 AND r.tecnico_responsavel IS NOT NULL
    GROUP BY u.id, u.nome_completo, u.email
    HAVING meta_total > 0
    ORDER BY meta_total DESC, u.nome_completo
    LIMIT 6
");
$stmt->execute();
$tecnicos_dados = $stmt->fetchAll();

// Calcular métricas gerais
$total_meta = array_sum(array_column($tecnicos_dados, 'meta_total'));
$total_atualizados = array_sum(array_column($tecnicos_dados, 'atualizados_total'));
$progresso_geral = $total_meta > 0 ? round(($total_atualizados / $total_meta) * 100, 1) : 0;

// Calcular segmentos da barra de progresso por técnico
$cores_tecnicos = ['#00AE9D', '#003641', '#C9D200', '#70B86C', '#494790', '#6c757d'];
$segmentos_progresso = [];
$tecnico_index_calc = 0;

foreach ($tecnicos_dados as $tecnico) {
    if ($total_meta > 0) {
        $porcentagem_contribuicao = ($tecnico['atualizados_total'] / $total_meta) * 100;
        $segmentos_progresso[] = [
            'porcentagem' => $porcentagem_contribuicao,
            'cor' => $cores_tecnicos[$tecnico_index_calc % count($cores_tecnicos)],
            'nome' => $tecnico['nome'],
            'atualizados' => $tecnico['atualizados_total']
        ];
    }
    $tecnico_index_calc++;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard MCI - Técnicos Agrícolas (HÍBRIDO)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* CSS EXATO do dashboard principal */
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-roxo: #494790;
            --sicoob-cinza: #6c757d;
            
            --cor-tecnico-1: #00AE9D;
            --cor-tecnico-2: #003641;
            --cor-tecnico-3: #C9D200;
            --cor-tecnico-4: #70B86C;
            --cor-tecnico-5: #494790;
            --cor-tecnico-6: #6c757d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #003641 0%, #00AE9D 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .dashboard-container {
            padding: 0.5rem;
            height: 100vh;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: white;
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .dashboard-header h1 {
            font-size: clamp(1.5rem, 3vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .dashboard-header .subtitle {
            font-size: clamp(0.8rem, 1.5vw, 1.1rem);
            opacity: 0.9;
        }

        .overview-section {
            margin-bottom: 1.5rem;
            flex-shrink: 0;
        }

        .overview-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .overview-card:hover {
            transform: translateY(-2px);
        }

        .overview-card h3 {
            color: var(--sicoob-verde-escuro);
            font-size: clamp(1rem, 2vw, 1.4rem);
            margin-bottom: 0.5rem;
        }

        .overview-value {
            font-size: clamp(1.5rem, 3vw, 2rem);
            font-weight: 700;
            color: var(--sicoob-turquesa);
            margin-bottom: 0.5rem;
        }

        .overview-progress {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            color: #6c757d;
            margin-bottom: 1rem;
        }

        .progress-bar-overview {
            height: 12px;
            background-color: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            display: flex;
        }

        .progress-segment {
            height: 100%;
            transition: width 0.8s ease;
            position: relative;
        }

        .tecnicos-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 0.8rem;
            flex: 1;
            overflow: hidden;
            height: 100%;
        }

        .tecnico-card {
            background: white;
            border-radius: 16px;
            padding: clamp(0.8rem, 2vw, 1.5rem);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            border-top: 3px solid var(--cor-tecnico, var(--sicoob-turquesa));
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            text-align: center;
            height: 100%;
            min-height: 0;
            overflow: hidden;
        }

        .tecnico-avatar-container {
            position: relative;
            margin-bottom: clamp(0.5rem, 1.5vw, 1rem);
            flex-shrink: 0;
        }

        .tecnico-avatar {
            width: clamp(60px, 8vw, 90px);
            height: clamp(60px, 8vw, 90px);
            border-radius: 50%;
            object-fit: cover;
            position: relative;
            z-index: 2;
        }

        .tecnico-avatar.placeholder {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: clamp(1.2rem, 4vw, 2.2rem);
            font-weight: 700;
        }

        .tecnico-info {
            flex: 0 0 auto;
            margin-bottom: clamp(0.3rem, 1vw, 0.8rem);
            width: 100%;
        }

        .tecnico-info h4 {
            color: var(--sicoob-verde-escuro);
            font-size: clamp(0.9rem, 2.2vw, 1.4rem);
            margin-bottom: 0;
            font-weight: 600;
            line-height: 1.2;
            word-wrap: break-word;
            hyphens: auto;
        }

        .metric-info {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            min-height: 0;
        }

        .metric-label {
            font-size: clamp(0.8rem, 1.8vw, 1.1rem);
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
            margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);
        }

        .metric-value {
            font-size: clamp(1.1rem, 2.5vw, 1.6rem);
            font-weight: 700;
            color: var(--cor-tecnico, var(--sicoob-turquesa));
            margin-bottom: clamp(0.2rem, 0.5vw, 0.4rem);
            line-height: 1.1;
        }

        .metric-percentage {
            font-size: clamp(1rem, 2.2vw, 1.4rem);
            font-weight: 600;
            color: var(--sicoob-verde-escuro);
        }

        /* Classes específicas para cada técnico */
        .tecnico-1 { --cor-tecnico: var(--cor-tecnico-1); }
        .tecnico-2 { --cor-tecnico: var(--cor-tecnico-2); }
        .tecnico-3 { --cor-tecnico: var(--cor-tecnico-3); }
        .tecnico-4 { --cor-tecnico: var(--cor-tecnico-4); }
        .tecnico-5 { --cor-tecnico: var(--cor-tecnico-5); }
        .tecnico-6 { --cor-tecnico: var(--cor-tecnico-6); }

        .debug-info {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 5px;
            margin: 5px 0;
            font-size: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="dashboard-header">
            <h1><i class="fas fa-seedling"></i> MCI - TÉCNICOS AGRÍCOLAS (HÍBRIDO)</h1>
            <div class="subtitle">Técnicos Agrícolas - Teste Híbrido - CSS Principal + Estrutura Simples</div>
        </div>

        <!-- Debug Info -->
        <div class="debug-info">
            <strong>DEBUG:</strong> Timestamp: <?php echo date('Y-m-d H:i:s'); ?> | 
            Total: <?php echo count($tecnicos_dados); ?> técnicos | 
            IDs: <?php echo implode(', ', array_column($tecnicos_dados, 'id')); ?>
        </div>

        <!-- Overview Geral -->
        <div class="overview-section">
            <div class="overview-card">
                <h3><i class="fas fa-chart-line"></i> Meta Total da Equipe</h3>
                <div class="overview-value"><?php echo number_format($total_meta); ?></div>
                <div class="overview-progress"><?php echo $total_atualizados; ?> concluídos (<?php echo $progresso_geral; ?>%)</div>
                <div class="progress-bar-overview">
                    <?php foreach ($segmentos_progresso as $segmento): ?>
                        <div class="progress-segment" 
                             style="width: <?php echo $segmento['porcentagem']; ?>%; background-color: <?php echo $segmento['cor']; ?>;"
                             title="<?php echo htmlspecialchars($segmento['nome']); ?>: <?php echo $segmento['atualizados']; ?> atualizações">
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Grid de Técnicos -->
        <div class="tecnicos-grid">
            <?php 
            $tecnico_index = 1;
            foreach ($tecnicos_dados as $tecnico): 
            ?>
            <div class="tecnico-card tecnico-<?php echo $tecnico_index; ?>">
                <div class="debug-info">
                    Card <?php echo $tecnico_index; ?>: ID <?php echo $tecnico['id']; ?> - <?php echo htmlspecialchars($tecnico['nome']); ?>
                </div>
                
                <div class="tecnico-avatar-container">
                    <div class="tecnico-avatar placeholder">
                        <?php echo strtoupper(substr($tecnico['nome'], 0, 1)); ?>
                    </div>
                </div>

                <div class="tecnico-info">
                    <h4><?php echo htmlspecialchars($tecnico['nome']); ?></h4>
                </div>

                <div class="metric-info">
                    <div class="metric-label">Meta Total</div>
                    <div class="metric-value"><?php echo $tecnico['atualizados_total']; ?>/<?php echo $tecnico['meta_total']; ?></div>
                    <div class="metric-percentage"><?php echo $tecnico['progresso_total']; ?>%</div>
                </div>
            </div>
            <?php 
            $tecnico_index++;
            endforeach; 
            ?>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.tecnico-card');
            console.log('=== DEBUG DASHBOARD HÍBRIDO ===');
            console.log('Total de cards encontrados:', cards.length);
            
            cards.forEach((card, index) => {
                const nome = card.querySelector('h4')?.textContent || 'Nome não encontrado';
                const debug = card.querySelector('.debug-info')?.textContent || 'Debug não encontrado';
                console.log(`Card ${index + 1}: ${nome}`);
                console.log(`  Debug: ${debug}`);
            });
            
            // Verificar problemas específicos
            const nomes = Array.from(cards).map(card => card.querySelector('h4')?.textContent || '');
            const felipe = nomes.find(nome => nome.includes('Felipe'));
            const sidnei = nomes.filter(nome => nome.includes('Sidnei'));
            const maycon = nomes.filter(nome => nome.includes('Maycon'));
            
            console.log('Felipe encontrado:', felipe || 'NÃO');
            console.log('Sidnei encontrado:', sidnei.length, 'vez(es)');
            console.log('Maycon encontrado:', maycon.length, 'vez(es)');
            
            if (sidnei.length > 1) {
                console.error('PROBLEMA: Sidnei duplicado!');
            }
            if (!felipe) {
                console.error('PROBLEMA: Felipe não encontrado!');
            }
            if (cards.length !== 5) {
                console.error('PROBLEMA: Deveria ter 5 cards, mas tem', cards.length);
            }
        });
    </script>
</body>
</html>
