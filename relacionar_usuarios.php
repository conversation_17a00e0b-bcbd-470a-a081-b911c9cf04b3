<?php
require_once 'auth_check.php';

// Verificar se é admin
checkMciAccess('admin');

// Verificar se a conexão com sicoob_access_control existe
if (!isset($pdo_sicoob)) {
    echo "<h1>Erro de Configuração</h1>";
    echo "<p style='color: red;'>❌ Conexão com banco sicoob_access_control não encontrada.</p>";
    echo "<p>Verifique se o arquivo de configuração está correto.</p>";
    exit;
}

echo "<h1>Relacionar Funcionários e Técnicos com Tabela de Usuários</h1>";
echo "<hr>";

try {
    echo "<h3>1. Analisando estrutura atual...</h3>";
    
    // Verificar estrutura atual das colunas
    $stmt = $pdo_mci->prepare("SHOW COLUMNS FROM cad_registros WHERE Field IN ('funcionario', 'tecnico_responsavel')");
    $stmt->execute();
    $colunas_atuais = $stmt->fetchAll();
    
    echo "<h4>Estrutura atual das colunas:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Padrão</th></tr>";
    foreach ($colunas_atuais as $coluna) {
        echo "<tr>";
        echo "<td><strong>{$coluna['Field']}</strong></td>";
        echo "<td><code>{$coluna['Type']}</code></td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>" . ($coluna['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar dados atuais
    echo "<h3>2. Analisando dados atuais...</h3>";
    
    $stmt = $pdo_mci->prepare("
        SELECT 
            funcionario, COUNT(*) as total_funcionario,
            tecnico_responsavel, COUNT(*) as total_tecnico
        FROM cad_registros 
        WHERE funcionario IS NOT NULL OR tecnico_responsavel IS NOT NULL
        GROUP BY funcionario, tecnico_responsavel
        ORDER BY total_funcionario DESC, total_tecnico DESC
        LIMIT 10
    ");
    $stmt->execute();
    $dados_atuais = $stmt->fetchAll();
    
    echo "<h4>Amostra dos dados atuais (top 10):</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Funcionário</th><th>Qtd</th><th>Técnico Responsável</th><th>Qtd</th></tr>";
    foreach ($dados_atuais as $dado) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($dado['funcionario'] ?? 'NULL') . "</td>";
        echo "<td>{$dado['total_funcionario']}</td>";
        echo "<td>" . htmlspecialchars($dado['tecnico_responsavel'] ?? 'NULL') . "</td>";
        echo "<td>{$dado['total_tecnico']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar usuários disponíveis
    echo "<h3>3. Verificando usuários disponíveis...</h3>";
    
    try {
        $stmt = $pdo_sicoob->prepare("SELECT id, nome_completo, email, ativo FROM usuarios WHERE ativo = 1 ORDER BY nome_completo LIMIT 10");
        $stmt->execute();
        $usuarios_disponiveis = $stmt->fetchAll();
        
        echo "<h4>Usuários disponíveis (amostra):</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Nome Completo</th><th>Email</th><th>Status</th></tr>";
        foreach ($usuarios_disponiveis as $usuario) {
            echo "<tr>";
            echo "<td>{$usuario['id']}</td>";
            echo "<td>" . htmlspecialchars($usuario['nome_completo']) . "</td>";
            echo "<td>" . htmlspecialchars($usuario['email']) . "</td>";
            echo "<td>" . ($usuario['ativo'] ? '✅ Ativo' : '❌ Inativo') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $stmt_count = $pdo_sicoob->prepare("SELECT COUNT(*) FROM usuarios WHERE ativo = 1");
        $stmt_count->execute();
        $total_usuarios = $stmt_count->fetchColumn();
        echo "<p><strong>Total de usuários ativos:</strong> $total_usuarios</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erro ao acessar tabela de usuários: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>Verifique se a conexão com o banco sicoob_access_control está funcionando.</p>";
        exit;
    }
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['executar_relacionamento'])) {
        echo "<h3>4. Executando relacionamento...</h3>";
        
        // Passo 1: Criar colunas temporárias
        echo "<p>Passo 1: Criando colunas temporárias...</p>";
        try {
            $pdo_mci->exec("ALTER TABLE cad_registros ADD COLUMN funcionario_id INT NULL COMMENT 'ID do funcionário (referência para sicoob_access_control.usuarios)'");
            $pdo_mci->exec("ALTER TABLE cad_registros ADD COLUMN tecnico_responsavel_id INT NULL COMMENT 'ID do técnico responsável (referência para sicoob_access_control.usuarios)'");
            echo "<p>✅ Colunas temporárias criadas</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p style='color: orange;'>⚠️ Colunas temporárias já existem</p>";
            } else {
                echo "<p style='color: red;'>❌ Erro ao criar colunas temporárias: " . htmlspecialchars($e->getMessage()) . "</p>";
                exit;
            }
        }
        
        // Passo 2: Tentar mapear nomes existentes para IDs de usuários
        echo "<p>Passo 2: Mapeando nomes para IDs de usuários...</p>";
        
        $mapeamentos_funcionario = 0;
        $mapeamentos_tecnico = 0;
        
        // Buscar todos os usuários para mapeamento
        $stmt = $pdo_sicoob->prepare("SELECT id, nome_completo FROM usuarios WHERE ativo = 1");
        $stmt->execute();
        $todos_usuarios = $stmt->fetchAll();
        
        foreach ($todos_usuarios as $usuario) {
            $nome_usuario = $usuario['nome_completo'];
            $id_usuario = $usuario['id'];
            
            // Mapear funcionários
            try {
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros 
                    SET funcionario_id = ? 
                    WHERE funcionario = ? AND funcionario_id IS NULL
                ");
                $stmt->execute([$id_usuario, $nome_usuario]);
                $affected = $stmt->rowCount();
                if ($affected > 0) {
                    echo "<p>✅ Mapeados $affected registros de funcionário '$nome_usuario' para ID $id_usuario</p>";
                    $mapeamentos_funcionario += $affected;
                }
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Erro ao mapear funcionário '$nome_usuario': " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
            // Mapear técnicos responsáveis
            try {
                $stmt = $pdo_mci->prepare("
                    UPDATE cad_registros 
                    SET tecnico_responsavel_id = ? 
                    WHERE tecnico_responsavel = ? AND tecnico_responsavel_id IS NULL
                ");
                $stmt->execute([$id_usuario, $nome_usuario]);
                $affected = $stmt->rowCount();
                if ($affected > 0) {
                    echo "<p>✅ Mapeados $affected registros de técnico '$nome_usuario' para ID $id_usuario</p>";
                    $mapeamentos_tecnico += $affected;
                }
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Erro ao mapear técnico '$nome_usuario': " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        echo "<p><strong>Total de mapeamentos:</strong></p>";
        echo "<ul>";
        echo "<li>Funcionários: $mapeamentos_funcionario registros</li>";
        echo "<li>Técnicos: $mapeamentos_tecnico registros</li>";
        echo "</ul>";
        
        // Passo 3: Verificar registros não mapeados
        echo "<p>Passo 3: Verificando registros não mapeados...</p>";
        
        $stmt = $pdo_mci->prepare("
            SELECT DISTINCT funcionario 
            FROM cad_registros 
            WHERE funcionario IS NOT NULL 
            AND funcionario != '' 
            AND funcionario_id IS NULL
            LIMIT 10
        ");
        $stmt->execute();
        $funcionarios_nao_mapeados = $stmt->fetchAll();
        
        $stmt = $pdo_mci->prepare("
            SELECT DISTINCT tecnico_responsavel 
            FROM cad_registros 
            WHERE tecnico_responsavel IS NOT NULL 
            AND tecnico_responsavel != '' 
            AND tecnico_responsavel_id IS NULL
            LIMIT 10
        ");
        $stmt->execute();
        $tecnicos_nao_mapeados = $stmt->fetchAll();
        
        if (!empty($funcionarios_nao_mapeados)) {
            echo "<h5>Funcionários não mapeados (amostra):</h5>";
            echo "<ul>";
            foreach ($funcionarios_nao_mapeados as $func) {
                echo "<li>" . htmlspecialchars($func['funcionario']) . "</li>";
            }
            echo "</ul>";
        }
        
        if (!empty($tecnicos_nao_mapeados)) {
            echo "<h5>Técnicos não mapeados (amostra):</h5>";
            echo "<ul>";
            foreach ($tecnicos_nao_mapeados as $tec) {
                echo "<li>" . htmlspecialchars($tec['tecnico_responsavel']) . "</li>";
            }
            echo "</ul>";
        }
        
        // Passo 4: Adicionar chaves estrangeiras (opcional, pode dar erro se houver dados não mapeados)
        echo "<p>Passo 4: Tentando adicionar chaves estrangeiras...</p>";
        
        try {
            $pdo_mci->exec("
                ALTER TABLE cad_registros 
                ADD CONSTRAINT fk_registros_funcionario 
                FOREIGN KEY (funcionario_id) REFERENCES sicoob_access_control.usuarios(id) 
                ON UPDATE CASCADE ON DELETE SET NULL
            ");
            echo "<p>✅ Chave estrangeira para funcionario_id adicionada</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Não foi possível adicionar FK para funcionario_id: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>Isso pode acontecer se houver registros com funcionario_id que não existem na tabela usuarios.</p>";
        }
        
        try {
            $pdo_mci->exec("
                ALTER TABLE cad_registros 
                ADD CONSTRAINT fk_registros_tecnico 
                FOREIGN KEY (tecnico_responsavel_id) REFERENCES sicoob_access_control.usuarios(id) 
                ON UPDATE CASCADE ON DELETE SET NULL
            ");
            echo "<p>✅ Chave estrangeira para tecnico_responsavel_id adicionada</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Não foi possível adicionar FK para tecnico_responsavel_id: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>Isso pode acontecer se houver registros com tecnico_responsavel_id que não existem na tabela usuarios.</p>";
        }
        
        // Passo 5: Mostrar estatísticas finais
        echo "<h3>5. Estatísticas finais...</h3>";
        
        $stmt = $pdo_mci->prepare("
            SELECT 
                COUNT(*) as total_registros,
                COUNT(funcionario_id) as funcionarios_mapeados,
                COUNT(tecnico_responsavel_id) as tecnicos_mapeados
            FROM cad_registros
        ");
        $stmt->execute();
        $stats = $stmt->fetch();
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Métrica</th><th>Valor</th><th>Percentual</th></tr>";
        echo "<tr>";
        echo "<td>Total de registros</td>";
        echo "<td><strong>{$stats['total_registros']}</strong></td>";
        echo "<td>100%</td>";
        echo "</tr>";
        echo "<tr>";
        echo "<td>Funcionários mapeados</td>";
        echo "<td><strong>{$stats['funcionarios_mapeados']}</strong></td>";
        echo "<td>" . round(($stats['funcionarios_mapeados'] / $stats['total_registros']) * 100, 1) . "%</td>";
        echo "</tr>";
        echo "<tr>";
        echo "<td>Técnicos mapeados</td>";
        echo "<td><strong>{$stats['tecnicos_mapeados']}</strong></td>";
        echo "<td>" . round(($stats['tecnicos_mapeados'] / $stats['total_registros']) * 100, 1) . "%</td>";
        echo "</tr>";
        echo "</table>";
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724;'>🎉 Relacionamento Criado com Sucesso!</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ Colunas funcionario_id e tecnico_responsavel_id criadas</li>";
        echo "<li>✅ Mapeamento automático de nomes para IDs realizado</li>";
        echo "<li>✅ Chaves estrangeiras configuradas (quando possível)</li>";
        echo "<li>✅ Dados originais preservados</li>";
        echo "</ul>";
        echo "<p style='color: #155724;'><strong>Próximo passo:</strong> Atualizar a interface para usar os novos campos.</p>";
        echo "</div>";
        
    } else {
        // Mostrar formulário de confirmação
        echo "<h3>4. Proposta de Relacionamento</h3>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px;'>";
        echo "<h4>🔧 Esta operação irá:</h4>";
        echo "<ul>";
        echo "<li>Criar colunas <code>funcionario_id</code> e <code>tecnico_responsavel_id</code> (INT)</li>";
        echo "<li>Tentar mapear nomes existentes para IDs de usuários</li>";
        echo "<li>Configurar chaves estrangeiras para sicoob_access_control.usuarios</li>";
        echo "<li>Preservar os dados originais nas colunas de texto</li>";
        echo "<li>Permitir seleção de usuários na interface</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ Importante:</h4>";
        echo "<ul>";
        echo "<li>Os dados originais serão preservados</li>";
        echo "<li>Apenas nomes que correspondem exatamente a usuários serão mapeados</li>";
        echo "<li>Registros não mapeados continuarão funcionando</li>";
        echo "<li>A interface será atualizada para usar dropdowns de usuários</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<div style='margin: 20px 0;'>";
        echo "<label>";
        echo "<input type='checkbox' name='executar_relacionamento' required> ";
        echo "<strong>Eu confirmo que desejo criar o relacionamento com a tabela de usuários</strong>";
        echo "</label>";
        echo "</div>";
        
        echo "<button type='submit' style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold;'>🔗 CRIAR RELACIONAMENTO</button>";
        echo " ";
        echo "<a href='gerenciar.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>❌ Cancelar</a>";
        echo "</form>";
    }
    
    echo "<hr>";
    echo "<h3>Estrutura Proposta</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h4>📋 Colunas após o relacionamento:</h4>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Coluna Atual</th><th>Nova Coluna</th><th>Tipo</th><th>Relacionamento</th></tr>";
    echo "<tr>";
    echo "<td>funcionario (VARCHAR)</td>";
    echo "<td>funcionario_id (INT)</td>";
    echo "<td>Chave Estrangeira</td>";
    echo "<td>sicoob_access_control.usuarios.id</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>tecnico_responsavel (VARCHAR)</td>";
    echo "<td>tecnico_responsavel_id (INT)</td>";
    echo "<td>Chave Estrangeira</td>";
    echo "<td>sicoob_access_control.usuarios.id</td>";
    echo "</tr>";
    echo "</table>";
    echo "<p><strong>Benefícios:</strong></p>";
    echo "<ul>";
    echo "<li>Integridade referencial garantida</li>";
    echo "<li>Interface com dropdowns de usuários</li>";
    echo "<li>Facilita relatórios e filtros</li>";
    echo "<li>Evita erros de digitação</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='gerenciar.php'>Voltar ao gerenciamento</a> | <a href='index.php'>Voltar ao sistema</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a análise</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
