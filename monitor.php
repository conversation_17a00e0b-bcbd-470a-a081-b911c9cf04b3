<?php
require_once 'auth_check.php';

header('Content-Type: application/json');

try {
    // Buscar estatísticas em tempo real
    $stats = [];
    
    // Total de registros
    $stmt = $pdo_mci->prepare("SELECT COUNT(*) as total FROM cad_registros");
    $stmt->execute();
    $stats['total_registros'] = $stmt->fetchColumn();
    
    // Registros por status
    $stmt = $pdo_mci->prepare("
        SELECT status, COUNT(*) as quantidade 
        FROM cad_registros 
        GROUP BY status
    ");
    $stmt->execute();
    $stats['por_status'] = $stmt->fetchAll();
    
    // Última importação
    $stmt = $pdo_mci->prepare("
        SELECT * FROM cad_importacoes 
        ORDER BY data_importacao DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $stats['ultima_importacao'] = $stmt->fetch();
    
    // Registros hoje
    $stmt = $pdo_mci->prepare("
        SELECT COUNT(*) as total 
        FROM cad_registros 
        WHERE DATE(data_cadastro) = CURDATE()
    ");
    $stmt->execute();
    $stats['registros_hoje'] = $stmt->fetchColumn();
    
    // Últimos 5 registros
    $stmt = $pdo_mci->prepare("
        SELECT id, pa, nome_cliente, status, data_cadastro 
        FROM cad_registros 
        ORDER BY data_cadastro DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $stats['ultimos_registros'] = $stmt->fetchAll();
    
    // Erros recentes
    $stmt = $pdo_mci->prepare("
        SELECT COUNT(*) as total 
        FROM cad_erros_importacao 
        WHERE DATE(data_erro) = CURDATE()
    ");
    $stmt->execute();
    $stats['erros_hoje'] = $stmt->fetchColumn();
    
    // Status da última importação em andamento
    $stmt = $pdo_mci->prepare("
        SELECT * FROM cad_importacoes 
        WHERE status = 'processando' 
        ORDER BY data_importacao DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $stats['importacao_em_andamento'] = $stmt->fetch();
    
    $stats['timestamp'] = date('Y-m-d H:i:s');
    $stats['success'] = true;
    
    echo json_encode($stats);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
